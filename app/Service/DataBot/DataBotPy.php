<?php

namespace App\Service\DataBot;


use App\Constant\Environment;
use App\Exception\AppException;
use Common\EnvConfig;
use http\Env;
use Monolog\Logger;
use RedisException;
use Swoole\FastCGI\Record\Data;

/**
 * 调用Python的一些操作
 */
class DataBotPy
{

    /**
     * 文件保存路径
     */
    const PLOTTING_DIR = SRV_DIR . '/data_bot';

    /**
     * 处理画图（画表格）
     *
     * @param mixed $channel_type 推送渠道，飞书或者微信
     * @param $table_list
     * @param $user_id
     * @param $logger
     * @return string
     * @throws RedisException
     */
    static public function plotting($channel_type, $table_list, $user_id, $logger)
    {
        // 先把数据写入到一个 临时 json 文件
        $json_data = json_encode($table_list, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);

        // 将 JSON 数据写入文件 文件名得唯一，防止并发
        $filename_prefix = self::PLOTTING_DIR . '/' . uniqid() . "_" . $user_id;
        file_put_contents($filename_prefix . '.json', $json_data);

        // 把文件名存入session里面
        DataBotSession::addSessionFile($channel_type, $user_id, $filename_prefix . '.json');

        $python_path = SCRIPT_DIR . "/py/plotting.py";

        $python_bin_path = EnvConfig::PY_BIN_PATH;

        $command = "$python_bin_path {$python_path} {$filename_prefix}";
        $data = exec($command, $out_array, $result_code);
        // 执行异常的任务跳过
        if ($result_code !== 0) {
            $logger->error("画图脚本命令执行异常。", ['out_array' => $out_array, 'command' => $command]);
            throw new AppException('画图脚本命令执行异常。');
        }
        $data = json_decode($data, true);
        if ($data['code'] !== 0) {
            $logger->error("画图脚本执行失败。错误信息：{$data['message']}");
            throw new AppException("画图脚本执行失败。错误信息：{$data['message']}");
        }

        return $filename_prefix . '.png';
    }


    /**
     * 处理数据分析
     *
     * @param $user_query
     * @param mixed $channel_type 推送渠道，飞书或者微信
     * @param $table_list
     * @param $user_id
     * @param Logger $logger
     * @return array
     * @throws RedisException
     */
    static public function analyse($user_query, $channel_type, $table_list, $user_id, Logger $logger)
    {
        $user_query .= " 保持原始时间顺序，不要对数据排序。自然语言部分请用中文";
        // 有数据的情况下才需要替换数据源
        if ($table_list) {
            // 先把数据写入到一个 临时 json 文件
            $json_data = json_encode($table_list, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);

            // 将 JSON 数据写入文件 文件名得唯一，防止并发
            $filename_prefix = self::PLOTTING_DIR . '/' . uniqid() . "_" . $user_id;
            $data_filename = $filename_prefix . '.json';
            file_put_contents($data_filename, $json_data);


            $logger->info('数据文件写入成功', ['filename' => $data_filename]);
            // 把文件名存入session里面
            DataBotSession::addSessionFile($channel_type, $user_id, $data_filename);
        } else {
            $data_filename = DataBotSession::getSessionFilename($channel_type, $user_id);
            $logger->info('获取到session的filename', ['filename' => $data_filename]);
        }

        if (!$data_filename) {
            throw new AppException('分析的数据不存在，请查询后再做分析');
        }

        $session_list = DataBotSession::getPandasAIHistory($channel_type, $user_id);
        $format_session_history = DataBotSession::formatPandasAISessionList($session_list);

        $content_data = [
            'content'              => $user_query,
            'history_message_list' => $format_session_history,
        ];

        // 会话的内容可以固定存一份文件
        $session_filename = self::PLOTTING_DIR . '/session_list_' . $user_id . '.json';
        file_put_contents($session_filename, json_encode($content_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));

        $logger->info('PandasAI提问内容', $content_data);


        if (EnvConfig::ENV === Environment::PROD) {
            $python_bin_path = "/data/www/myenv/bin/python3";
        } else {
            $python_bin_path = "/var/www/myenv/bin/python3";
        }
        $python_path = SCRIPT_DIR . "/py/pandasAI.py";

        $command = "$python_bin_path {$python_path} {$data_filename} {$session_filename}";
        $data = exec($command, $out_array, $result_code);
        // 执行异常的任务跳过
        if ($result_code !== 0) {
            $logger->error("PandasAI命令执行异常。。", ['out_array' => $out_array, 'command' => $command]);
            throw new AppException('PandasAI命令执行异常。');
        }
        $data = json_decode($data, true);
        if ($data['code'] !== 0) {
            $logger->error("PandasAI命令执行失败。错误信息：{$data['message']}", ['command' => $command]);
            throw new AppException("PandasAI命令执行失败。错误信息：{$data['message']}");
        }

        $logger->info('PandasAI返回数据', $data);

        return self::handlerPandasRespond($data, $channel_type, $user_id, $user_query, $command, $logger);
//        // 字符串的话判断前缀图片路径，这个路径是写死的
//        // 判断返回的是不是画图的路径，如果是，则要发送图片。否则直接发送字符串

//        $prefix = SCRIPT_DIR . '/py/../../srv/data_bot/pandasai_img';
//        $error_prefix = "Unfortunately, I was not able to answer your question";
//        if ($data['data']['type'] === 'string') {
//            if (strpos($data['data']['value'], $prefix) === 0) {
//                $ret_value = [
//                    'type'  => 'img',
//                    'value' => $data['data']['value'],
//                ];
//                DataBotSession::addPandasAIHistory($channel_type, $user_id, $user_query, "上次回答生成了一张图片，图片路径：" . $data['data']['value']);
//                DataBotSession::addAllConversation($channel_type, $user_id, $user_query, "[画图]");
//            } else if (strpos($data['data']['value'], $error_prefix) === 0) {
//                $ret_value = [
//                    'type'  => 'error',
//                    'value' => "PandasAI画图出错，请重试。",
//                ];
//            } else {
//                $ret_value = [
//                    'type'  => 'str',
//                    'value' => $data['data']['value'],
//                ];
//                // 消息内容写进历史记录
//                DataBotSession::addPandasAIHistory($channel_type, $user_id, $user_query, $data['data']['value']);
//                DataBotSession::addAllConversation($channel_type, $user_id, $user_query, $data['data']['value']);
//            }
//        } elseif ($data['data']['type'] === 'dataframe') {
//            $ret_value = [
//                'type'  => 'json',
//                'value' => json_decode($data['data']['value']),
//            ];
//            // 非图片的情况，要把消息内容写进历史记录
//            DataBotSession::addPandasAIHistory($channel_type, $user_id, $user_query, "上次回答生成了一段json数据：\n```{$data['data']['value']}```");
//            DataBotSession::addAllConversation($channel_type, $user_id, $user_query, "上次回答生成了一段json数据：\n```{$data['data']['value']}```");
//        } else {
//            $ret_value = [
//                'type'  => 'other',
//                'value' => $data['data']['value'],
//            ];
//            // 把消息内容写进历史记录
//            DataBotSession::addPandasAIHistory($channel_type, $user_id, $user_query, $data['data']['value']);
//            DataBotSession::addAllConversation($channel_type, $user_id, $user_query, $data['data']['value']);
//        }
//
//        return $ret_value;
    }


    /**
     * 处理pandasAI返回的数据
     * 这里处理的是3.0
     *
     * @param $data
     * @param $channel_type
     * @param $user_id
     * @param $user_query
     * @param $command
     * @param $logger
     * @return array|string[]
     * @throws RedisException
     */
    static public function handlerPandasRespond($data, $channel_type, $user_id, $user_query, $command, $logger)
    {
        // 错误信息
        if ($data['data']['type'] === 'error') {
            $ret_value = [
                'type'  => 'error',
                'value' => "PandasAI画图出错，请重试。",
            ];

            $logger->info('PandasAI画图出错', ['command' => $command]);
            return $ret_value;
        }

        // 初始化
        $ret_value = [
            'type'  => 'error',
            'value' => 'pandasAI发送未知错误，请重试。'
        ];

        // pandasAI的对话内容也添加到参数提取那边去。只不过没有AI的回复
        DataBotSession::addParamSessionByUser($channel_type, $user_id, $user_query);

        // 图片
        if ($data['data']['type'] === 'plot') {
            $ret_value = [
                'type'  => 'img',
                'value' => $data['data']['value'],
            ];
            DataBotSession::addPandasAIHistory($channel_type, $user_id, $user_query, "上次回答生成了一张图片，图片路径：" . $data['data']['value']);
            DataBotSession::addAllConversationHistory($channel_type, $user_id, $user_query, "[画图]");

        } elseif (($data['data']['type'] === 'string')) {
            $ret_value = [
                'type'  => 'str',
                'value' => $data['data']['value'],
            ];
            // 消息内容写进历史记录
            DataBotSession::addPandasAIHistory($channel_type, $user_id, $user_query, $data['data']['value']);
            DataBotSession::addAllConversationHistory($channel_type, $user_id, $user_query, $data['data']['value']);
        } elseif ($data['data']['type'] === 'dataframe') {
            $ret_value = [
                'type'  => 'json',
                'value' => json_decode($data['data']['value']),
            ];
            // 非图片的情况，要把消息内容写进历史记录
            DataBotSession::addPandasAIHistory($channel_type, $user_id, $user_query, "上次回答生成了一段json数据：\n```{$data['data']['value']}```");
            DataBotSession::addAllConversationHistory($channel_type, $user_id, $user_query, "上次回答生成了一段json数据：\n```{$data['data']['value']}```");
        }

        return $ret_value;
    }

    /**
     * 返回文件名
     *
     * @param $user_id
     * @param $json_data
     * @return string
     */
    public function saveDataFile($user_id, $json_data)
    {
        // 将 JSON 数据写入文件 文件名得唯一，防止并发
        $filename_prefix = self::PLOTTING_DIR . '/' . uniqid() . "_" . $user_id;
        file_put_contents($filename_prefix . '.json', $json_data);

        // 写入会话PandasAI会话信息

        return $filename_prefix . '.json';
    }

}