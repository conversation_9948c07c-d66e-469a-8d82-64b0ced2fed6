<?php

namespace App\Service\MediaAD;

use App\Constant\AgentGroup;
use App\Constant\BatchAD;
use App\Constant\MediaType;
use App\Constant\PlatId;
use App\Exception\AppException;
use App\Logic\DSP\ADTaskDigestLogic;
use App\Model\HttpModel\Bilibili\Campaign\CampaignModel;
use App\Model\HttpModel\Bilibili\Creative\CreativeModel;
use App\Model\HttpModel\Bilibili\MetaData\MetaDataModel;
use App\Model\HttpModel\Bilibili\Unit\UnitModel;
use App\Model\SqlModel\DataMedia\OdsMediaSDKModel;
use App\Model\SqlModel\Zeda\ADTaskModel;
use App\Model\SqlModel\Zeda\CampaignCreateMediaLogModel;
use App\Model\SqlModel\Zeda\MaterialFileModel;
use App\Model\SqlModel\Zeda\MaterialUploadMediaTaskModel;
use App\Model\SqlModel\Zeda\MediaAccountModel;
use App\Param\ADLabServing\ADLabTaskParam;
use App\Param\ADServing\ADTaskParam;
use App\Param\ADServing\ADTaskUpdateParam;
use App\Param\ADServing\BiliBili\ADTargetingContentParam;
use App\Param\ADServing\BiliBili\Basics\ADOtherSettingContentParam;
use App\Param\ADServing\BiliBili\Basics\ADSettingContentParam;
use App\Param\BiliBili\CampaignParam;
use App\Param\BiliBili\CreativeParam;
use App\Param\BiliBili\UnitParam;
use App\Param\ChannelPackageParam;
use App\Param\ConvertCreateParam;
use App\Param\Material\MaterialFileParam;
use App\Param\Material\MaterialUploadMediaTaskParam;
use App\Param\MediaAccountInfoParam;
use App\Service\ScriptMsgService;
use App\Struct\Input;
use App\Utils\Helpers;
use Common\EnvConfig;
use Illuminate\Support\Collection;
use Throwable;

class MediaBilibili extends AbstractMedia
{

    /**
     * @param Input $input
     * @return mixed
     */
    public function getInterestActionCategoryList(Input $input)
    {
        // TODO: Implement getInterestActionCategoryList() method.
    }

    /**
     * @param Input $input
     * @return mixed
     */
    public function getInterestActionKeywordList(Input $input)
    {
        // TODO: Implement getInterestActionKeywordList() method.
    }

    /**
     * @param Input $input
     * @return mixed
     */
    public function getInterestInterestCategoryList(Input $input)
    {
        // TODO: Implement getInterestInterestCategoryList() method.
    }

    /**
     * @param Input $input
     * @return mixed
     */
    public function getInterestInterestKeywordList(Input $input)
    {
        // TODO: Implement getInterestInterestKeywordList() method.
    }

    /**
     * @param Input $input
     * @return array
     */
    public function campaignList(Input $input)
    {
        // TODO: Implement campaignList() method.
    }

    /**
     * @param ADTaskParam $param
     * @return mixed
     */
    public function isCreateConvert(ADTaskParam $param)
    {
        // TODO: Implement isCreateConvert() method.
    }

    /**
     * @param Input $input
     * @return array
     */
    public function convertList(Input $input)
    {
        // TODO: Implement convertList() method.
    }

    /**
     * @param ConvertCreateParam $param
     * @return array
     */
    public function createConvert(ConvertCreateParam $param)
    {
        // TODO: Implement createConvert() method.
    }

    /**
     * @param ChannelPackageParam $param
     * @return array
     */
    public function createChannelPackage(ChannelPackageParam $param)
    {
        // TODO: Implement createChannelPackage() method.
    }

    /**
     * @param Input $input
     * @return array
     */
    public function audiencePackageList(Input $input)
    {
        // TODO: Implement audiencePackageList() method.
    }

    /**
     * @param ADLabTaskParam $param
     * @param string $account_id
     * @param string $access_token
     * @return array
     */
    public function updateAd(ADLabTaskParam $param, string $account_id, string $access_token)
    {
        // TODO: Implement updateAd() method.
    }

    /**
     * @param ADLabTaskParam $param
     * @param string $account_id
     * @param string $access_token
     * @return array
     */
    public function updateADStatus(ADLabTaskParam $param, string $account_id, string $access_token)
    {
        // TODO: Implement updateADStatus() method.
    }

    /**
     * @param ADLabTaskParam $param
     * @param string $account_id
     * @param string $access_token
     * @return array
     */
    public function updateCreativeStatus(ADLabTaskParam $param, string $account_id, string $access_token)
    {
        // TODO: Implement updateCreativeStatus() method.
    }

    /**
     * @param ADLabTaskParam $param
     * @param string $account_id
     * @param string $access_token
     * @return array
     */
    public function createAudience(ADLabTaskParam $param, string $account_id, string $access_token)
    {
        // TODO: Implement createAudience() method.
    }

    /**
     * @param ADLabTaskParam $param
     * @param string $account_id
     * @param string $access_token
     * @return array
     */
    public function expandAudience(ADLabTaskParam $param, string $account_id, string $access_token)
    {
        // TODO: Implement expandAudience() method.
    }

    /**
     * @param MaterialFileParam $param
     * @param $advertiser_id
     * @param $access_token
     * @return array
     */
    public function uploadImage(MaterialFileParam $param, $advertiser_id, $access_token)
    {
        try {
            $account_info = (new MediaAccountModel())->getDataByAccountId($advertiser_id, MediaType::BILIBILI);
            if (!$account_info) {
                throw new AppException('找不到账号：' . $advertiser_id);
            }
            $model = new MetaDataModel();

            if ($param->file_type == MaterialFileModel::FILE_TYPE_IMAGE) {
                $img_path = EnvConfig::MATERIAL_IMG_DIR_NAME;
            } elseif ($param->file_type == MaterialFileModel::FILE_TYPE_ICON) {
                $img_path = EnvConfig::MATERIAL_ICON_DIR_NAME;
            } else {
                $img_path = EnvConfig::MATERIAL_VIDEO_DIR_NAME;
            }

            $access_path = EnvConfig::UPLOAD_PATH;
            $upload_path = SRV_DIR . "/{$access_path}";
            $platform = $param->platform;
            $material_id = $param->material_id;
            $file_name = $param->filename;
            $file_path = "$upload_path/$img_path/$platform/$material_id/$file_name";
            $result = $model->uploadImage($file_path, $advertiser_id, $account_info->majordomo_name, $account_info->access_token);

            return [
                'id' => $result['id'],
                'url' => $result['url']
            ];
        } catch (Throwable $e) {
            throw new AppException("{$param->filename}上传出错" . $e->getMessage());
        }
    }

    /**
     * @param MaterialFileParam $param
     * @param $advertiser_id
     * @param $access_token
     * @return array
     */
    public function uploadVideo(MaterialFileParam $param, $advertiser_id, $access_token)
    {
        try {
            $account_info = (new MediaAccountModel())->getDataByAccountId($advertiser_id, MediaType::BILIBILI);
            if (!$account_info) {
                throw new AppException('找不到账号：' . $advertiser_id);
            }
            $model = new MetaDataModel();

            $video_path = EnvConfig::MATERIAL_VIDEO_DIR_NAME;
            $access_path = EnvConfig::UPLOAD_PATH;
            $upload_path = SRV_DIR . "/{$access_path}";
            $platform = $param->platform;
            $material_id = $param->material_id;
            $file_name = $param->filename;
            $file_path = "$upload_path/$video_path/$platform/$material_id/$file_name";

            // 获取预上传链接
            $signed_result = $model->getSignedUrl($param->signature, $advertiser_id, $account_info->majordomo_name, $account_info->access_token);
            $signed_url = $signed_result['archive_signed_urls'][0]['signed_url'];
            // 推送视频
            $model->putVideo($signed_url, $file_path);

            // 创建稿件
            $archive_details = [
                [
                    'title' => $param->filename,
                    'video_file_name' => $param->filename,
                    'video_md5' => $param->signature,
                ]
            ];
            $result = $model->createArchives($archive_details, $advertiser_id, $account_info->majordomo_name, $account_info->access_token);

            return [
                'id' => $result['archive_infos'][0]['process_id'], // 稿件处理id 这里拿不到素材id
                'url' => ''
            ];
        } catch (Throwable $e) {
            if (strpos($e->getMessage(), '请求被限流，请稍后再试') !== false) {
                throw new AppException("{$param->filename}上传出错" . $e->getMessage(), 2466474);
            } else {
                throw new AppException("{$param->filename}上传出错" . $e->getMessage());
            }
        }
    }

    /**
     * @param Input $input
     * @return array
     */
    public function getIndustryList(Input $input)
    {
        // TODO: Implement getIndustryList() method.
    }

    /**
     * @param array $input
     * @param $media_type
     * @return mixed
     */
    public function getWordById($input, $media_type)
    {
        // TODO: Implement getWordById() method.
    }

    /**
     * @param array $ids
     * @param int $status
     * @return mixed
     */
    public function getAudienceListByIds(array $ids, int $status = 2)
    {
        // TODO: Implement getAudienceListByIds() method.
    }

    /**
     * @param array $audience_account_id_list
     * @param array $need_push_audience_list_data
     * @param array $target_account_ids
     * @return array
     */
    public function pushAudience(array $audience_account_id_list, array $need_push_audience_list_data, array $target_account_ids)
    {
        // TODO: Implement pushAudience() method.
    }

    /**
     * @param $company
     * @param $page
     * @param $rows
     * @param string $id
     * @param string $name
     * @param string $tag
     * @param string $source
     * @param string $account_id
     * @param array $extra
     * @return mixed
     */
    public function audienceList($company, $page, $rows, $id = '', $name = '', $tag = '', $source = '', $account_id = '', $extra = [])
    {
        // TODO: Implement audienceList() method.
    }

    /**
     * @param string $name
     * @param string $account_id
     * @param int $page
     * @param int $row
     * @return mixed
     */
    public function flowList($name = '', $account_id = '', $page = 1, $row = 15)
    {
        // TODO: Implement flowList() method.
    }

    /**
     * @param $audience_list
     * @param $account_id
     * @param $platform
     * @param $company
     * @return Collection
     */
    public function getNeedUploadAudienceList($audience_list, $account_id, $platform, $company)
    {
        // TODO: Implement getNeedUploadAudienceList() method.
    }

    /**
     * @param $company
     * @param $audience_md5
     * @return mixed
     */
    public function getTargetingDataByAD2($company, $audience_md5)
    {
        // TODO: Implement getTargetingDataByAD2() method.
    }

    /**
     * @param array $audience_info
     * @return String
     */
    public function getTargetingName(array $audience_info)
    {
        // TODO: Implement getTargetingName() method.
    }

    /**
     * @param array $targeting_info
     * @param bool $is_return
     * @return array
     */
    public function fillTargetingInfo(array $targeting_info, $is_return = false)
    {
        // TODO: Implement fillTargetingInfo() method.
    }

    /**
     * @param array $condition
     * @return array
     */
    public function getAdActionList(array $condition)
    {
        // TODO: Implement getAdActionList() method.
    }

    /**
     * @param array $data
     * @return array
     */
    public function getActionWord(array $data)
    {
        // TODO: Implement getActionWord() method.
    }

    /**
     * @param array $condition
     * @return array
     */
    public function getAdInterestList(array $condition)
    {
        // TODO: Implement getAdInterestList() method.
    }

    /**
     * @param array $data
     * @return array
     */
    public function getInterestWord(array $data)
    {
        // TODO: Implement getInterestWord() method.
    }

    /**
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     * @return mixed
     */
    public function updateADAnalysisStatus(int $media_type, array $data, array $ad_format_target)
    {
        // TODO: Implement updateADAnalysisStatus() method.
    }

    /**
     * @param array $data
     * @return mixed
     */
    public function updateADAnalysisFirstClass(array $data)
    {
        // TODO: Implement updateADAnalysisFirstClass() method.
    }

    /**
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     * @return mixed
     */
    public function updateADAnalysisSecondClass(int $media_type, array $data, array $ad_format_target)
    {
        // TODO: Implement updateADAnalysisSecondClass() method.
    }

    /**
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     * @param string $update_field
     * @return mixed
     */
    public function updateADAnalysisBudgetOrBid(int $media_type, array $data, array $ad_format_target, string $update_field)
    {
        // TODO: Implement updateADAnalysisBudgetOrBid() method.
    }

    /**
     * @param int $media_type
     * @param array $data
     * @return mixed
     */
    public function handleADAnalysisBudgetOrBidMQData(int $media_type, array $data)
    {
        // TODO: Implement handleADAnalysisBudgetOrBidMQData() method.
    }

    /**
     * @param array $data
     * @param Collection $account_info
     * @return mixed
     */
    public function updateADAnalysisTargeting(array $data, Collection $account_info)
    {
        // TODO: Implement updateADAnalysisTargeting() method.
    }

    /**
     * @param array $input
     * @return int
     */
    public function addADTaskByApi(array $input)
    {
        // TODO: Implement addADTaskByApi() method.
    }

    /**
     * @param array $input
     * @return mixed
     */
    public function updateADTaskByApi(array $input)
    {
        // TODO: Implement updateADTaskByApi() method.
    }

    /**
     * @param array $input
     * @return mixed
     */
    public function updateAndroidChannelPackage(array $input)
    {
        // TODO: Implement updateAndroidChannelPackage() method.
    }

    /**
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     * @param string $update_field
     * @return mixed
     */
    public function updateADAnalysisDeepBidOrROI(int $media_type, array $data, array $ad_format_target, string $update_field)
    {
        // TODO: Implement updateADAnalysisDeepBidOrROI() method.
    }

    /**
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     * @return mixed
     */
    public function deleteADAnalysisAD(int $media_type, array $data, array $ad_format_target)
    {
        // TODO: Implement deleteADAnalysisAD() method.
    }

    /**
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     * @param string $update_field
     * @return mixed
     */
    public function updateADAnalysisFirstClassBudget(int $media_type, array $data, array $ad_format_target, string $update_field)
    {
        // TODO: Implement updateADAnalysisFirstClassBudget() method.
    }

    /**
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     * @param string $update_field
     * @return mixed
     */
    public function updateADAnalysisAccountBudget(int $media_type, array $data, array $ad_format_target, string $update_field)
    {
        // TODO: Implement updateADAnalysisAccountBudget() method.
    }

    /**
     * @param array $input
     * @return mixed
     */
    function bindADAnalysisADRTA(array $input)
    {
        // TODO: Implement bindADAnalysisADRTA() method.
    }

    /**
     * @param array $data
     * @param array $input
     * @return mixed
     */
    function unbindADAnalysisADRTA(array $data, array $input)
    {
        // TODO: Implement unbindADAnalysisADRTA() method.
    }

    /**
     * @param array $target
     * @param $account_info
     * @return mixed
     */
    function getTargetAudienceEstimate(array $target, $account_info)
    {
        // TODO: Implement getTargetAudienceEstimate() method.
    }

    /**
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @return mixed
     */
    function beforeMakeSite(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        // TODO: Implement beforeMakeSite() method.
    }

    /**
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @return mixed
     */
    function afterMakeSite(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        // TODO: Implement afterMakeSite() method.
    }

    function getAD1IdByParam(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        $campaign_info = (new CampaignCreateMediaLogModel())->getCreateLog(MediaType::BILIBILI, $account_param->account_id, $param->ad1_name_text);
        return $campaign_info ? $campaign_info->campaign_id : 0;
    }

    function createAD1(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        /* @var ADSettingContentParam $setting */
        $setting = $param->setting;

        /* @var ADOtherSettingContentParam $other_setting */
        $other_setting = $param->other_setting;

        $campaign_param = new CampaignParam([
            'campaign_name' => $param->ad1_name_text,
            'promotion_purpose_type' => $other_setting->promotion_content_type == 6 ? 4 : 2,
            'ad_type' => 0,
            'support_auto' => $setting->support_auto,
        ]);

        if ($setting->campaign_budget_limit_type == 'custom') {
            $campaign_param->setBudget(1, $setting->campaign_budget);
        } else {
            $campaign_param->setBudget(2, 0);
        }

        $result = (new CampaignModel())->create($campaign_param, $account_param->account_id, $account_param->majordomo_name, $account_param->access_token);

        $campaign_id = $result['campaign_id'];
        (new CampaignCreateMediaLogModel())->addCreateLog(MediaType::BILIBILI, $param->account_id, $campaign_id, $param->ad1_name_text);

        return $campaign_id;
    }

    public static function formatBaiduScheduleFromBinaryToJson($binary_schedule_time)
    {
        $result = [];
        //按照每24个元素，把二进制分成7个数组，每个数组24个元素代表一天24小时
        foreach (str_split($binary_schedule_time, 24) as $day => $hours) {
            //遍历24小时
            $hours_arr = str_split($hours);

            $day_hours = [];

            foreach ($hours_arr as $k => $v) {
                if (1 === (int)$v) {
                    $day_hours[] = $k;
                }
            }
            $result[] = $day_hours;
        }

        return $result;
    }

    function createAD2(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        /* @var ADSettingContentParam $setting */
        $setting = $param->setting;

        /* @var ADOtherSettingContentParam $other_setting */
        $other_setting = $param->other_setting;

        /* @var ADTargetingContentParam $setting */
        $targeting = $param->targeting;

        $unit_param = new UnitParam([
            'campaign_id' => $param->ad1_id,
            'unit_name' => mb_substr($param->ad2_name_text, 0, 64, "UTF-8"),
            'promotion_content_type' => $other_setting->promotion_content_type,
            'launch_begin_date' => strtotime($setting->launch_begin_date) > strtotime(date('Y-m-d')) ? $setting->launch_begin_date : date('Y-m-d'),
            'launch_end_date' => strtotime($setting->launch_end_date) > strtotime(date('Y-m-d')) ? $setting->launch_end_date : date('Y-m-d', strtotime("+30 days")),
            'launch_time' => $this->formatBaiduScheduleFromBinaryToJson(implode('', $setting->launch_time)),
            'budget' => $setting->unit_budget,
            'speed_mode' => $setting->speed_mode,
        ]);



        if ($param->getSiteConfig()->game_type == '安卓') {
            $unit_param->setGame(
                (int)$param->getSiteConfig()->appid,
                (int)$param->getSiteConfig()->ext['sub_pkg']
            );
        }

        $targeting_data = $targeting->toParam();
        $unit_param->setTarget(
            array_merge(
                $targeting_data,
                [
                    'installed_user_filter' => $setting->installed_user_filter,
                    'converted_user_filter' => $setting->converted_user_filter
                ]
            ),
            $setting->support_auto
        );

        $unit_param->setSalesMode([
            'is_no_bid' => $setting->is_no_bid,
            'base_target' => $setting->base_target,
            'cpa_target' => (int)$param->getSiteConfig()->convert_type,
            'cpa_bid' => $setting->getCpaBid(),
            'deep_cpa_target' => (int)($param->getSiteConfig()->deep_external_action ?: false),
            'dual_bid_two_stage_optimization' => $setting->dual_bid_two_stage_optimization,
            'deep_cpa_bid' => $setting->getDeepCpaBid(),
        ]);

        $result = (new UnitModel())->create($unit_param, $account_param->account_id, $account_param->majordomo_name, $account_param->access_token);

        $unit_id = $result['unit_id'];

        return $unit_id;
    }

    function createAD3(ADTaskParam $param, MediaAccountInfoParam $account_param, $material_media_id_map)
    {

        /* @var ADSettingContentParam $setting */
        $setting = $param->setting;

        /* @var ADOtherSettingContentParam $other_setting */
        $other_setting = $param->other_setting;

        $creative_param = new CreativeParam();
        $creative_param->setUnit([
            'campaign_id' => (int)$param->ad1_id,
            'unit_id' => (int)$param->ad2_id,
            'is_bili_native' => 0,
            'channel_id' => $setting->channel_id,
            'scene_id_list' => $other_setting->scene_id_list,
            'is_programmatic' => $param->creative_mode == BatchAD::CREATIVE_PROGRAM_MODE ? 1 : 0,
            'tag_list' => $setting->tag_list,
            'is_smart_material' => 0,
            'monitor_list' => [
                [
                    'type' => 2,
                    'url' => $param->getSiteConfig()->action_track_url,
                ],
            ],
            'business_category' => $setting->getBusinessCategory(),
        ]);

        if ($param->creative_mode == BatchAD::CREATIVE_PROGRAM_MODE) {
            $creative_data = [
                'template_group_id' => $other_setting->template_group_id,
                'creative_name' => sprintf("%s_创意_%s", $param->ad2_id, date('Y-m-d H:i:s')),
                'description' => $other_setting->description,
                'title_list' => array_map(function ($word) {
                    return [
                        'title' => $word,
                    ];
                }, $param->getWordList()),
                'ad_space_id' => $other_setting->getADSpaceId($account_param->account_id)
            ];

            if ($other_setting->template_group_id == 1) {
                $creative_data['image_list'] = [];
                foreach ($param->creative_list as $creative_object) {
                    $creative_data['image_list'][] = [
                        'material_id' => "{$material_media_id_map[$creative_object['image_info']['id']]['id']}"
                    ];
                }
            }

            if ($other_setting->template_group_id == 4) {
                $creative_data['archive_list'] = [];
                foreach ($param->creative_list as $creative_object) {
                    $creative_data['archive_list'][] = [
                        'source' => 1,
                        'avid' => "{$material_media_id_map[$creative_object['video_info']['id']]['id']}",
                        'cover' => [
                            'material_id' => $material_media_id_map[$creative_object['cover_info']['id']]['id'],
                            'is_use_smart_cut' => 0
                        ]
                    ];
                }
            }

            if ($param->getSiteConfig()->game_type == '安卓') {
                if ($param->getSiteConfig()->plat_id == PlatId::MINI) {
                    $creative_data['landing_page'] = [
                        'mgk_page_id' => $other_setting->getLandingPage($account_param->account_id),
                    ];
                } else {
                    $creative_data['landing_page'] = [
                        'url' => "bilibili://game_center/detail?id=" . (int)$param->getSiteConfig()->appid . "&sourceType=adPut"
                    ];
                }
            }

            if ($param->getSiteConfig()->plat_id == PlatId::MINI) {
                $mini_game_info = $other_setting->getMiniGame($account_param->account_id);
                $creative_data['mini_game'] = [
                    'game_id' => $mini_game_info['id'] ?? '',
                    'url' => $mini_game_info['url'] ?? '',
                ];
            }

            $creative_param->creative_list[] = $creative_data;
            $result = (new CreativeModel())->save($creative_param, $account_param->account_id, $account_param->majordomo_name, $account_param->access_token);
            return $result['creative_id_list'];
        } else {
            foreach ($param->creative_list as $creative_object) {

                $creative_data = [
                    'template_group_id' => $other_setting->template_group_id,
                    'creative_name' => sprintf("%s_创意_%s", $param->ad2_id, date('Y-m-d:H:i:s')),
                    'description' => $other_setting->description,
                    'ad_space_id' => $other_setting->getADSpaceId($account_param->account_id)
                ];

                if ($param->getSiteConfig()->game_type == '安卓') {
                    if ($param->getSiteConfig()->plat_id == PlatId::MINI) {
                        $creative_data['landing_page'] = [
                            'mgk_page_id' => $other_setting->getLandingPage($account_param->account_id),
                        ];
                    } else {
                        $creative_data['landing_page'] = [
                            'url' => "bilibili://game_center/detail?id=" . (int)$param->getSiteConfig()->appid . "&sourceType=adPut"
                        ];
                    }
                }

                if ($param->getSiteConfig()->plat_id == PlatId::MINI) {
                    $mini_game_info = $other_setting->getMiniGame($account_param->account_id);
                    $creative_data['mini_game'] = [
                        'game_id' => $mini_game_info['id'] ?? '',
                        'url' => $mini_game_info['url'] ?? '',
                    ];
                }

                if ($other_setting->template_group_id == 1) {
                    $creative_data['image_list'] = [
                        [
                            'material_id' => "{$material_media_id_map[$creative_object['image_info']['id']]['id']}"
                        ]
                    ];
                    $creative_data['title_list'] = [
                        [
                            'title' => $creative_object['title'],
                        ]
                    ];
                }
                if ($other_setting->template_group_id == 4) {
                    $creative_data['archive_list'] = [
                        [
                            'source' => 1,
                            'avid' => "{$material_media_id_map[$creative_object['video_info']['id']]['id']}",
                            'cover' => [
                                'material_id' => $material_media_id_map[$creative_object['cover_info']['id']]['id'],
                                'is_use_smart_cut' => 0
                            ]
                        ]
                    ];
                    $creative_data['title_list'] = [
                        [
                            'title' => $creative_object['title'],
                        ]
                    ];
                }
                $creative_param->creative_list[] = $creative_data;
            }

            $result = (new CreativeModel())->save($creative_param, $account_param->account_id, $account_param->majordomo_name, $account_param->access_token);
            return $result['creative_id_list'];
        }
    }

    /**
     * 获取微信小游戏原始ID
     * @param $platform
     * @param $media_type
     * @param $game_id
     * @return array
     */
    public function getWechatGameInfo($platform, $media_type, $game_id)
    {
        $sdk_info = (new OdsMediaSDKModel())->getDataByGame($platform, $media_type, $game_id);
        if (empty($sdk_info)) {
            throw new AppException("请到SDK管理配置相应的游戏参数-小程序原始ID");
        }

        $ext = json_decode($sdk_info->ext, true);
        if (empty($ext['mini_game_original_id'])) {
            throw new AppException("请到SDK管理配置相应的游戏参数-小程序原始ID.");
        }

        return [
            'mini_game_original_id' => $ext['mini_game_original_id'],
        ];
    }

    /**
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @param array $material_media_id_map
     * @return mixed
     */
    function createAD(ADTaskParam $param, MediaAccountInfoParam $account_param, array $material_media_id_map)
    {
        $ad_task_model = new ADTaskModel();

        // 新建广告一级
        if (!$param->ad1_id) {
            $param->state_code = ADTaskModel::FINISH_UPLOAD_FLOW;
            try {
                $param->ad1_id = $this->getAD1IdByParam($param, $account_param)
                    ?: $this->createAD1($param, $account_param);
                $param->state_code = ADTaskModel::FINISH_CREATE_CAMPAIGN;
                $ad_task_model->updateTask($param, $param->id);
            } catch (Throwable $e) {
                $ad_task_model->updateTask($param, $param->id);
                throw new AppException('新建广告一级错误:' . $e->getMessage());
            }
        }

        $param->state_code = ADTaskModel::FINISH_CREATE_CAMPAIGN;
        ScriptMsgService::ADTaskUpdateByParam(new ADTaskUpdateParam($param));

        // 新建广告二级
        if (!$param->ad2_id) {
            try {
                $param->ad2_id = $this->createAD2($param, $account_param);
                $param->state_code = ADTaskModel::FINISH_CREATE_AD;
                $ad_task_model->updateTask($param, $param->id);
            } catch (Throwable $e) {
                $ad_task_model->updateTask($param, $param->id);
                throw new AppException('新建广告二级错误:' . $e->getMessage());
            }
        }

        $param->state_code = ADTaskModel::FINISH_CREATE_AD;
        ScriptMsgService::ADTaskUpdateByParam(new ADTaskUpdateParam($param));

        // 新建广告三级
        if (!$param->ad3_ids) {
            $param->state_code = ADTaskModel::FINISH_CREATE_AD;
            try {
                $creative_ids = $this->createAD3($param, $account_param, $material_media_id_map);
                $param->state_code = ADTaskModel::FINISH_CREATE_CREATIVE;
                $param->ad3_ids = $creative_ids;
                $ad_task_model->updateTask($param, $param->id);
            } catch (Throwable $e) {
                $ad_task_model->updateTask($param, $param->id);
                throw new AppException('新建广告三级错误:' . $e->getMessage());
            }
        }

        $param->state_code = ADTaskModel::FINISH_CREATE_CREATIVE;
        ScriptMsgService::ADTaskUpdateByParam(new ADTaskUpdateParam($param));

        return;
    }

    /**
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @return mixed
     */
    function pushFlowPackage(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        // TODO: Implement pushFlowPackage() method.
    }

    /**
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @return mixed
     */
    function pushAudiencePackage(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        // TODO: Implement pushAudiencePackage() method.
    }

    /**
     * @param ADTaskParam $param
     * @param MediaAccountInfoParam $account_param
     * @return mixed
     */
    function waitDelayPack(ADTaskParam $param, MediaAccountInfoParam $account_param)
    {
        // TODO: Implement waitDelayPack() method.
    }

    /**
     * 校验素材是否存在 不存在则过滤
     * @param array $upload_info_list
     * @param MediaAccountInfoParam $account_param
     * @return array
     */
    public function getMaterialAvid(array $upload_info_list, MediaAccountInfoParam $account_param): array
    {
        $map = [];

        foreach ($upload_info_list as $media_upload_info) {
            if ($media_upload_info->file_type == 2) {
                $verify_result = (new MetaDataModel())->getVideoAvid(str_replace('.mp4', '', $media_upload_info->filename), $account_param->account_id, $account_param->majordomo_name, $account_param->access_token);

                if ($verify_result['data']) {
                    $map[$media_upload_info->file_id] = [
                        'id' => $verify_result['data'][0]['ugc']['avid'],
                        'url' => '',
                    ];
                } else {
                    throw new AppException('视频素材审核状态为进行中，自动重启', ADTaskDigestLogic::RESTART_TASK_CODE);
                }
            } else {
                $verify_result = (new MetaDataModel())->uploadPicWithUrl($media_upload_info->url, $account_param->account_id, $account_param->majordomo_name, $account_param->access_token);
                if ($verify_result) {
                    $map[$media_upload_info->file_id] = [
                        'id' => $verify_result['material_id'],
                        'url' => '',
                    ];
                }
            }

        }

        return $map;
    }

    /**
     * @param Collection $material_file_list
     * @param MediaAccountInfoParam $account_param
     * @return mixed
     */
    function getMaterialFileMediaInfo(Collection $material_file_list, MediaAccountInfoParam $account_param)
    {
        $upload_info_list = (new MaterialUploadMediaTaskModel())->getMaterialUploadLogByFileIdList(
            array_column($material_file_list->toArray(), 'id'), MediaType::BILIBILI, $account_param->account_id)
            ->keyBy('file_id')->toArray();

        $avid_info_list = $this->getMaterialAvid($upload_info_list, $account_param);

        $result = [];

        $is_restart = false;

        foreach ($material_file_list as $material_file) {

            $material_file = new MaterialFileParam($material_file);
            $insert = [];
            $insert['platform'] = $material_file->platform;
            $insert['media_type'] = MediaType::BILIBILI;
            $insert['account_id'] = $account_param->account_id;
            $insert['material_id'] = $material_file->material_id;
            $insert['file_type'] = $material_file->file_type;
            $insert['file_id'] = $material_file->id;
            $insert['filename'] = $material_file->filename;
            $insert['signature'] = $material_file->signature;
            $insert['create_type'] = MaterialUploadMediaTaskModel::AD_UPLOAD;
            $insert['uploader'] = $material_file->uploader;

            if ($upload_info_list[$material_file->id]) {
                $result[$material_file->id] = [
                    'id' => $avid_info_list[$material_file->id]['id'],
                    'url' => $avid_info_list[$material_file->id]['url'],
                ];
            } else {
                if ($material_file->file_type == MaterialFileModel::FILE_TYPE_IMAGE ||
                    $material_file->file_type == MaterialFileModel::FILE_TYPE_COVER) {
                    $media_file_info = $this->uploadImage(
                        $material_file,
                        $account_param->account_id,
                        $account_param->access_token
                    );
                    $media_file_id = $media_file_info['id'];
                    $media_file_url = $media_file_info['url'];
                    $insert['media_material_file_id'] = $media_file_id;
                    $insert['url'] = $media_file_url;
                    $result[$material_file->id] = [
                        'id' => $media_file_id,
                        'url' => $media_file_url
                    ];
                    $is_restart = true;
                } else {
                    $media_file_info = $this->uploadVideo(
                        $material_file,
                        $account_param->account_id,
                        $account_param->access_token
                    );
                    $media_file_id = $media_file_info['id'];
                    $media_file_url = $media_file_info['url'];
                    $insert['media_material_file_id'] = $media_file_id;
                    $insert['url'] = $media_file_url;
                    $result[$material_file->id] = [
                        'id' => $media_file_id,
                        'url' => $media_file_url
                    ];

                    $is_restart = true;
                }

                if ($media_file_id) {
                    $insert['upload_state'] = MaterialUploadMediaTaskModel::SUCCESS_UPLOAD;
                    // 上传后增加上传记录
                    $log_result = (new MaterialUploadMediaTaskModel())->add(new MaterialUploadMediaTaskParam($insert));
                    if (!$log_result) {
                        throw new AppException('上传文件到媒体成功，但记录数据库错误');
                    }
                }
            }
        }

        if ($is_restart) {
            throw new AppException('素材上传需审核，自动重启', ADTaskDigestLogic::RESTART_TASK_CODE);
        }

        return $result;
    }

    /**
     * @param ADTaskParam $task_param
     * @return mixed
     */
    function isRestartADTask(ADTaskParam $task_param)
    {
        $need_restart_message_format_list = [
            '请求被限流，请稍后再试',
        ];
        foreach ($need_restart_message_format_list as $message_format) {
            if (strpos($task_param->error_msg, $message_format) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * @param ADTaskParam $task_param
     * @return mixed
     */
    function prepareCreativeList(ADTaskParam $task_param)
    {
        // TODO: Implement prepareCreativeList() method.
    }

    /**
     * @param ADTaskParam $task_param
     * @return mixed
     */
    function getAgentGroup(ADTaskParam $task_param)
    {
        if ($task_param->getSiteConfig()->ext['sub_pkg'] == 4) {
            return AgentGroup::BILIBILI_LY_CPS;
        }
        return AgentGroup::BILIBILI;
    }

    /**
     * @return Collection
     */
    public function getDiffMediaStatus()
    {
        // TODO: Implement getDiffMediaStatus() method.
    }

    /**
     * @param $keyword
     * @param $condition
     * @return Collection
     */
    public function getDiffMediaInventoryList($keyword, $condition): Collection
    {
        // TODO: Implement getDiffMediaInventoryList() method.
    }

    /**
     * @param ADTaskParam $task_param
     * @param $name_norms
     * @return mixed
     */
    public function getADName(ADTaskParam $task_param, $name_norms)
    {
        // TODO: Implement getADName() method.
    }

    /**
     * @param array $data
     * @return mixed
     */
    public function getTransferSet(array $data)
    {
        // TODO: Implement getTransferSet() method.
    }

    /**
     * @param $data
     * @return mixed
     */
    public function getAccountIdListByTransferableKey($data)
    {
        // TODO: Implement getAccountIdListByTransferableKey() method.
    }

    /**
     * @param $data
     * @return mixed
     */
    public function refreshAccountBalance($data)
    {
        // TODO: Implement refreshAccountBalance() method.
    }

    /**
     * 修改基本报表一级出价
     *
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     * @param string $update_field
     *
     * @return mixed
     */
    public function updateADAnalysisFirstClassCpaBid(int $media_type, array $data, array $ad_format_target, string $update_field)
    {
        // TODO: Implement updateADAnalysisFirstClassCpaBid() method.
    }

    /**
     * 修改基本报表一级投放时段
     *
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     *
     * @return mixed
     */
    public function updateFirstClassScheduleTime(int $media_type, array $data, array $ad_format_target)
    {
        // TODO: Implement updateFirstClassScheduleTime() method.
    }

    /**
     * 修改基本报表二级投放时段
     *
     * @param int $media_type
     * @param array $data
     * @param array $ad_format_target
     *
     * @return mixed
     */
    public function updateSecondClassScheduleTime(int $media_type, array $data, array $ad_format_target)
    {
        // TODO: Implement updateSecondClassScheduleTime() method.
    }

    /**
     * 获取媒体三级广告状态
     * @return Collection
     */
    public function getDiffMediaAD3Status()
    {
        // TODO: Implement getDiffMediaAD3Status() method.
    }

    public function updateADAnalysisADRaise(array $data)
    {
        // TODO: Implement updateADAnalysisADRaise() method.
    }

    public function updateFirstClassRoiGoal(array $data, array $ad_format_target)
    {
        // TODO: Implement updateFirstClassRoiGoal() method.
    }
}
