<?php


namespace App\Service\PlatformAD;

use App\Constant\ActionTrackType;
use App\Constant\AgentGroup;
use App\Constant\ConvertSourceType;
use App\Constant\ConvertToolkit;
use App\Constant\MediaType;
use App\Constant\PlatformAbility;
use App\Constant\PlatId;
use App\Exception\AppException;
use App\Model\SqlModel\Tanwan\V2DimGameIdModel;
use App\Model\SqlModel\Tanwan\V2DWDADTrackBaseLogModel;
use App\Model\SqlModel\Tanwan\V2ODSMediaCallbackModel;
use App\Model\SqlModel\DatahubLY\V2DWDADTrackBaseLogModel as LYV2DWDADTrackBaseLogModel;
use App\Model\SqlModel\DatahubLY\V2ODSMediaCallbackModel as LYV2ODSMediaCallbackModel;
use App\Param\ADMonetization\ADMonetizationGroupParam;
use App\Param\ADMonetization\ADMonetizationPlanParam;
use App\Param\LDYListParam;
use App\Param\AgentParam;
use App\Param\APKListParam;
use App\Param\SiteConfigParam;
use App\Param\SiteGroupEditParam;
use App\Param\SiteLogParam;

abstract class AbstractPlatform
{
    const ACTION_TRACK_DOMAIN_ZXZT = 'https://media-datahub.zxzt123.com';
    const DISPLAY_TRACK_DOMAIN_ZXZT = 'https://media-show.zxzt123.com';

    private $action_track_domain = 'https://media-datahub.wanzi.com';
    private $display_track_domain = 'https://media-show.wanzi.com';

    protected $platform = '';

    public function __construct($platform)
    {
        $this->platform = $platform;
    }

    public function setDisplayTrackDomain(string $display_track_domain): void
    {
        $this->display_track_domain = $display_track_domain;
    }

    public function setActionTrackDomain(string $action_track_domain): void
    {
        $this->action_track_domain = $action_track_domain;
    }

    abstract public function addAgent(AgentParam $param, $username);

    abstract public function editAgent(AgentParam $param, $username);

    abstract public function addSite(SiteConfigParam $param, $agent_leader, $user_name, $username, $options);

    abstract public function editSite(SiteConfigParam $param, $agent_leader, $user_name, $username, $options);

    abstract public function groupEditSite(SiteGroupEditParam $param, $username);

    abstract public function updateUptState($agent_id, $site_ids, $upt_state, $username);

    abstract public function getLDYList(LDYListParam $param, $username);

    abstract public function getLDYFileList($id, $username);

    abstract public function getTemplateList($page, $rows, $username);

    abstract public function addLDY($memo, $voice, $copyfrom, $theme, $username);

    abstract public function editLDY($id, $memo, $voice, $copyfrom, $theme, $username);

    abstract public function editLDYState($ids, $sid, $username);

    abstract public function deleteLDY($ids, $username);

    abstract public function uploadLDY($id, array $files, $username);

    abstract public function getHTML($id, $username);

    abstract public function editHTML($id, $content, $username);

    abstract public function deleteLDYFile($file_id, $username);

    abstract public function fwsh($ids, $ismark, $username);

    abstract public function score($id, $score, $username);

    abstract public function switchAD($site_ids, $option, $rsync_time, $is_game, $is_adid, $is_sdk, $username);

    abstract public function switchGame($agent_ids, $ori_game_id, $game_id, $username);

    abstract public function getCode($site_id, $game_id, $username);

    abstract public function addROIGame($media_type, $game_ids, $username);

    abstract public function deleteROIGame($media_type, $game_id, $username);

    /**
     * @doc 头条 https://ad.oceanengine.com/openapi/doc/index.html?id=1299
     * @doc 腾讯 https://developers.e.qq.com/docs/guide/conversion/click?version=1.1&_preview=1
     * @doc 快手 https://yiqixie.com/d/home/<USER>
     * @param object $table_site_data
     * @param $username
     * @return bool
     */
    public function reportTest(object $table_site_data, $username)
    {
        // 是否为联运渠道组
        $is_ly = in_array($table_site_data->agent_group, [AgentGroup::HONOR]);
        if ($is_ly) {
            $click_data = (new LYV2DWDADTrackBaseLogModel())->getDataBySiteId($table_site_data->platform, $table_site_data->site_id);
        } else {
            $click_data = (new V2DWDADTrackBaseLogModel())->getDataBySiteId($table_site_data->platform, $table_site_data->site_id);
        }
        if (
            empty($click_data) ||
            ($table_site_data->convert_toolkit === ConvertToolkit::TOUTIAO_ASSET && empty($click_data->click_id)) && !in_array($table_site_data->media_type, [MediaType::XINGTU, MediaType::XINGTU_AD, MediaType::XINGTU_STAR])
        ) {
            throw new AppException("测试上报接口失败|无点击数据");
        }

        if ($is_ly && empty($click_data->click_id)) {
            throw new AppException("联运测试上报接口失败|无点击数据");
        }

        // 查询游戏信息
        $game_info = (new V2DimGameIdModel())->getDataByGameId($table_site_data->platform, $table_site_data->game_id);
        if (empty($game_info)) {
            throw new AppException("测试上报接口失败|游戏不存在");
        }

        // 转化类型算一个事件
        // 深度转化算一个事件
        // 1:激活，2:注册，3:付费，5:次留
        $action_ids = [];
        // 上报不同类型 例：快手小游戏 虎牙小程序为2
        $callback_type = 0;
        if ($table_site_data->media_type === MediaType::TOUTIAO) {
            switch ($table_site_data->convert_type) {
                case 1:
                    $action_ids[] = 1;
                    break;
                case 2:
                    $action_ids[] = 2;
                    break;
                case 3:
                case 4:
                case 5:
                    $action_ids[] = 3;
                    break;
                default:
                    break;
            }
            switch ($table_site_data->deep_external_action) {
                case 'AD_CONVERT_TYPE_ACTIVE_REGISTER':
                    $action_ids[] = 2;
                    break;
                case 'AD_CONVERT_TYPE_PAY':
                case 'AD_CONVERT_TYPE_PURCHASE_ROI':
                case 'AD_CONVERT_TYPE_PURCHASE_ROI_7D':
                    $action_ids[] = 3;
                    break;
                default:
                    break;
            }
            // 头条事件管理 所有事件都联调
            if ($table_site_data->convert_toolkit === ConvertToolkit::TOUTIAO_ASSET) {
                $callback_type = 3;
                $action_ids = [1, 2, 3, 5];
            }

        } else if ($table_site_data->media_type === MediaType::TENCENT) {
            $action_ids[] = $table_site_data->convert_type === 'ACTIVATE_APP' ? 1 : 3;
            switch ($table_site_data->deep_external_action) {
                case 'OPTIMIZATIONGOAL_FIRST_PURCHASE':
                    $action_ids[] = 3;
                    break;
                default:
                    break;
            }
        } else if ($table_site_data->media_type === MediaType::KUAISHOU) {
            // 上报激活
            $action_ids[] = 1;
            $action_ids[] = (int)$table_site_data->convert_type === 1 ? 2 : 3;
            // 快手小游戏渠道组
            if ($table_site_data->agent_group === AgentGroup::KUAISHOU_MINI_GAME) {
                $callback_type = 2;
            }
        } else if ($table_site_data->media_type === MediaType::BAIDU) {
            // 上报激活
            $action_ids[] = 1;
            switch ($table_site_data->deep_external_action) {
                case '25':
                    $action_ids[] = 2;
                    break;
                case '26':
                    $action_ids[] = 3;
                    break;
                default:
                    break;
            }
        } else if ($table_site_data->media_type === MediaType::UC) {
            // 上报激活
            $action_ids[] = 1;
            if ($table_site_data->deep_external_action === '1000') {
                $action_ids[] = 2;
                $action_ids[] = 3;
            }
        } else if ($table_site_data->media_type === MediaType::HUYA) {
            // 虎牙小程序渠道组
            if ($table_site_data->agent_group === AgentGroup::HUYA_MINI_GAME) {
                // 上报激活
                $action_ids = [1, 2, 3];
                $callback_type = 2;
            } else {
                $action_ids = [1, 2, 3];
            }
        } else if ($table_site_data->media_type === MediaType::CHENZHONG) {
            $action_ids = [1, 2, 3];
        } else if ($table_site_data->media_type === MediaType::QIWU_CPS) {
            $action_ids = [1, 2, 3];
        } else if ($table_site_data->media_type === MediaType::XINGTU) {
            $action_ids = [1, 2, 3];
        } else if ($table_site_data->media_type === MediaType::IQIYI) {
            $action_ids = [1, 2, 3];
        } else if ($table_site_data->media_type === MediaType::ZHIHU) {
            $action_ids = [1, 2, 3];
        } else if ($table_site_data->media_type === MediaType::BILIBILI_LY) {
            $action_ids = [1, 2, 3];
        } else if ($table_site_data->media_type === MediaType::BILIBILI) {
            // 小游戏处理
            if ($game_info->plat_id == PlatId::MINI) {
                $callback_type = 2;
            }
            $action_ids = [1, 2, 3];
        } else if ($table_site_data->media_type === MediaType::NETEASE_NEWS) {
            $action_ids = [1, 2, 3];
        } else if ($table_site_data->media_type === MediaType::YOUKU) {
            $action_ids = [1, 2, 3];
        } else if ($table_site_data->media_type === MediaType::HONOR) {
            $action_ids = [1, 2, 3];
        } else if ($table_site_data->media_type === MediaType::DOUBAN) {
            $action_ids = [1, 2, 3];
        } else if ($table_site_data->media_type === MediaType::XINGTU_AD) {
            $callback_type = 3;
            $action_ids = [1, 2, 3];
        } else if ($table_site_data->media_type === MediaType::XIAOHONGSHU) {
            if (empty($click_data->click_id) || empty($click_data->account_id)) {
                throw new AppException("测试上报接口失败|点击数据或点击账号不存在");
            }
            $action_ids = [1, 2, 3];
        } else {
            throw new AppException("该媒体尚未对接测试上报");
        }

        $media_pay_money = [
            MediaType::ZHIHU => 6,
            MediaType::HUYA => 6,
            MediaType::BILIBILI_LY => 6,
            MediaType::BILIBILI => 6,
            MediaType::NETEASE_NEWS => 6,
            MediaType::YOUKU => 6,
            MediaType::DOUBAN => 6,
            MediaType::XIAOHONGSHU => 6,
        ];

        $agent_group_ly_channel = [
            AgentGroup::HONOR => 51066
        ];

        $callback_data = [];
        foreach ($action_ids as $action_id) {
            if ($action_id === 3) {
                $pay_money = $media_pay_money[$table_site_data->media_type] ?? 10;
            } else {
                $pay_money = 0;
            }
            $callback = $table_site_data->convert_toolkit === ConvertToolkit::TOUTIAO_ASSET && !in_array($table_site_data->media_type, [MediaType::XINGTU, MediaType::XINGTU_AD, MediaType::XINGTU_STAR])
                    ? $click_data->click_id
                    : $click_data->callback;
            $media_type_id = $is_ly ? $agent_group_ly_channel[$table_site_data->agent_group] : $click_data->media_type_id;
            $callback_data[] = [
                'pk_id' => $click_data->pk_id,
                'uid' => 0,
                'platform' => $click_data->platform,
                'game_id' => $click_data->game_id,
                'site_id' => $click_data->site_id,
                'adcre_id' => 0,
                'imei' => '',
                'muid' => $click_data->muid,
                'device_type' => 3,
                'click_time' => $click_data->click_time,
                'conv_time' => $click_data->click_time, // 没有产生真正事件 用click_time
                'callback' => $callback,
                'media_type_id' => $media_type_id,
                'action_id' => $action_id,
                'source_type' => 1,
                'callback_type' => $callback_type,
                'pay_money' => $pay_money,
            ];
        }

        if ($is_ly) {
            return (new LYV2ODSMediaCallbackModel())->add($callback_data);
        } else {
            return (new V2ODSMediaCallbackModel())->add($callback_data);
        }
    }

    abstract public function getDataDetail($agent_id, $site_id, $start_date, $end_date, $username);

    abstract public function detailExport($agent_id, $start_date, $end_date, $username);

    abstract public function getAPKList(APKListParam $param, $username);

    abstract public function getAPKStateList($site_ids, $username);

    abstract public function setAPK($site_ids, $game_id, $version, $type, $media_type, $username);

    abstract public function editAPK($ids, $data, $username);

    abstract public function getSiteLogs(SiteLogParam $param, $username);

    public function getSiteLDYUrl($agent_id, $site_id, $base_url)
    {
        return "$base_url/code/$agent_id/$site_id.html";
    }

    abstract public function refreshCDN($url, $username);

    protected function getActionTrackURL(SiteConfigParam $param, $site_data)
    {
        $action_track_url = '';
        $domain = $this->action_track_domain;

        switch ($param->media_type) {
            case MediaType::TOUTIAO:
                if ($param->action_track_type === ActionTrackType::DEFAULT) {
                    if ($param->game_type === 'IOS') {
                        $action_track_url = "{$domain}/toutiao/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&idfa=__IDFA__&os=__OS__&callback=__CALLBACK_URL__&click_time=__TS__&adgroup_id=__AID__&adgroup_name=__AID_NAME__&campaign_id=__CAMPAIGN_ID__&adcre_id=__CID__&csite=__CSITE__&ctype=__CTYPE__&ip=__IP__&mac=__MAC1__&ua=__UA__&union_site=__UNION_SITE__&account_id=__ADVERTISER_ID__&geo=__GEO__&device_model=__MODEL__&request_id=__REQUEST_ID__&caid_caa=__CAID__&rta_id=__RTA_TRACE_ID__&outer_target_id=__RTAID__&rta_exp_id=__VID__&muid=__IDFA_MD5__&click_id=__CLICK_ID__&port_version=1.0";
                    } else {
                        $action_track_url = "{$domain}/toutiao/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&muid=__IMEI__&android_id=__ANDROIDID__&os=__OS__&callback=__CALLBACK_URL__&click_time=__TS__&adgroup_id=__AID__&adgroup_name=__AID_NAME__&campaign_id=__CAMPAIGN_ID__&adcre_id=__CID__&csite=__CSITE__&oaid=__OAID__&ctype=__CTYPE__&ip=__IP__&mac=__MAC1__&ua=__UA__&union_site=__UNION_SITE__&account_id=__ADVERTISER_ID__&geo=__GEO__&device_model=__MODEL__&request_id=__REQUEST_ID__&rta_id=__RTA_TRACE_ID__&outer_target_id=__RTAID__&rta_exp_id=__VID__&moaid=__OAID_MD5__&click_id=__CLICK_ID__&port_version=1.0";
                    }
                    if (in_array($param->plat_id, [PlatId::DY_MINI, PlatId::DY_MINI_PROGRAM])) {
                        if ($param->game_type === 'IOS') {
                            $action_track_url = "{$domain}/toutiao/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&idfa=__IDFA__&os=__OS__&callback=__CALLBACK_URL__&click_time=__TS__&adgroup_id=__AID__&adgroup_name=__AID_NAME__&campaign_id=__CAMPAIGN_ID__&adcre_id=__CID__&csite=__CSITE__&ctype=__CTYPE__&ip=__IP__&mac=__MAC1__&ua=__UA__&union_site=__UNION_SITE__&account_id=__ADVERTISER_ID__&geo=__GEO__&device_model=__MODEL__&request_id=__REQUEST_ID__&caid_caa=__CAID__&rta_id=__RTA_TRACE_ID__&outer_target_id=__RTAID__&rta_exp_id=__VID__&muid=__IDFA_MD5__&click_id=__CLICK_ID__&aweme_mini_open_id=__OPENID__&port_version=1.0";
                        } else {
                            $action_track_url = "{$domain}/toutiao/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&muid=__IMEI__&android_id=__ANDROIDID__&os=__OS__&callback=__CALLBACK_URL__&click_time=__TS__&adgroup_id=__AID__&adgroup_name=__AID_NAME__&campaign_id=__CAMPAIGN_ID__&adcre_id=__CID__&csite=__CSITE__&oaid=__OAID__&ctype=__CTYPE__&ip=__IP__&mac=__MAC1__&ua=__UA__&union_site=__UNION_SITE__&account_id=__ADVERTISER_ID__&geo=__GEO__&device_model=__MODEL__&request_id=__REQUEST_ID__&rta_id=__RTA_TRACE_ID__&outer_target_id=__RTAID__&rta_exp_id=__VID__&moaid=__OAID_MD5__&click_id=__CLICK_ID__&aweme_mini_open_id=__OPENID__&port_version=1.0";
                        }
                    }
                } else {
                    if ($param->game_type === 'IOS') {
                        $action_track_url = "{$domain}/toutiao/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&idfa=__IDFA__&os=__OS__&callback=__CALLBACK_URL__&click_time=__TS__&adgroup_id=__PROMOTION_ID__&adgroup_name=__AID_NAME__&campaign_id=__PROJECT_ID__&adcre_id=__CID__&csite=__CSITE__&ctype=__CTYPE__&ip=__IP__&mac=__MAC1__&ua=__UA__&union_site=__UNION_SITE__&account_id=__ADVERTISER_ID__&geo=__GEO__&device_model=__MODEL__&request_id=__REQUEST_ID__&caid_caa=__CAID__&rta_id=__RTA_TRACE_ID__&outer_target_id=__RTAID__&rta_exp_id=__VID__&muid=__IDFA_MD5__&click_id=__CLICK_ID__&image_material_id=__MID1__&title_material_id=__MID2__&video_material_id=__MID3__&playable_material_id=__MID4__&external_material_id=__MID5__&weburl_material_id=__MID6__&port_version=2.0";
                    } else {
                        $action_track_url = "{$domain}/toutiao/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&muid=__IMEI__&android_id=__ANDROIDID__&os=__OS__&callback=__CALLBACK_URL__&click_time=__TS__&adgroup_id=__PROMOTION_ID__&adgroup_name=__AID_NAME__&campaign_id=__PROJECT_ID__&adcre_id=__CID__&csite=__CSITE__&oaid=__OAID__&ctype=__CTYPE__&ip=__IP__&mac=__MAC1__&ua=__UA__&union_site=__UNION_SITE__&account_id=__ADVERTISER_ID__&geo=__GEO__&device_model=__MODEL__&request_id=__REQUEST_ID__&rta_id=__RTA_TRACE_ID__&outer_target_id=__RTAID__&rta_exp_id=__VID__&moaid=__OAID_MD5__&click_id=__CLICK_ID__&image_material_id=__MID1__&title_material_id=__MID2__&video_material_id=__MID3__&playable_material_id=__MID4__&external_material_id=__MID5__&weburl_material_id=__MID6__&port_version=2.0";
                    }
                    if (in_array($param->plat_id, [PlatId::DY_MINI, PlatId::DY_MINI_PROGRAM])) {
                        if ($param->game_type === 'IOS') {
                            $action_track_url = "{$domain}/toutiao/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&idfa=__IDFA__&os=__OS__&callback=__CALLBACK_URL__&click_time=__TS__&adgroup_id=__PROMOTION_ID__&adgroup_name=__AID_NAME__&campaign_id=__PROJECT_ID__&adcre_id=__CID__&csite=__CSITE__&ctype=__CTYPE__&ip=__IP__&mac=__MAC1__&ua=__UA__&union_site=__UNION_SITE__&account_id=__ADVERTISER_ID__&geo=__GEO__&device_model=__MODEL__&request_id=__REQUEST_ID__&caid_caa=__CAID__&rta_id=__RTA_TRACE_ID__&outer_target_id=__RTAID__&rta_exp_id=__VID__&muid=__IDFA_MD5__&click_id=__CLICK_ID__&image_material_id=__MID1__&title_material_id=__MID2__&video_material_id=__MID3__&playable_material_id=__MID4__&external_material_id=__MID5__&weburl_material_id=__MID6__&aweme_mini_open_id=__OPENID__&port_version=2.0";
                        } else {
                            $action_track_url = "{$domain}/toutiao/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&muid=__IMEI__&android_id=__ANDROIDID__&os=__OS__&callback=__CALLBACK_URL__&click_time=__TS__&adgroup_id=__PROMOTION_ID__&adgroup_name=__AID_NAME__&campaign_id=__PROJECT_ID__&adcre_id=__CID__&csite=__CSITE__&oaid=__OAID__&ctype=__CTYPE__&ip=__IP__&mac=__MAC1__&ua=__UA__&union_site=__UNION_SITE__&account_id=__ADVERTISER_ID__&geo=__GEO__&device_model=__MODEL__&request_id=__REQUEST_ID__&rta_id=__RTA_TRACE_ID__&outer_target_id=__RTAID__&rta_exp_id=__VID__&moaid=__OAID_MD5__&click_id=__CLICK_ID__&image_material_id=__MID1__&title_material_id=__MID2__&video_material_id=__MID3__&playable_material_id=__MID4__&external_material_id=__MID5__&weburl_material_id=__MID6__&aweme_mini_open_id=__OPENID__&port_version=2.0";
                        }
                    }
                }
                break;
            case MediaType::TENCENT:
            case MediaType::CHANNELS_LIVE:
            case MediaType::MEDIA_SCHEDULE:
                if ($param->action_track_type === ActionTrackType::DEFAULT) {
                    if ($param->game_type === 'IOS') {
                        $action_track_url = "{$domain}/gdt/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&account_id=__ACCOUNT_ID__&os=__DEVICE_OS_TYPE__&click_id=__CLICK_ID__&click_time=__CLICK_TIME__&callback=__CALLBACK__&deeplink_url=__UNIVERSAL_LINK__&campaign_id=__CAMPAIGN_ID__&adgroup_id=__ADGROUP_ID__&adcre_id=__AD_ID__&csite=__SITE_SET_NAME__&ad_platform_type=__AD_PLATFORM_TYPE__&ip=__IP__&ua=__USER_AGENT__&muid=__MUID__&app_id=__PROMOTED_OBJECT_ID__&click_cost=__REAL_COST__&ctype=__PROMOTED_OBJECT_TYPE__&ipv6=__IPV6__&caid_caa=__QAID_CAA__&request_id=__REQUEST_ID__&impression_id=__IMPRESSION_ID__&rta_id=__RTA_TRACE_ID__&rta_exp_id=__RTA_EXP_ID__&union_site=__ENCRYPTED_POSITION_ID__&rta_valid_features=__RTA_VALID_FEATURES__&outer_target_id=__RTA_TARGET_ID__&ipv6=__IPV6__&port_version=1.0";
                    } else {
                        $action_track_url = "{$domain}/gdt/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&android_id=__HASH_ANDROID_ID__&account_id=__ACCOUNT_ID__&os=__DEVICE_OS_TYPE__&click_id=__CLICK_ID__&click_time=__CLICK_TIME__&callback=__CALLBACK__&deeplink_url=__DEEPLINK_URL__&campaign_id=__CAMPAIGN_ID__&adgroup_id=__ADGROUP_ID__&adcre_id=__AD_ID__&csite=__SITE_SET_NAME__&ad_platform_type=__AD_PLATFORM_TYPE__&ip=__IP__&ua=__USER_AGENT__&muid=__MUID__&oaid=__OAID__&app_id=__PROMOTED_OBJECT_ID__&click_cost=__REAL_COST__&ctype=__PROMOTED_OBJECT_TYPE__&ipv6=__IPV6__&moaid=__HASH_OAID__&request_id=__REQUEST_ID__&impression_id=__IMPRESSION_ID__&rta_id=__RTA_TRACE_ID__&rta_exp_id=__RTA_EXP_ID__&union_site=__ENCRYPTED_POSITION_ID__&rta_valid_features=__RTA_VALID_FEATURES__&outer_target_id=__RTA_TARGET_ID__&ipv6=__IPV6__&port_version=1.0";
                    }
                    if ($param->plat_id == PlatId::MINI) {
                        $action_track_url = "{$domain}/gdt/ods_media_click_log/{$param->platform}/log.gif?agent_id={$param->agent_id}&site_id={$site_data['site_id']}&game_id={$param->game_id}&rta_exp_id=__PKAM_EXP_IDS__&os=__DEVICE_OS_TYPE__&request_id=__REQUEST_ID__&click_time=__CLICK_TIME__&csite=__SITE_SET__&ctype=__AD_PLATFORM_TYPE__&click_cost=__REAL_COST__&account_id=__ACCOUNT_ID__&adcre_id=__AD_ID__&callback=__CALLBACK__&union_site=__ENCRYPTED_POSITION_ID__&campaign_id=__CAMPAIGN_ID__&click_id=__CLICK_ID__&impression_id=__IMPRESSION_ID__&wechat_open_id=__WECHAT_OPEN_ID__&adgroup_id=__ADGROUP_ID__&app_id=__PROMOTED_OBJECT_ID__&port_version=1.0";
                    }
                } else if ($param->action_track_type === ActionTrackType::TENCENT_ADA) {
                    if ($param->game_type === 'IOS') {
                        $action_track_url = "{$domain}/gdt/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&account_id=__ACCOUNT_ID__&os=__DEVICE_OS_TYPE__&click_id=__CLICK_ID__&click_time=__CLICK_TIME__&callback=__CALLBACK__&deeplink_url=__UNIVERSAL_LINK__&campaign_id=__PROJECT_ID__&csite=__SITE_SET_NAME__&ad_platform_type=__AD_PLATFORM_TYPE__&ip=__IP__&ua=__USER_AGENT__&muid=__MUID__&app_id=__PROMOTED_OBJECT_ID__&click_cost=__REAL_COST__&ctype=__PROMOTED_OBJECT_TYPE__&ipv6=__IPV6__&caid_caa=__QAID_CAA__&request_id=__REQUEST_ID__&impression_id=__IMPRESSION_ID__&rta_id=__RTA_TRACE_ID__&rta_exp_id=__RTA_EXP_ID__&union_site=__ENCRYPTED_POSITION_ID__&rta_valid_features=__RTA_VALID_FEATURES__&outer_target_id=__RTA_TARGET_ID__&ipv6=__IPV6__&video_material_id=__ELEMENT_INFO__&port_version=2.0";
                    } else {
                        $action_track_url = "{$domain}/gdt/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&android_id=__HASH_ANDROID_ID__&account_id=__ACCOUNT_ID__&os=__DEVICE_OS_TYPE__&click_id=__CLICK_ID__&click_time=__CLICK_TIME__&callback=__CALLBACK__&deeplink_url=__DEEPLINK_URL__&campaign_id=__PROJECT_ID__&csite=__SITE_SET_NAME__&ad_platform_type=__AD_PLATFORM_TYPE__&ip=__IP__&ua=__USER_AGENT__&muid=__MUID__&oaid=__OAID__&app_id=__PROMOTED_OBJECT_ID__&click_cost=__REAL_COST__&ctype=__PROMOTED_OBJECT_TYPE__&ipv6=__IPV6__&moaid=__HASH_OAID__&request_id=__REQUEST_ID__&impression_id=__IMPRESSION_ID__&rta_id=__RTA_TRACE_ID__&rta_exp_id=__RTA_EXP_ID__&union_site=__ENCRYPTED_POSITION_ID__&rta_valid_features=__RTA_VALID_FEATURES__&outer_target_id=__RTA_TARGET_ID__&ipv6=__IPV6__&video_material_id=__ELEMENT_INFO__&port_version=2.0";
                    }
                    if ($param->plat_id == PlatId::MINI) {
                        $action_track_url = "{$domain}/gdt/ods_media_click_log/{$param->platform}/log.gif?agent_id={$param->agent_id}&site_id={$site_data['site_id']}&game_id={$param->game_id}&rta_exp_id=__PKAM_EXP_IDS__&os=__DEVICE_OS_TYPE__&request_id=__REQUEST_ID__&click_time=__CLICK_TIME__&csite=__SITE_SET__&ctype=__AD_PLATFORM_TYPE__&click_cost=__REAL_COST__&account_id=__ACCOUNT_ID__&callback=__CALLBACK__&union_site=__ENCRYPTED_POSITION_ID__&campaign_id=__PROJECT_ID__&click_id=__CLICK_ID__&impression_id=__IMPRESSION_ID__&wechat_open_id=__WECHAT_OPEN_ID__&app_id=__PROMOTED_OBJECT_ID__&video_material_id=__ELEMENT_INFO__&port_version=2.0";
                    }
                } else {
                    if ($param->game_type === 'IOS') {
                        $action_track_url = "{$domain}/gdt/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&account_id=__ACCOUNT_ID__&os=__DEVICE_OS_TYPE__&click_id=__CLICK_ID__&click_time=__CLICK_TIME__&callback=__CALLBACK__&deeplink_url=__UNIVERSAL_LINK__&campaign_id=__ADGROUP_ID__&adgroup_id=__ADGROUP_ID__&adcre_id=__DYNAMIC_CREATIVE_ID__&csite=__SITE_SET_NAME__&ad_platform_type=__AD_PLATFORM_TYPE__&ip=__IP__&ua=__USER_AGENT__&muid=__MUID__&app_id=__PROMOTED_OBJECT_ID__&click_cost=__REAL_COST__&ctype=__PROMOTED_OBJECT_TYPE__&ipv6=__IPV6__&caid_caa=__QAID_CAA__&request_id=__REQUEST_ID__&impression_id=__IMPRESSION_ID__&rta_id=__RTA_TRACE_ID__&rta_exp_id=__RTA_EXP_ID__&union_site=__ENCRYPTED_POSITION_ID__&rta_valid_features=__RTA_VALID_FEATURES__&outer_target_id=__RTA_TARGET_ID__&ipv6=__IPV6__&video_material_id=__ELEMENT_INFO__&external_material_id=__CREATIVE_COMPONENTS_INFO__&port_version=3.0";
                    } else {
                        $action_track_url = "{$domain}/gdt/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&android_id=__HASH_ANDROID_ID__&account_id=__ACCOUNT_ID__&os=__DEVICE_OS_TYPE__&click_id=__CLICK_ID__&click_time=__CLICK_TIME__&callback=__CALLBACK__&deeplink_url=__DEEPLINK_URL__&campaign_id=__ADGROUP_ID__&adgroup_id=__ADGROUP_ID__&adcre_id=__DYNAMIC_CREATIVE_ID__&csite=__SITE_SET_NAME__&ad_platform_type=__AD_PLATFORM_TYPE__&ip=__IP__&ua=__USER_AGENT__&muid=__MUID__&oaid=__OAID__&app_id=__PROMOTED_OBJECT_ID__&click_cost=__REAL_COST__&ctype=__PROMOTED_OBJECT_TYPE__&ipv6=__IPV6__&moaid=__HASH_OAID__&request_id=__REQUEST_ID__&impression_id=__IMPRESSION_ID__&rta_id=__RTA_TRACE_ID__&rta_exp_id=__RTA_EXP_ID__&union_site=__ENCRYPTED_POSITION_ID__&rta_valid_features=__RTA_VALID_FEATURES__&outer_target_id=__RTA_TARGET_ID__&ipv6=__IPV6__&video_material_id=__ELEMENT_INFO__&external_material_id=__CREATIVE_COMPONENTS_INFO__&port_version=3.0";
                    }
                    if ($param->plat_id == PlatId::MINI) {
                        $action_track_url = "{$domain}/gdt/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&rta_exp_id=__PKAM_EXP_IDS__&os=__DEVICE_OS_TYPE__&request_id=__REQUEST_ID__&click_time=__CLICK_TIME__&csite=__SITE_SET_NAME__&ctype=__AD_PLATFORM_TYPE__&click_cost=__REAL_COST__&account_id=__ACCOUNT_ID__&callback=__CALLBACK__&union_site=__ENCRYPTED_POSITION_ID__&campaign_id=__ADGROUP_ID__&adgroup_id=__ADGROUP_ID__&adcre_id=__DYNAMIC_CREATIVE_ID__&click_id=__CLICK_ID__&impression_id=__IMPRESSION_ID__&wechat_open_id=__WECHAT_OPEN_ID__&app_id=__PROMOTED_OBJECT_ID__&video_material_id=__ELEMENT_INFO__&external_material_id=__CREATIVE_COMPONENTS_INFO__&port_version=3.0";
                    }
                }
                break;
            case MediaType::KUAISHOU:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/kuaishou/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&account_id=__ACCOUNTID__&campaign_id=__DID__&campaign_name=__DNAME__&adgroup_id=__AID__&adcre_id=__CID__&csite=__CSITE__&os=__OS__&click_time=__TS__&muid=__IDFA2__&ip=__IP__&ua=__UA__&callback=__CALLBACK__&caid_caa=__KENYID_CAA__&rta_exp_id=__VID__";
                } else {
                    $action_track_url = "{$domain}/kuaishou/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&account_id=__ACCOUNTID__&campaign_id=__DID__&campaign_name=__DNAME__&adgroup_id=__AID__&adcre_id=__CID__&csite=__CSITE__&os=__OS__&click_time=__TS__&muid=__IMEI2__&oaid=__OAID__&mac=__MAC__&android_id=__ANDROIDID2__&ip=__IP__&ua=__UA__&callback=__CALLBACK__&moaid=__OAID2__&rta_exp_id=__VID__";
                }
                break;
            case MediaType::BAIDU:
                if ($param->plat_id == PlatId::MINI) {
                    if ($param->game_type === 'IOS') {
                        $action_track_url = "{$domain}/baidu/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&campaign_id={{PLAN_ID}}&adgroup_id={{UNIT_ID}}&adcre_id={{IDEA_ID}}&account_id={{USER_ID}}&ip={{IP}}&ua={{UA}}&os={{OS}}&click_time={{TS}}&click_id={{BD_VID}}&idfa={{IDFA}}&external_material_id={{COMBID}}&ipv6={{IPV6}}";
                    } else {
                        $action_track_url = "{$domain}/baidu/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&campaign_id={{PLAN_ID}}&adgroup_id={{UNIT_ID}}&adcre_id={{IDEA_ID}}&account_id={{USER_ID}}&ip={{IP}}&ua={{UA}}&os={{OS}}&click_time={{TS}}&click_id={{BD_VID}}&muid={{IMEI_MD5}}&android_id={{ANDROID_ID_MD5}}&oaid={{OAID}}&external_material_id={{COMBID}}&ipv6={{IPV6}}";
                    }
                } else if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/baidu/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&account_id={{USER_ID}}&idfa={{IDFA}}&mac={{MAC_MD5}}&os={{OS}}&ip={{IP}}&ua={{UA}}&click_time={{TS}}&click_id={{CLICK_ID}}&campaign_id={{PLAN_ID}}&adgroup_id={{UNIT_ID}}&adcre_id={{IDEA_ID}}&deeplink_url={{DEEPLINK_URL}}&callback={{CALLBACK_URL}}&sign={{SIGN}}&caid_caa={{CAID}}&external_material_id={{COMBID}}&ipv6={{IPV6}}&impression_id={{BD_VID}}";
                } else if ($param->convert_source_type === ConvertSourceType::SDK) {
                    $action_track_url = "{$domain}/baidu/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&android_id={{ANDROID_ID_MD5}}&account_id={{USER_ID}}&muid={{IMEI_MD5}}&mac={{MAC_MD5}}&oaid={{OAID}}&moaid={{OAID_MD5}}&os={{OS}}&ip={{IP}}&ua={{UA}}&click_time={{TS}}&click_id={{CLICK_ID}}&campaign_id={{PLAN_ID}}&adgroup_id={{UNIT_ID}}&adcre_id={{IDEA_ID}}&deeplink_url={{DEEPLINK_URL}}&sign={{SIGN}}&external_material_id={{COMBID}}&ipv6={{IPV6}}";
                } else {
                    $action_track_url = "{$domain}/baidu/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&android_id={{ANDROID_ID_MD5}}&account_id={{USER_ID}}&muid={{IMEI_MD5}}&mac={{MAC_MD5}}&oaid={{OAID}}&moaid={{OAID_MD5}}&os={{OS}}&ip={{IP}}&ua={{UA}}&click_time={{TS}}&click_id={{CLICK_ID}}&campaign_id={{PLAN_ID}}&adgroup_id={{UNIT_ID}}&adcre_id={{IDEA_ID}}&deeplink_url={{DEEPLINK_URL}}&callback={{CALLBACK_URL}}&sign={{SIGN}}&external_material_id={{COMBID}}&ipv6={{IPV6}}";
                }
                break;
            case MediaType::BAIDU_SEARCH:
                if ($param->plat_id == PlatId::MINI) {
                    if ($param->game_type === 'IOS') {
                        $action_track_url = "{$domain}/baidu_search/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&campaign_id={{PLAN_ID}}&adgroup_id={{UNIT_ID}}&adcre_id={{IDEA_ID}}&account_id={{USER_ID}}&ip={{IP}}&ua={{UA}}&os={{OS}}&click_time={{TS}}&click_id={{BD_VID}}&idfa={{IDFA}}&external_material_id={{COMBID}}&ipv6={{IPV6}}";
                    } else {
                        $action_track_url = "{$domain}/baidu_search/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&campaign_id={{PLAN_ID}}&adgroup_id={{UNIT_ID}}&adcre_id={{IDEA_ID}}&account_id={{USER_ID}}&ip={{IP}}&ua={{UA}}&os={{OS}}&click_time={{TS}}&click_id={{BD_VID}}&muid={{IMEI_MD5}}&android_id={{ANDROID_ID_MD5}}&oaid={{OAID}}&external_material_id={{COMBID}}&ipv6={{IPV6}}";
                    }
                } else if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/baidu_search/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&account_id={{USER_ID}}&idfa={{IDFA}}&mac={{MAC_MD5}}&os={{OS}}&ip={{IP}}&ua={{UA}}&click_time={{TS}}&click_id={{CLICK_ID}}&campaign_id={{PLAN_ID}}&adgroup_id={{UNIT_ID}}&adcre_id={{IDEA_ID}}&deeplink_url={{DEEPLINK_URL}}&callback={{CALLBACK_URL}}&sign={{SIGN}}&caid_caa={{CAID}}&external_material_id={{COMBID}}&ipv6={{IPV6}}&impression_id={{BD_VID}}";
                } else if ($param->convert_source_type === ConvertSourceType::SDK) {
                    $action_track_url = "{$domain}/baidu_search/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&android_id={{ANDROID_ID_MD5}}&account_id={{USER_ID}}&muid={{IMEI_MD5}}&mac={{MAC_MD5}}&oaid={{OAID}}&moaid={{OAID_MD5}}&os={{OS}}&ip={{IP}}&ua={{UA}}&click_time={{TS}}&click_id={{CLICK_ID}}&campaign_id={{PLAN_ID}}&adgroup_id={{UNIT_ID}}&adcre_id={{IDEA_ID}}&deeplink_url={{DEEPLINK_URL}}&sign={{SIGN}}&external_material_id={{COMBID}}&ipv6={{IPV6}}";
                } else {
                    $action_track_url = "{$domain}/baidu_search/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&android_id={{ANDROID_ID_MD5}}&account_id={{USER_ID}}&muid={{IMEI_MD5}}&mac={{MAC_MD5}}&oaid={{OAID}}&moaid={{OAID_MD5}}&os={{OS}}&ip={{IP}}&ua={{UA}}&click_time={{TS}}&click_id={{CLICK_ID}}&campaign_id={{PLAN_ID}}&adgroup_id={{UNIT_ID}}&adcre_id={{IDEA_ID}}&deeplink_url={{DEEPLINK_URL}}&callback={{CALLBACK_URL}}&sign={{SIGN}}&external_material_id={{COMBID}}&ipv6={{IPV6}}";
                }
                break;
            case MediaType::MP:
                $action_track_url = "?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}";
                break;
            case MediaType::UC:
                if ($param->plat_id == PlatId::MINI) {
                    $action_track_url = "{$domain}/uc/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&account_id={ACID}&android_id={ANDROIDID_SUM}&muid={IMEI_SUM1}&oaid={OAID}&moaid={OAID_SUM}&click_time={TS}&callback={CALLBACK_URL}&campaign_id={GID}&adgroup_id={AID}&adcre_id={CID}&ua={UA}&os={OS}&ip={IP}&click_id={uctrackid}&idfa={IDFA1}";
                } else if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/uc/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&account_id={ACID}&idfa={IDFA1}&click_time={TS}&callback={CALLBACK_URL}&campaign_id={GID}&adgroup_id={AID}&adcre_id={CID}&ua={UA}&os={OS}&ip={IP}&click_id={uctrackid}&caid_caa={CAID}";
                } else if ($param->convert_source_type === ConvertSourceType::SDK) {
                    $action_track_url = "{$domain}/uc/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&account_id={ACID}&android_id={ANDROIDID_SUM1}&muid={IMEI_SUM1}&oaid={OAID}&click_time={TS}&campaign_id={GID}&adgroup_id={AID}&adcre_id={CID}&mac={MAC_SUM2}&ua={UA}&os={OS}&ip={IP}";
                } else {
                    $action_track_url = "{$domain}/uc/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&account_id={ACID}&android_id={ANDROIDID_SUM}&muid={IMEI_SUM1}&oaid={OAID}&moaid={OAID_SUM}&click_time={TS}&callback={CALLBACK_URL}&campaign_id={GID}&adgroup_id={AID}&adcre_id={CID}&ua={UA}&os={OS}&ip={IP}&click_id={uctrackid}";
                }
                break;
            case MediaType::NETEASE:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/netease/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&idfa=__IDFA__&os=__OS__&ua=__UA__&ip=__IP__&click_time=__CLICKTIME__&click_id=__CLICKID__&account_id=__SPONSOR_ID__&caid_caa=__YAID__&campaign_id=__PLAN_ID__&adgroup_id=__ADID__&adcre_id=__CREATIVE_ID__";
                } else {
                    $action_track_url = "{$domain}/netease/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&imei=__IMEI__&os=__OS__&ua=__UA__&ip=__IP__&click_time=__CLICKTIME__&click_id=__CLICKID__&account_id=__SPONSOR_ID__&campaign_id=__PLAN_ID__&adgroup_id=__ADID__&adcre_id=__CREATIVE_ID__&android_id=__ANDROIDID__&moaid=__OAIDMD5__";
                }
                break;
            case MediaType::SHENMA_SEARCH:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/shenmasousuo/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&idfa={IDFA_SUM}&click_time={TS}&callback={CALLBACK_URL}&campaign_id={PLANID}&adgroup_id={UNITID}&adcre_id={IDEAID}&request_id={CLICKID}&ua={UA}&os={OS}&geo={LBS}&ip={IP}";
                } else {
                    $action_track_url = "{$domain}/shenmasousuo/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&muid={IMEI_SUM}&oaid={oaid}&click_time={TS}&callback={CALLBACK_URL}&campaign_id={PLANID}&adgroup_id={UNITID}&adcre_id={IDEAID}&request_id={CLICKID}&ua={UA}&os={OS}&geo={LBS}&ip={IP}";
                }
                break;
            case MediaType::QIHU_SEARCH:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/qihusearch/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&request_id=__UniqueID__&click_time=__clicktime__&ip=__IP__&os=__OS__&device_model=__devicetype__&idfa=__IDFA__&mac=__MAC_MD5__&campaign_id=__planid__&adgroup_id=__groupid__&adcre_id=__creativeid__&callback=__callback_url__&ua=__UA__";
                } else {
                    $action_track_url = "{$domain}/qihusearch/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&request_id=__UniqueID__&click_time=__clicktime__&ip=__IP__&os=__OS__&device_model=__devicetype__&muid=__imei_md5__&moaid=__oaid_md5__&mac=__MAC_MD5__&android_id=__ANDROID_ID__&campaign_id=__planid__&adgroup_id=__groupid__&adcre_id=__creativeid__&callback=__callback_url__&ua=__UA__";
                }
                break;
            case MediaType::CHENZHONG:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/kuaijiedan/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&idfa=__IDFA2__&callback=__CALLBACK__&click_time=__TS__&ip=__IP__&rta_exp_id=__VID__";
                } else {
                    $action_track_url = "{$domain}/kuaijiedan/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&imei=__IMEI2__&oaid=__OAID__&callback=__CALLBACK__&click_time=__TS__&ip=__IP__&rta_exp_id=__VID__";
                }
                break;
            case MediaType::DOUYIN_STAR:
                $action_track_url = "{$domain}/douyin_star/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&ip=__IP__&ua=__UA__&os=__OS__&click_time=__TS__&device_model=__MODEL__&video_material_id=__ITEM_ID__&callback_param=__CALLBACK_PARAM__&callback=__CALLBACK_URL__";
                break;
            case MediaType::TOUTIAO_APP:
                if ($param->agent_group === AgentGroup::DOUYIN_UOP) {
                    $action_track_url = "{$domain}/toutiao_app/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&ip=__IP__&ua=__UA__&os=__OS__&click_time=__TS__&device_model=__MODEL__&callback=__CALLBACK_PARAM__&aweme_account=__GAME_AWEME_ID__";
                } else {
                    $action_track_url = "{$domain}/toutiao_app/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&ip=__IP__&ua=__UA__&os=__OS__&click_time=__TS__&device_model=__MODEL__&callback=__CALLBACK_PARAM__";
                }
                break;
            case MediaType::TOUTIAO_PINPAI:
                $action_track_url = "{$domain}/toutiao_pinpai/ods_media_topview_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&os=__OS__&ua=__UA__&ip=__IP__&click_time=__TS__&geo=__GEO__&bdid=__BDID__&reqid=__REQID__";
                break;
            case MediaType::DONGQIUDI:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/dongqiudi/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&os=__OS__&idfa=__IDFA__&imei2=__IMEI__&device_model=__MN__&click_time=__TT__&callback=__CALLBACK_PARAM__";
                } else {
                    $action_track_url = "{$domain}/dongqiudi/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&os=__OS__&imei2=__IMEI__&android_id=__ANDROIDID__&device_model=__MN__&click_time=__TT__&callback=__CALLBACK_PARAM__&oaid=__OAID__";
                }
                break;
            case MediaType::HESHENG:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/hesheng/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&app_id={$param->appid}&request_id={click_id}&click_time={click_time}&idfa={idfa}&ip={ip}&callback={callback}";
                }
                break;
            case MediaType::QUHUAN:
                $action_track_url = "{$domain}/quhuan/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&idfa={idfa}&ip={ip}&callback={callback}";
                break;
            case MediaType::BUPET:
                $action_track_url = "{$domain}/bupet/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&imei={imei}&idfa={idfa}&os={os}&oaid={oaid}&android_id={android_id}&ip={ip}&ua={ua}&callback={callback}&caid_caa={caid}";
                break;
            case MediaType::DAHENG:
                $action_track_url = "{$domain}/daheng/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&idfa={idfa}&ip={ip}&ua={encode[ua]}&callback={encode[calllback]}";
                break;
            case MediaType::TIANCHENG:
                $action_track_url = "{$domain}/tiancheng/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&muid={imei}&moaid={oaid}&android_id={androidId}&ip={ip}&callback={callbackUrl}";
                break;
            case MediaType::WEISHI:
                $action_track_url = "{$domain}/weishi/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&appid={$param->appid}&idfa={idfa}&ip={ip}&callback={callback}";
                break;
            case MediaType::AIPU:
                $action_track_url = "{$domain}/aipu/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&appid={$param->appid}&idfa={idfa}&ip={ip}&callback={callback}";
                break;
            case MediaType::LINDONG:
                $action_track_url = "{$domain}/lindong/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&click_id=\${CLICKID}&muid=\${IMEI_MD5}&oaid=\${OAID}&ip=\${IP}&android_id=\${ANDROIDID_MD5}&os=\${OS}&ua=\${UA}&campaign_id=\${PLANID}&adgroup_id=\${CID}&adcre_id=\${CRID}";
                break;
            case MediaType::BILIBILI:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/bilibili/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&click_id=__TRACKID__&campaign_id=__CAMPAIGNID__&adgroup_id=__UNITID__&adcre_id=__CREATIVEID__&callback=__CALLBACKURL__&caid_caa=__CAID__&muid=__IDFAMD5__&mac=__MAC1__&ip=__IP__&ua=__UA__&device_model=__MODEL__&click_time=__TS__&account_id=__ACCOUNTID__";
                } else {
                    $action_track_url = "{$domain}/bilibili/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&click_id=__TRACKID__&muid=__IMEI__&campaign_id=__CAMPAIGNID__&adgroup_id=__UNITID__&adcre_id=__CREATIVEID__&callback=__CALLBACKURL__&mac=__MAC1__&android_id=__ANDROIDID__&moaid=__OAIDMD5__&ip=__IP__&ua=__UA__&device_model=__MODEL__&click_time=__TS__&account_id=__ACCOUNTID__";
                }
                break;
            case MediaType::QUTOUTIAO:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/qutoutiao/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&adcre_id=__CID__&idfa=__IDFA__&os=__OS__&campaign_id=__PLAN__&adgroup_id=__UNIT__&callback=__CALLBACK_URL__&click_time=__TSMS__&account_id=__UID__&ip=__IP__";
                } else {
                    $action_track_url = "{$domain}/qutoutiao/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&adcre_id=__CID__&muid=__IMEIMD5__&os=__OS__&campaign_id=__PLAN__&adgroup_id=__UNIT__&callback=__CALLBACK_URL__&click_time=__TSMS__&android_id=__ANDROIDIDMD5__&account_id=__UID__&oaid=__OAID__&ip=__IP__";
                }
                break;
            case MediaType::XINMEI:
                $action_track_url = "{$domain}/xinmei/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&idfa=__IDFA__&callback=__CALLBACK_URL__&ip=__IP__";
                break;
            case MediaType::XIANGRONG:
                $action_track_url = "{$domain}/xiangrong/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&idfa={idfa}&ip={ip}&ua={encode[ua]}&callback={encode[calllback]}";
                break;
            case MediaType::MITUTU:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/mitutu/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&idfa={IDFA}&ip={IP}&ua={ua}&callback={callback}";
                } else {
                    $action_track_url = "{$domain}/mitutu/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&imei={imei}&oaid={oaid}&android_id={androidId}&ip={ip}&ua={encode[ua]}&callback={encode[calllback]}";
                }
                break;
            case MediaType::YANGLE:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/yangle/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&idfa={idfa}&ip={ip}&callback={callbackUrl}";
                } else {
                    $action_track_url = "{$domain}/yangle/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&muid={imei}&android_id={androidId}&ip={ip}&callback={callbackUrl}";
                }
                break;
            case MediaType::QIHU_JINGJIA:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/qihujingjia/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&request_id=__UniqueID__&click_time=__clicktime__&ip=__IP__&os=__OS__&device_model=__devicetype__&muid=__imei_md5__&idfa=__IDFA__&mac=__MAC_MD5__&adgroup_id=__groupid__&adcre_id=__creativeid__&callback=__callback_url__";
                } else {
                    $action_track_url = "{$domain}/qihujingjia/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&request_id=__UniqueID__&click_time=__clicktime__&ip=__IP__&os=__OS__&device_model=__devicetype__&muid=__imei_md5__&moaid=__oaid_md5__&mac=__MAC_MD5__&android_id=__ANDROID_ID__&adgroup_id=__groupid__&adcre_id=__creativeid__&callback=__callback_url__";
                }
                break;
            case MediaType::KUAIKANMANHUA:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/kuaikanmanhua/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&mac=__MAC_MD5__&os=__OS__&idfa=__IDFA__&campaign_id=__GROUP_ID__&adgroup_id=__CAMPAIGN_ID__&adcre_id=__CREATIVE_ID__&click_id=__UNIQUE_ID__&account_id=__ADVERTISER_ID__&ip=__IP__&click_time=__TS_SECOND__&callback=__CALLBACK__";
                } else {
                    $action_track_url = "{$domain}/kuaikanmanhua/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&mac=__MAC_MD5__&os=__OS__&android_id=__ANDROID_ID__&imei=__IMEI__&muid=__IMEI_MD5__&oaid=__OAID__&moaid=__OAID_MD5__&campaign_id=__GROUP_ID__&adgroup_id=__CAMPAIGN_ID__&adcre_id=__CREATIVE_ID__&click_id=__UNIQUE_ID__&account_id=__ADVERTISER_ID__&ip=__IP__&click_time=__TS_SECOND__&callback=__CALLBACK__";
                }
                break;
            case MediaType::XIMALAYA:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/ximalaya/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&ip=_IP_&idfa=_IDFA_&os=_OS_&click_time=_TS_&campaign_id=_TASK_ID_&adgroup_id=_PLAN_ID_&adcre_id=_MATERIAL_ID_&callback=_CALLBACK_URL_&ua=_UA_&device_model=_MODEL_";
                } else {
                    $action_track_url = "{$domain}/ximalaya/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&ip=_IP_&muid=_IMEI_MD5_&oaid=_OAID_&android_id=_ANDROIDID_&os=_OS_&click_time=_TS_&campaign_id=_TASK_ID_&adgroup_id=_PLAN_ID_&adcre_id=_MATERIAL_ID_&callback=_CALLBACK_URL_&ua=_UA_&device_model=_MODEL_";
                }
                break;
            case MediaType::TAPTAP:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/taptap/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&idfa={IDFA}&click_time={TIME}&ip={IP}&os={DEVICE}&adgroup_id={ADSET_ID}&adcre_id={CREATIVE_ID}&device_model={DEVICE_MODEL}&callback={DEEP_CALLBACK_URL}&click_id={TAP_TRACK_ID}&external_material_id={TAP_PROJECT_ID}&ua={WEB_UA}&ipv6={IPV6}";
                } else {
                    $action_track_url = "{$domain}/taptap/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&imei={IMEI}&oaid={OAID}&click_time={TIME}&ip={IP}&os={DEVICE}&adgroup_id={ADSET_ID}&adcre_id={CREATIVE_ID}&device_model={DEVICE_MODEL}&callback={DEEP_CALLBACK_URL}&click_id={TAP_TRACK_ID}&external_material_id={TAP_PROJECT_ID}&ua={WEB_UA}&ipv6={IPV6}";
                }
                break;
            case MediaType::WEIXING:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/weixing/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&idfa={IDFA}&ip={IP}&app_id={appid}&callback={callback}";
                }
                break;
            case MediaType::KUAISHOU_PINPAI:
                $action_track_url = "{$domain}/kuaishou_pinpai/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&account_id=__ACCOUNTID__&campaign_id=__DID__&campaign_name=__DNAME__&adgroup_id=__AID__&adcre_id=__CID__&csite=__CSITE__&os=__OS__&click_time=__TS__&ip=__IP__&ua=__UA__&callback=__CALLBACK__&idfa=__IDFA__&caid_caa=__KENYID_CAA__&muid=__IMEI2__&oaid=__OAID__&mac=__MAC__&android_id=__ANDROIDID2__&moaid=__OAID2__&rta_exp_id=__VID__";
                break;
            case MediaType::XIAOQIANQIAN:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/xiaoqianqian/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&idfa={idfa}&callback=\${URLEncoder.encode(\"{url}\",\"utf-8\")}&ip={ip}&ua={ua}";
                }
                break;
            case MediaType::SINA:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/sina/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&campaign_id={GroupId}&adgroup_id={LineitemId}&adcre_id={AdId}&click_id={Uuid}&ip={Ip}&muid={User}&ua={RawUA}";
                } else {
                    $action_track_url = "{$domain}/sina/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&campaign_id={GroupId}&adgroup_id={LineitemId}&adcre_id={AdId}&click_id={Uuid}&ip={Ip}&oaid={Oaid}&muid={User}&ua={RawUA}";
                }
                break;
            case MediaType::LEME:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/leme/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&idfa={idfa}&ip={ip}&ua={user_agent}&callback={callback}";
                } else {
                    $action_track_url = "{$domain}/leme/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&android_id={android_id}&ip={ip}&ua={user_agent}&callback={callback}&imei={aff_sub3}&oaid={aff_sub4}";
                }
                break;
            case MediaType::WEIBO:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/weibo/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&ipv6={ip}&ip={ipv4}&ua={ua}&muid={idfa_MD5}&click_time={clicktime}&campaign_id={CAMPAIGN_ID}&adgroup_id={ad_id}&adcre_id={creative_id}&os={devicetype}&mac={mac_MD5}&device_model={MODEL}&caid_caa={GX_CAID}&click_id={IMP}";
                } else {
                    $action_track_url = "{$domain}/weibo/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&ipv6={ip}&ip={ipv4}&ua={ua}&muid={imei_MD5}&oaid={oaid}&moaid={oaid_MD5}&android_id={androidid_MD5}&click_time={clicktime}&campaign_id={CAMPAIGN_ID}&adgroup_id={ad_id}&adcre_id={creative_id}&os={devicetype}&mac={mac_MD5}&device_model={MODEL}&caid_caa={GX_CAID}&click_id={IMP}";
                }
                if ($param->plat_id == PlatId::MINI) {
                    $action_track_url = "{$domain}/weibo/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&ipv6={ip}&ip={ipv4}&ua={ua}&click_time={clicktime}&adgroup_id={ad_id}&adcre_id={creative_id}&click_id={IMP}";
                }
                break;
            case MediaType::LAHONG:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/lahong/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&idfa={IDFA}&callback={CALLBACK}&ip={ipv4}&ua={UA}";
                }
                break;
            case MediaType::IQIYI:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/iqiyi/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&callback=__CALLBACK_URL__&idfa=__IDFA__&ip=__IP__&os=__OS__&click_time=__TS__&ua=__UA__&campaign_id=__ORDER_GROUP_ID__&adgroup_id=__ORDER_PLAN_ID__&adcre_id=__CREATIVE_ID__&account_id=__ADVERTISER_ID__&caid_caa=%7B%22caids%22%3A%22__CAID__%22%2C%22mcaids%22%3A%22__CAID1__%22%7D&click_id=__IMPRESSS_ID__";
                } else if ($param->convert_source_type === ConvertSourceType::SDK) {
                    $action_track_url = "{$domain}/iqiyi/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&imei=__IMEI__&oaid=__OAID__&android_id=__ANDROIDID__&ip=__IP__&os=__OS__&click_time=__TS__&ua=__UA__&campaign_id=__ORDER_GROUP_ID__&adgroup_id=__ORDER_PLAN_ID__&adcre_id=__CREATIVE_ID__&account_id=__ADVERTISER_ID__&click_id=__IMPRESSS_ID__";
                } else {
                    $action_track_url = "{$domain}/iqiyi/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&callback=__CALLBACK_URL__&imei=__IMEI__&oaid=__OAID__&android_id=__ANDROIDID__&ip=__IP__&os=__OS__&click_time=__TS__&ua=__UA__&campaign_id=__ORDER_GROUP_ID__&adgroup_id=__ORDER_PLAN_ID__&adcre_id=__CREATIVE_ID__&account_id=__ADVERTISER_ID__&click_id=__IMPRESSS_ID__";
                }
                break;
            case MediaType::BAITE:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/baite/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&callback={callback}&idfa={idfa}&click_id={adid}&mac={mac}&ip={ip}&ua={ua}";
                } else {
                    $action_track_url = "{$domain}/baite/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&callback={callback}&imei={imei}&click_id={adid}&mac={mac}&ip={ip}&ua={ua}";
                }
                break;
            case MediaType::YOUJU:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/youju/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&callback={callback}&idfa={idfa}&adgroup_id={appid}";
                }
                break;
            case MediaType::JUGAO:
                if ($param->plat_id == PlatId::MINI) {
                    if ($param->game_type === 'IOS') {
                        $action_track_url = "{$domain}/jugao/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&ip=__IP__&ua=__UA__&os=__OS__&callback=__CALLBACK__&idfa=__IDFA__&campaign_id=__CID__&adgroup_id=__GID__&adcre_id=__CRID__&account_id=__ADVERTISERID__";
                    } else {
                        $action_track_url = "{$domain}/jugao/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&ip=__IP__&ua=__UA__&os=__OS__&callback=__CALLBACK__&muid=__IMEI_MD5__&android_id=__ANDROIDID_MD5__&oaid=__OAID__&campaign_id=__CID__&adgroup_id=__GID__&adcre_id=__CRID__&account_id=__ADVERTISERID__";
                    }
                } else if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/jugao/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&callback={adinall_callback}&idfa={adinall_idfa}&ip={adinall_ip}&click_id={adinall_clickid}";
                } else {
                    $action_track_url = "{$domain}/jugao/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&callback={adinall_callback}&imei={adinall_imei}&android_id={adinall_android_id}&oaid={adinall_oaid}&ua={ua}&ip={adinall_ip}&click_id={adinall_clickid}";
                }
                break;
            case MediaType::YUCHI:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/yuchi/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&callback={callback_url}&ua={agent}&ip={ip}&click_id={click_id}&idfa={ios_idfa}";
                } else {
                    $action_track_url = "{$domain}/yuchi/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&callback={callback_url}&ua={agent}&ip={ip}&click_id={click_id}&imei={imei}&oaid={oaid}";
                }
                break;
            case MediaType::HUOBANYUN:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/huobanyun/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&callback={callback}&ua={ua}&ip={ip}&idfa={idfa}&click_time={ts}&device_model={model}";
                }
                break;
            case MediaType::YOUKU:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/youku/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&idfa=__IDFA__&os=__OS__&ua=__UA__&ip=__IP__&click_id=__TRACKID__&caid_caa=__CAID__&adgroup_id=__ADGROUPID__&adcre_id=__CREATIVEID__";
                } else {
                    $action_track_url = "{$domain}/youku/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&muid=__IMEI__&os=__OS__&ua=__UA__&ip=__IP__&click_id=__TRACKID__&caid_caa=__CAID__&adgroup_id=__ADGROUPID__&adcre_id=__CREATIVEID__&android_id=__ANDROIDID__&oaid=__OAID__";
                }
                break;
            case MediaType::YUNXIANG:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/yunxiang/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&idfa={idfa}&ua={user_agent}&ip={ip}&callback={callback}&caid_caa={caid}";
                } else {
                    $action_track_url = "{$domain}/yunxiang/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&imei={aff_sub3}&ua={user_agent}&ip={ip}&android_id={android_id}&oaid={aff_sub4}&callback={callback}";
                }
                break;
            case MediaType::HUYA:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/huya/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&campaign_id=__PID__&adgroup_id=__UID__&adcre_id=__CID__&os=__OS__&ip=__IP__&ua=__UA__&mac=__MAC__&muid=__IDFA_MD5__&click_time=__TS__&callback=__CALLBACK__&caid_caa=__CAID1__&request_id=__TRACEID__";
                } else {
                    $action_track_url = "{$domain}/huya/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&campaign_id=__PID__&adgroup_id=__UID__&adcre_id=__CID__&os=__OS__&ip=__IP__&ua=__UA__&mac=__MAC__&moaid=__OAID_MD5__&imei=__IMEI__&click_time=__TS__&callback=__CALLBACK__&android_id=__ANDROIDID__&request_id=__TRACEID__";
                }
                break;
            case MediaType::AISIZHUSHOU:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/aisizhushou/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&idfa={idfa}&ip={ip}&ua={ua}}&os={os}&click_time={timestamp}&callback={callback}&device_model={model}";
                }
                break;
            case MediaType::LAITEMOBI:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/laitemobi/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&callback={callback}&idfa={idfa}&app_id={appid}&ip={ip}";
                }
                break;
            case MediaType::MOYING:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/moying/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&callback={callback}&idfa={idfa}&app_id={sonCode}&ip={ip}&ua={ua}";
                }
                break;
            case MediaType::HAODONG:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/haodong/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&callback={callback}&idfa={idfa}&ip={ip}";
                }
                break;
            case MediaType::HAXI:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/haxi/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&callback={callback}&idfa={idfa}&ip={ipv4}&ua={ua}";
                }
                break;
            case MediaType::SIGMOB:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/sigmob/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&muid=_IDFAMD5_&callback=_CALLBACK_&click_id=_CLICKID_&ip=_IP_&ua=_UA_&caid_caa=_CAID_";
                } else {
                    $action_track_url = "{$domain}/sigmob/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&muid=_IMEIMD5_&callback=_CALLBACK_&click_id=_CLICKID_&ip=_IP_&ua=_UA_&moaid=_OAIDMD5_";
                }
                break;
            case MediaType::YURUYOU:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/yuruyou/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&idfa={idfa}&callback={callback}&click_id={clickid}&ip={ip}";
                }
                break;
            case MediaType::FENGHUANG:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/fenghuang/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&idfa=MNT_04_IDFA&ip=MNT_01_IP&os=MNT_00_OS&ua=MNT_18_UA&callback=MNT_08_CALLBACK";
                } else {
                    $action_track_url = "{$domain}/fenghuang/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&imei=MNT_03_IMEI&ip=MNT_01_IP&os=MNT_00_OS&ua=MNT_18_UA&callback=MNT_08_CALLBACK&oaid=MNT_14_OAID";
                }
                break;
            case MediaType::QIMAI:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/qimai/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&appid={appid}&ip={ip}&ua={ua}&callback={callback}&device_model={model}&idfa={idfa}";
                }
                break;
            case MediaType::YUNSI:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/yunsi/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&appid={appid}&idfa={idfa}&callback={callback}";
                }
                break;
            case MediaType::FANZHUO:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/fanzhuo/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&appid={appid}&idfa={idfa}&callback={callback}";
                }
                break;
            case MediaType::QIDIAN:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/qidian/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&idfa={idfa}&ip={ip}&ua={encode[ua]}&callback={encode[calllback]}";
                }
                break;
            case MediaType::NETEASE_YOUDAO:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/netease_youdao/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&click_id=__conv__&idfa=__idfa__&account_id=__sponsor_id__&campaign_id=__campaign_id__&adgroup_id=__group_id__&adcre_id=__content_id__&app_id=__app_id__&ip=__ip__&ua=__ua__&os=__os__&click_time=__ts__&media_conv_action=__conv_action__&caid_caa=__caid__";
                } else {
                    $action_track_url = "{$domain}/netease_youdao/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&click_id=__conv__&muid=__imei__&android_id=__android_id__&oaid=__oaid__&account_id=__sponsor_id__&campaign_id=__campaign_id__&adgroup_id=__group_id__&adcre_id=__content_id__&app_id=__app_id__&ip=__ip__&ua=__ua__&os=__os__&click_time=__ts__&media_conv_action=__conv_action__";
                }
                break;
            case MediaType::HAXI_DOUYU:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/haxi_douyu/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&account_id=[[_DYSOURCE_]]&request_id=[[_DYREQUESTID_]]&muid=[[_DYIDFA_]]&device_model=[[_DYDVERSION_]]&os=[[_DYOSTYPE_]]&ip=[[_DYIP_]]&ua=[[_DYUSERAGENT_]]&callback=[[_DYCALLBACK_]]&click_id=[[_DYID_]]&adgroup_id=[[_DYADID_]]&adcre_id=[[_DYCREATIVEID_]]";
                } else {
                    $action_track_url = "{$domain}/haxi_douyu/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&account_id=[[_DYSOURCE_]]&request_id=[[_DYREQUESTID_]]&device_model=[[_DYDVERSION_]]&os=[[_DYOSTYPE_]]&ip=[[_DYIP_]]&ua=[[_DYUSERAGENT_]]&callback=[[_DYCALLBACK_]]&click_id=[[_DYID_]]&adgroup_id=[[_DYADID_]]&adcre_id=[[_DYCREATIVEID_]]&muid=[[_DYIMEI_]]&moaid=[[_DYOAID_]]&android_id=[[_DYANID_]]";
                }
                break;
            case MediaType::YOUJIAYUAN:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/youjiayuan/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&ip={ipv4}&idfa={idfa}&ua={ua}&callback={callback}";
                } else {
                    $action_track_url = "{$domain}/youjiayuan/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&ip={ip}&imei={imei}&ua=={encode[ua]}&callback={encode[calllback]}&android_id={androidId}&oaid={oaid}";
                }
                break;
            case MediaType::DOUYU:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/douyu/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&account_id=[[_DYSOURCE_]]&request_id=[[_DYREQUESTID_]]&muid=[[_DYIDFA_]]&device_model=[[_DYDVERSION_]]&os=[[_DYOSTYPE_]]&ip=[[_DYIP_]]&ua=[[_DYUSERAGENT_]]&callback=[[_DYCALLBACK_]]&click_id=[[_DYID_]]&adgroup_id=[[_DYADID_]]&adcre_id=[[_DYCREATIVEID_]]";
                } else {
                    $action_track_url = "{$domain}/douyu/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&account_id=[[_DYSOURCE_]]&request_id=[[_DYREQUESTID_]]&device_model=[[_DYDVERSION_]]&os=[[_DYOSTYPE_]]&ip=[[_DYIP_]]&ua=[[_DYUSERAGENT_]]&callback=[[_DYCALLBACK_]]&click_id=[[_DYID_]]&adgroup_id=[[_DYADID_]]&adcre_id=[[_DYCREATIVEID_]]&muid=[[_DYIMEI_]]&moaid=[[_DYOAID_]]&android_id=[[_DYANID_]]";
                }
                break;
            case MediaType::YUCHE:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/yuche/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&appid={appid}&idfa={idfa}&callback={callback}&ip={ip}";
                }
                break;
            case MediaType::HUAWEI:
                if ($param->game_type === '安卓') {
                    $action_track_url = "{$domain}/huawei/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&muid=__UNIQUE_ID__&oaid=__OAID__&callback=__CALLBACK__&click_time=__TS__&device_model=__ID_TYPE__";
                }
                break;
            case MediaType::HEMING:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/heming/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&idfa={idfa}&ip={ip}&ua={encode[ua]}&callback={encode[calllback]}";
                }
                break;
            case MediaType::SEARCH_PINZHUAN:
                $action_track_url = "{$domain}/search_pinzhuan/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&account_id={{USER_ID}}&ip={{IP}}&ua={{UA_ENCODE}}&os={{OS}}&click_time={{TS}}&click_id={{CLICK_ID}}&idfa={{IDFA}}&muid={{IMEI_MD5}}&android_id={{ANDROID_ID_MD5}}&oaid={{OAID}}&campaign_id={{AD_ID}}&adgroup_id={{AU_ID}}&adcre_id={{MU_ID}}&ipv6={{IPV6}}";
                break;
            case MediaType::QIMAO:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/qimao/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&os=__OS__&ip=__IP__&ua=__UA__&callback=__CALLBACK__&click_time=__TS__&account_id=__ADVERTISERID__&campaign_id=__CID__&adgroup_id=__GID__&adcre_id=__CRID__&muid=__IDFA_MD5__";
                } else {
                    $action_track_url = "{$domain}/qimao/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&os=__OS__&ip=__IP__&ua=__UA__&callback=__CALLBACK__&click_time=__TS__&account_id=__ADVERTISERID__&campaign_id=__CID__&adgroup_id=__GID__&adcre_id=__CRID__&muid=__IMEI_MD5__&android_id=__ANDROIDID_MD5__&moaid=__OAID_MD5__";
                }
                break;
            case MediaType::YOUBIKESI:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/youbikesi/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&os=__OS__&ip=__IP__&ua=__UA__&callback=__CALLBACK_PARAM__&campaign_id=__CAMPAIGNID__&adgroup_id=__ADGROUPID__&adcre_id=__CREATIVEID__&muid=__IDFA_MD5__";
                } else {
                    $action_track_url = "{$domain}/youbikesi/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&os=__OS__&ip=__IP__&ua=__UA__&callback=__CALLBACK_PARAM__&campaign_id=__CAMPAIGNID__&adgroup_id=__ADGROUPID__&adcre_id=__CREATIVEID__&muid=__IMEI_MD5__&moaid=__OAID_MD5__";
                }
                break;
            case MediaType::NETEASE_NEWS:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/netease_news/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&os=__OS1__&ip=__IP__&ua=__UA__&callback=__CALLBACK__&adcre_id=__CID__&muid=__IDFA2__&account_id=__ACCOUNTID__&caid_caa=__CAID__";
                } else {
                    $action_track_url = "{$domain}/netease_news/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&os=__OS1__&ip=__IP__&ua=__UA__&callback=__CALLBACK__&adcre_id=__CID__&muid=__IMEIMD5__&moaid=__OAIDMD5__&account_id=__ACCOUNTID__&android_id=__ANDROIDID__";
                }
                break;
            case MediaType::ALIPAY:
                if ($param->plat_id == PlatId::MINI) {
                    $action_track_url = "{$domain}/alipay/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&muid=__IDFA_MD5__&moaid=__OAID_MD5__&request_id=__REQUEST_ID__&click_time=__TS__&os=__OS__&adcre_id=__AD_ID__&adgroup_id=__GROUP_ID__&campaign_id=__PLAN_ID__&account_id=__PRINCIPAL_ID__&callback_param=__CALLBACK_EXT_INFO__&alipay_open_id=__OPENID__&ip=__IP__&ua=__UA__";
                } else {
                    $action_track_url = "{$domain}/alipay/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&muid=__IDFA_MD5__&moaid=__OAID_MD5__&request_id=__REQUEST_ID__&click_time=__TS__&os=__OS__&adcre_id=__AD_ID__&adgroup_id=__GROUP_ID__&campaign_id=__PLAN_ID__&account_id=__PRINCIPAL_ID__&callback_param=__CALLBACK_EXT_INFO__";
                }
                break;
            case MediaType::AD_OWN_CASH:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/ad_own_cash/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&ip=__IPV4__&ipv6=__IPV6__&muid=__IDFA_MD5__&ua=__UA__&click_id=__CONV__&click_time=__TS__&campaign_id=__PLAN_ID__&adgroup_id=__ADID__&adcre_id=__CREATIVE_ID__&device_model=__MODEL__";
                } else {
                    $action_track_url = "{$domain}/ad_own_cash/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&android_id=__ANDROID_ID_MD5__&ip=__IPV4__&ipv6=__IPV6__&muid=__IMEI_MD5__&moaid=__OAID_MD5__&ua=__UA__&click_id=__CONV__&click_time=__TS__&campaign_id=__PLAN_ID__&adgroup_id=__ADID__&adcre_id=__CREATIVE_ID__&device_model=__MODEL__";
                }
                break;
            case MediaType::XINGTU:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/xingtu/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&account_id={$param->account_id}&ip=__IP__&ua=__UA__&os=__OS__&click_time=__TS__&device_model=__MODEL__&callback=__CALLBACK_PARAM__&muid=__IDFA_MD5__&campaign_id=__DEMAND_ID__&adgroup_id=__DEMAND_ID__&adcre_id=__ITEM_ID__";
                } else {
                    $action_track_url = "{$domain}/xingtu/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&account_id={$param->account_id}&ip=__IP__&ua=__UA__&os=__OS__&click_time=__TS__&device_model=__MODEL__&callback=__CALLBACK_PARAM__&muid=__IMEI_MD5__&campaign_id=__DEMAND_ID__&adgroup_id=__DEMAND_ID__&adcre_id=__ITEM_ID__&moaid=__OAID_MD5__&android_id=__ANDROIDID_MD5__";
                }
                break;
            case MediaType::HONOR:
                $action_track_url = "{$domain}/honor/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&ip=__IP__&os=__OS__&oaid=__OAID__&account_id=__ADVERTISER_ID__&click_id=__TRACK_ID__&adcre_id=__CREATIVE_ID__&campaign_id=__CAMPAIGNID__&adgroup_id=__GROUPID__";
                break;
            case MediaType::JINSHOUZHI:
                $action_track_url = "{$domain}/jinshouzhi/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&appid={$param->appid}&idfa={idfa}&callback={callback}";
                break;
            case MediaType::DOUYIN_ENTERPRISE:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/douyin_enterprise/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&ip=__IP__&ua=__UA__&os=__OS__&click_time=__TS__&device_model=__MODEL__&campaign_id=__COMPONENT_ID__&adgroup_id=__COMPONENT_ID__&mix_adcre_id=__ITEM_ID__%2C__ROOM_ID__&aweme_account=__AWEME_ID__&csite=__SCENE__&muid=__IDFA_MD5__";
                } else {
                    $action_track_url = "{$domain}/douyin_enterprise/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&ip=__IP__&ua=__UA__&os=__OS__&click_time=__TS__&device_model=__MODEL__&campaign_id=__COMPONENT_ID__&adgroup_id=__COMPONENT_ID__&mix_adcre_id=__ITEM_ID__%2C__ROOM_ID__&aweme_account=__AWEME_ID__&csite=__SCENE__&android_id=__ANDROIDID_MD5__&muid=__IMEI_MD5__&moaid=__OAID_MD5__";
                }
                break;
            case MediaType::BILIBILI_STAR:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/bilibili_star/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&click_id=__TRACKID__&campaign_id=__CAMPAIGNID__&adgroup_id=__UNITID__&adcre_id=__CREATIVEID__&callback=__CALLBACKURL__&caid_caa=__CAID__&muid=__IDFAMD5__&mac=__MAC1__&ip=__IP__&ua=__UA__&device_model=__MODEL__&click_time=__TS__&account_id=__ACCOUNTID__";
                } else {
                    $action_track_url = "{$domain}/bilibili_star/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&click_id=__TRACKID__&muid=__IMEI__&campaign_id=__CAMPAIGNID__&adgroup_id=__UNITID__&adcre_id=__CREATIVEID__&callback=__CALLBACKURL__&mac=__MAC1__&android_id=__ANDROIDID__&moaid=__OAIDMD5__&ip=__IP__&ua=__UA__&device_model=__MODEL__&click_time=__TS__&account_id=__ACCOUNTID__";
                }
                break;
            case MediaType::WEILAIYOU:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/weilaiyou/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&appid={appid}&idfa={idfa}&callback={callback}";
                }
                break;
            case MediaType::ZHIHU:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/zhihu/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&ip=__IP__&ua=__UA__&os=__OS__&click_time=__TS__&device_model=__MODEL__&callback=__CALLBACK__&muid=__IDFA__&campaign_id=__CAMPAIGN__&adgroup_id=__ADID__&adcre_id=__CREATIVE__&account_id=__ADVERTISER__&zhs2s=1&caid_caa=__CAID_VER1__%2C__CAID_VER2__";
                } else {
                    $action_track_url = "{$domain}/zhihu/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&ip=__IP__&ua=__UA__&os=__OS__&click_time=__TS__&device_model=__MODEL__&callback=__CALLBACK__&muid=__IMEI__&oaid=__OAID__&android_id=__ANDROIDID__&campaign_id=__CAMPAIGN__&adgroup_id=__ADID__&adcre_id=__CREATIVE__&account_id=__ADVERTISER__&zhs2s=1";
                }
                break;
            case MediaType::BILIBILI_LY:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/bilibili_ly/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&click_id=__TRACKID__&campaign_id=__CAMPAIGNID__&adgroup_id=__UNITID__&adcre_id=__CREATIVEID__&callback=__CALLBACKURL__&caid_caa=__CAID__&muid=__IDFAMD5__&mac=__MAC1__&ip=__IP__&ua=__UA__&device_model=__MODEL__&click_time=__TS__&account_id=__ACCOUNTID__";
                } else {
                    $action_track_url = "{$domain}/bilibili_ly/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&click_id=__TRACKID__&muid=__IMEI__&campaign_id=__CAMPAIGNID__&adgroup_id=__UNITID__&adcre_id=__CREATIVEID__&callback=__CALLBACKURL__&mac=__MAC1__&android_id=__ANDROIDID__&moaid=__OAIDMD5__&ip=__IP__&ua=__UA__&device_model=__MODEL__&click_time=__TS__&account_id=__ACCOUNTID__";
                }
                break;
            case MediaType::BRBT:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/brbt/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&ip={ip}&ua={ua}&callback={callback}&idfa={idfa}";
                } else {
                    $action_track_url = "{$domain}/brbt/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&ip={ip}&ua={ua}&callback={callback}&android_id={androidIdMd5}&imei={imei}&oaid={oaid}";
                }
                break;
            case MediaType::LONGZHU:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/longzhu/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&appid={$param->appid}&idfa={idfa}&ip={ip}&callback={callbackurl}";
                }
                break;
            case MediaType::OUAI:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/ouai/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&appid={$param->appid}&idfa={idfa}&ip={ip}&callback={callback}";
                }
                break;
            case MediaType::XINGTU_AD:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/xingtu_ad/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&ip=__IP__&ua=__UA__&os=__OS__&click_time=__TS__&device_model=__MODEL__&callback=__CALLBACK_PARAM__&muid=__IDFA_MD5__&campaign_id=__DEMAND_ID__&adgroup_id=__DEMAND_ID__&adcre_id=__ITEM_ID__";
                } else {
                    $action_track_url = "{$domain}/xingtu_ad/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&ip=__IP__&ua=__UA__&os=__OS__&click_time=__TS__&device_model=__MODEL__&callback=__CALLBACK_PARAM__&muid=__IMEI_MD5__&campaign_id=__DEMAND_ID__&adgroup_id=__DEMAND_ID__&adcre_id=__ITEM_ID__&moaid=__OAID_MD5__&android_id=__ANDROIDID_MD5__";
                }
                break;
            case MediaType::TANGTANG_TEC:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/tangtang_tec/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&appid={$param->appid}&idfa={idfa}&callback={callback}";
                }
                break;
            case MediaType::XINCHENG_BOX:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/xincheng_box/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&ip={ip}&callback={callbackUrl}&idfa={idfa}";
                } else {
                    $action_track_url = "{$domain}/xincheng_box/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&ip={ip}&callback={callbackUrl}&android_id={androidId}&muid={imei}&moaid={oaid}";
                }
                break;
            case MediaType::MENGYOU:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/mengyou/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&appid={$param->appid}&idfa={idfa}&click_time={timestamp}&ip={ip}";
                }
                break;
            case MediaType::DOUBAN:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/douban/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&ip=__ip__&ua=__ua__&os=__os__&click_time=__ts__&device_model=__model__&idfa=__idfa__&campaign_id=__campaign_id__&adgroup_id=__group_id__&adcre_id=__creative_id__&account_id=__sponsor_id__&callback=__conv__&external_material_id=__device_id__&request_id=__req_id__&caid_caa=__caid__";
                } else {
                    $action_track_url = "{$domain}/douban/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&ip=__ip__&ua=__ua__&os=__os__&click_time=__ts__&device_model=__model__&imei=__imei__&campaign_id=__campaign_id__&adgroup_id=__group_id__&adcre_id=__creative_id__&oaid=__oaid__&android_id=__android_id__&account_id=__sponsor_id__&callback=__conv__&external_material_id=__device_id__&request_id=__req_id__";
                }
                break;
            case MediaType::ADX:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/adx/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&ip={ip}&ua={ua}&idfa={idfa}&callback={callback}click_id={clickid}";
                } else {
                    $action_track_url = "{$domain}/adx/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&ip={ip}&ua={ua}&imei={imei}&oaid={oaid}&android_id={androidid}&callback={callback}&click_id={clickid}";
                }
                break;
            case MediaType::WEIBO_PINXUAN:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/weibo_pinxuan/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&ipv6={ip}&ip={ipv4}&ua={ua}&muid={idfa_MD5}&click_time={clicktime}&campaign_id={CAMPAIGN_ID}&adgroup_id={ad_id}&adcre_id={creative_id}&os={devicetype}&mac={mac_MD5}&device_model={MODEL}&caid_caa={GX_CAID}&click_id={IMP}";
                } else {
                    $action_track_url = "{$domain}/weibo_pinxuan/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&ipv6={ip}&ip={ipv4}&ua={ua}&muid={imei_MD5}&oaid={oaid}&moaid={oaid_MD5}&android_id={androidid_MD5}&click_time={clicktime}&campaign_id={CAMPAIGN_ID}&adgroup_id={ad_id}&adcre_id={creative_id}&os={devicetype}&mac={mac_MD5}&device_model={MODEL}&caid_caa={GX_CAID}&click_id={IMP}";
                }
                break;
            case MediaType::WEIBO_STAR:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/weibo_star/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&ipv6={ip}&ip={ipv4}&ua={ua}&muid={idfa_MD5}&click_time={clicktime}&campaign_id={CAMPAIGN_ID}&adgroup_id={ad_id}&adcre_id={creative_id}&os={devicetype}&mac={mac_MD5}&device_model={MODEL}&caid_caa={GX_CAID}&click_id={IMP}";
                } else {
                    $action_track_url = "{$domain}/weibo_star/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&ipv6={ip}&ip={ipv4}&ua={ua}&muid={imei_MD5}&oaid={oaid}&moaid={oaid_MD5}&android_id={androidid_MD5}&click_time={clicktime}&campaign_id={CAMPAIGN_ID}&adgroup_id={ad_id}&adcre_id={creative_id}&os={devicetype}&mac={mac_MD5}&device_model={MODEL}&caid_caa={GX_CAID}&click_id={IMP}";
                }
                break;
            case MediaType::KUAISHOU_STAR:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/kuaishou_star/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&account_id=__ACCOUNTID__&campaign_id=__DID__&campaign_name=__DNAME__&adgroup_id=__AID__&adcre_id=__CID__&csite=__CSITE__&os=__OS__&click_time=__TS__&muid=__IDFA2__&ip=__IP__&ua=__UA__&callback=__CALLBACK__&caid_caa=__KENYID_CAA__&rta_exp_id=__VID__";
                } else {
                    $action_track_url = "{$domain}/kuaishou_star/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&account_id=__ACCOUNTID__&campaign_id=__DID__&campaign_name=__DNAME__&adgroup_id=__AID__&adcre_id=__CID__&csite=__CSITE__&os=__OS__&click_time=__TS__&muid=__IMEI2__&oaid=__OAID__&mac=__MAC__&android_id=__ANDROIDID2__&ip=__IP__&ua=__UA__&callback=__CALLBACK__&moaid=__OAID2__&rta_exp_id=__VID__";
                }
                break;
            case MediaType::XINGTU_STAR:
                $action_track_url = "{$domain}/xingtu_star/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&ip=__IP__&ua=__UA__&os=__OS__&click_time=__TS__";
                break;
            case MediaType::HUAWEI_JINGHONG:
                $action_track_url = "{$domain}/huawei_jinghong/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&ip={ip}&ua={user_agent}&click_time={trace_time}&campaign_id={campaign_id}&adgroup_id={adgroup_id}&adcre_id={content_id}&callback={callback}&os={os_version}&oaid={oaid}";
                break;
            case MediaType::OPPO:
                if ($param->game_type === '安卓') {
                    $action_track_url = "{$domain}/oppo/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&os=\$os$&device_model=\$m$&ip=__IP__&click_time=__TS__&muid=\$im$&oaid=__OAID__&account_id=\$ownerid$&campaign_id=\$planid$&adgroup_id=\$groupid$&adcre_id=\$ad$";
                }
                break;
            case MediaType::XIAOHONGSHU:
                if ($param->plat_id == PlatId::MINI) {
                    $action_track_url = "{$domain}/xiaohongshu/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&account_id=__ADVERTISER_ID__&campaign_id=__CAMPAIGN_ID__&adgroup_id=__UNIT_ID__&adcre_id=__CREATIVITY_ID__&click_id=__CLICK_ID__";
                } elseif ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/xiaohongshu/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&account_id=__ADVERTISER_ID__&campaign_id=__CAMPAIGN_ID__&adgroup_id=__UNIT_ID__&adcre_id=__CREATIVITY_ID__&os=__OS__&muid=__IDFA_MD5__&ip=__IP__&app_id=__APP_ID__&caid_caa=__CAID_MD5__&click_id=__CLICK_ID__&click_time=__TS__";
                }
                break;
            case MediaType::KUAISHOU_JUXING:
                if ($param->plat_id == PlatId::MINI) {
                    if ($param->game_type === 'IOS') {
                        $action_track_url = "{$domain}/kuaishou_juxing/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&campaign_id=__MISSION_ID__&adgroup_id=__ORDER_ID__&adcre_id=__ORDER_ID__&click_time=__TS__&muid=__IDFA2__&ip=__IP__&ua=__UA__&callback=__CALLBACK__";
                    } else {
                        $action_track_url = "{$domain}/kuaishou_juxing/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&campaign_id=__MISSION_ID__&adgroup_id=__ORDER_ID__&adcre_id=__ORDER_ID__&click_time=__TS__&muid=__IMEI2__&ip=__IP__&ua=__UA__&callback=__CALLBACK__&oaid=__OAID2__&android_id=__ANDROIDID2__";
                    }
                }
                break;
            case MediaType::VIVO:
                $action_track_url = "{$domain}/vivo/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&ip=__IP__&os=__OS__&moaid=__OAID__&account_id=__ADVERTISERID__&muid=__IMEI__&ua=__UA__&android_id=__ANDROIDID__&click_time=__TS__&external_material_id=__CREATIVEID__";
                break;
            case MediaType::JIUXIANG:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/jiuxiang/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&appid={appid}&ip={ip}&callback={callbackurl}&idfa={idfa}";
                }
                break;
            case MediaType::HANDAN_DALIANG:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/handan_daliang/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&ip={ip}&callback={callback}&idfa={idfa}";
                }
                break;
            case MediaType::XINGTU_CPS:
                $action_track_url = "{$domain}/xingtu_cps/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&ip={ip}&callback={callback}&idfa={idfa}&ua={user_agent}&imei={aff_sub3}&oaid={aff_sub4}&android_id={android_id}";
                break;
            case MediaType::YANYANG:
                $action_track_url = "{$domain}/yanyang/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&ip=__IP__&ua=__UA__&idfa=__IDFA__&callback=__CALLBACK_URL__&android_id=__ANDROID_ID_MD5__&click_time=__EVENT_TIME__&campaign_id=__CAMPAIGN_ID__&adgroup_id=__ADGROUP_ID__&adcre_id=__CREATIVE_ID__&account_id=__ACCOUNT_ID__&moaid=__OAID_MD5__&imei=__IMEI__";
                break;
            case MediaType::MANXING:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/manxing/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&idfa={idfa}&ip={ip}&ua={encode[ua]}&callback={encode[calllback]}";
                } else {
                    $action_track_url = "{$domain}/manxing/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&oaid={oaid}&android_id={anid}&device_model={model}&ip={ip}&ua={ua}&callback={callback_url}&click_id={clkid}";
                }
                break;
            case MediaType::ZHISUO:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/zhisuo/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&ip={ipv4}&callback={ecodeUrl(callback)}&idfa={idfa}";
                }
                break;
            case MediaType::HUANYU:
                $action_track_url = "{$domain}/huanyu/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&imei={imei}&oaid={oaid}&idfa={idfa}&ip={ip}&ua={ua}&callback={callback}";
                break;
            case MediaType::IPSYSKE:
                $action_track_url = "{$domain}/ipsyske/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&idfa={idfa}&ip={ip}&ua={ua}&callback={callback}&android_id={android_id}";
                break;
            case MediaType::YIDENGYUN:
                $action_track_url = "{$domain}/yidengyun/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&idfa={idfa}&ip={ip}&ua={ua}&callback={callback}&android_id={androidid}&oaid={oaid}&imei={imei}&click_time={ts}";
                break;
            case MediaType::BILIBILI_LIVE:
                if ($param->game_type === 'IOS') {
                    $action_track_url = "{$domain}/bilibili_live/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&click_id=__TRACKID__&campaign_id=__CAMPAIGNID__&adgroup_id=__UNITID__&adcre_id=__CREATIVEID__&callback=__CALLBACKURL__&caid_caa=__CAID__&muid=__IDFAMD5__&mac=__MAC1__&ip=__IP__&ua=__UA__&device_model=__MODEL__&click_time=__TS__&account_id=__ACCOUNTID__";
                } else {
                    $action_track_url = "{$domain}/bilibili_live/ods_media_click_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&click_id=__TRACKID__&muid=__IMEI__&campaign_id=__CAMPAIGNID__&adgroup_id=__UNITID__&adcre_id=__CREATIVEID__&callback=__CALLBACKURL__&mac=__MAC1__&android_id=__ANDROIDID__&moaid=__OAIDMD5__&ip=__IP__&ua=__UA__&device_model=__MODEL__&click_time=__TS__&account_id=__ACCOUNTID__";
                }
                break;
            default:
                break;
        }
        return $action_track_url;
    }

    protected function getDisplayTrackURL(SiteConfigParam $param, $site_data)
    {
        $display_track_url = '';
        $domain = $this->display_track_domain;

        switch ($param->media_type) {
            case MediaType::TOUTIAO:
                if ($param->action_track_type === ActionTrackType::DEFAULT) {
                    if ($param->game_type === 'IOS') {
                        $display_track_url = "{$domain}/toutiao/ods_media_show_log/{$param->platform}/log.jsp?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&idfa=__IDFA__&os=__OS__&callback=__CALLBACK_URL__&show_time=__TS__&adgroup_id=__AID__&adgroup_name=__AID_NAME__&campaign_id=__CAMPAIGN_ID__&adcre_id=__CID__&csite=__CSITE__&ip=__IP__&ua=__UA__&union_site=__UNION_SITE__&geo=__GEO__&device_model=__MODEL__&rta_id=__RTA_TRACE_ID__&outer_target_id=__RTAID__";
                    } else {
                        $display_track_url = "{$domain}/toutiao/ods_media_show_log/{$param->platform}/log.jsp?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&muid=__IMEI__&android_id=__ANDROIDID__&os=__OS__&callback=__CALLBACK_URL__&show_time=__TS__&adgroup_id=__AID__&adgroup_name=__AID_NAME__&campaign_id=__CAMPAIGN_ID__&adcre_id=__CID__&csite=__CSITE__&ip=__IP__&ua=__UA__&union_site=__UNION_SITE__&geo=__GEO__&device_model=__MODEL__&rta_id=__RTA_TRACE_ID__&outer_target_id=__RTAID__";
                    }
                } else {
                    if ($param->game_type === 'IOS') {
                        $display_track_url = "{$domain}/toutiao/ods_media_show_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&idfa=__IDFA__&os=__OS__&callback=__CALLBACK_URL__&adgroup_id=__PROMOTION_ID__&campaign_id=__PROJECT_ID__&adcre_id=__CID__&csite=__CSITE__&ctype=__CTYPE__&ip=__IP__&mac=__MAC1__&ua=__UA__&union_site=__UNION_SITE__&geo=__GEO__&device_model=__MODEL__&request_id=__REQUEST_ID__&rta_id=__RTA_TRACE_ID__&outer_target_id=__RTAID__&rta_exp_id=__VID__&muid=__IDFA_MD5__&image_material_id=__MID1__&title_material_id=__MID2__&video_material_id=__MID3__&playable_material_id=__MID4__&external_material_id=__MID5__&weburl_material_id=__MID6__&port_version=2.0&show_time=__TS__";
                    } else {
                        $display_track_url = "{$domain}/toutiao/ods_media_show_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&muid=__IMEI__&android_id=__ANDROIDID__&os=__OS__&callback=__CALLBACK_URL__&adgroup_id=__PROMOTION_ID__&campaign_id=__PROJECT_ID__&adcre_id=__CID__&csite=__CSITE__&oaid=__OAID__&ctype=__CTYPE__&ip=__IP__&mac=__MAC1__&ua=__UA__&union_site=__UNION_SITE__&geo=__GEO__&device_model=__MODEL__&request_id=__REQUEST_ID__&rta_id=__RTA_TRACE_ID__&outer_target_id=__RTAID__&rta_exp_id=__VID__&image_material_id=__MID1__&title_material_id=__MID2__&video_material_id=__MID3__&playable_material_id=__MID4__&external_material_id=__MID5__&weburl_material_id=__MID6__&port_version=2.0&show_time=__TS__";
                    }
                    if (in_array($param->plat_id, [PlatId::DY_MINI, PlatId::DY_MINI_PROGRAM])) {
                        if ($param->game_type === 'IOS') {
                            $display_track_url = "{$domain}/toutiao/ods_media_show_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&idfa=__IDFA__&os=__OS__&callback=__CALLBACK_URL__&adgroup_id=__PROMOTION_ID__&adgroup_name=__AID_NAME__&campaign_id=__PROJECT_ID__&adcre_id=__CID__&csite=__CSITE__&ctype=__CTYPE__&ip=__IP__&mac=__MAC1__&ua=__UA__&union_site=__UNION_SITE__&geo=__GEO__&device_model=__MODEL__&request_id=__REQUEST_ID__&rta_id=__RTA_TRACE_ID__&outer_target_id=__RTAID__&rta_exp_id=__VID__&muid=__IDFA_MD5__&image_material_id=__MID1__&title_material_id=__MID2__&video_material_id=__MID3__&playable_material_id=__MID4__&external_material_id=__MID5__&weburl_material_id=__MID6__&aweme_mini_open_id=__OPENID__&port_version=2.0&show_time=__TS__";
                        } else {
                            $display_track_url = "{$domain}/toutiao/ods_media_show_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&muid=__IMEI__&android_id=__ANDROIDID__&os=__OS__&callback=__CALLBACK_URL__&adgroup_id=__PROMOTION_ID__&campaign_id=__PROJECT_ID__&adcre_id=__CID__&csite=__CSITE__&oaid=__OAID__&ctype=__CTYPE__&ip=__IP__&mac=__MAC1__&ua=__UA__&union_site=__UNION_SITE__&geo=__GEO__&device_model=__MODEL__&request_id=__REQUEST_ID__&rta_id=__RTA_TRACE_ID__&outer_target_id=__RTAID__&rta_exp_id=__VID__&image_material_id=__MID1__&title_material_id=__MID2__&video_material_id=__MID3__&playable_material_id=__MID4__&external_material_id=__MID5__&weburl_material_id=__MID6__&aweme_mini_open_id=__OPENID__&port_version=2.0&show_time=__TS__";
                        }
                    }
                }
                break;
            case MediaType::NETEASE:
                if ($param->game_type === 'IOS') {
                    $display_track_url = "{$domain}/netease/ods_media_show_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&idfa=__IDFA__&os=__OS__&ua=__UA__&ip=__IP__&show_time=__CLICKTIME__&click_id=__CLICKID__&account_id=__SPONSOR_ID__&campaign_id=__PLAN_ID__&adgroup_id=__ADID__&adcre_id=__CREATIVE_ID__";
                } else {
                    $display_track_url = "{$domain}/netease/ods_media_show_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&imei=__IMEI__&os=__OS__&ua=__UA__&ip=__IP__&show_time=__CLICKTIME__&click_id=__CLICKID__&account_id=__SPONSOR_ID__&campaign_id=__PLAN_ID__&adgroup_id=__ADID__&adcre_id=__CREATIVE_ID__&android_id=__ANDROIDID__&moaid=__OAIDMD5__";
                }
                break;
            case MediaType::KUAISHOU:
                if ($param->game_type === 'IOS') {
                    $display_track_url = "{$domain}/kuaishou/ods_media_show_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&account_id=__ACCOUNTID__&campaign_id=__DID__&campaign_name=__DNAME__&adgroup_id=__AID__&adcre_id=__CID__&csite=__CSITE__&os=__OS__&muid=__IDFA2__&ip=__IP__&ua=__UA__&callback=__CALLBACK__&rta_exp_id=__VID__&show_time=__TS__";
                } else {
                    $display_track_url = "{$domain}/kuaishou/ods_media_show_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&account_id=__ACCOUNTID__&campaign_id=__DID__&campaign_name=__DNAME__&adgroup_id=__AID__&adcre_id=__CID__&csite=__CSITE__&os=__OS__&muid=__IMEI2__&oaid=__OAID__&mac=__MAC__&android_id=__ANDROIDID2__&ip=__IP__&ua=__UA__&callback=__CALLBACK__&moaid=__OAID2__&rta_exp_id=__VID__&show_time=__TS__";
                }
                break;
            case MediaType::KUAISHOU_PINPAI:
                $display_track_url = "{$domain}/kuaishou_pinpai/ods_media_show_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&account_id=__ACCOUNTID__&campaign_id=__DID__&campaign_name=__DNAME__&adgroup_id=__AID__&adcre_id=__CID__&csite=__CSITE__&os=__OS__&ip=__IP__&ua=__UA__&callback=__CALLBACK__&idfa=__IDFA__&muid=__IMEI2__&oaid=__OAID__&mac=__MAC__&android_id=__ANDROIDID2__&moaid=__OAID2__&rta_exp_id=__VID__&show_time=__TS__";
                break;
            case MediaType::BAIDU:
                if ($param->plat_id == PlatId::MINI) {
                    if ($param->game_type === 'IOS') {
                        $display_track_url = "{$domain}/baidu/ods_media_show_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&campaign_id={{PLAN_ID}}&adgroup_id={{UNIT_ID}}&adcre_id={{IDEA_ID}}&account_id={{USER_ID}}&ip={{IP}}&ua={{UA}}&os={{OS}}&idfa={{IDFA}}&external_material_id={{COMBID}}&show_time={{TS}}&click_id={{CLICK_ID}}";
                    } else {
                        $display_track_url = "{$domain}/baidu/ods_media_show_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&campaign_id={{PLAN_ID}}&adgroup_id={{UNIT_ID}}&adcre_id={{IDEA_ID}}&account_id={{USER_ID}}&ip={{IP}}&ua={{UA}}&os={{OS}}&muid={{IMEI_MD5}}&android_id={{ANDROID_ID_MD5}}&oaid={{OAID}}&external_material_id={{COMBID}}&show_time={{TS}}&click_id={{CLICK_ID}}";
                    }
                } else if ($param->game_type === 'IOS') {
                    $display_track_url = "{$domain}/baidu/ods_media_show_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&account_id={{USER_ID}}&idfa={{IDFA}}&mac={{MAC_MD5}}&os={{OS}}&ip={{IP}}&ua={{UA}}&campaign_id={{PLAN_ID}}&adgroup_id={{UNIT_ID}}&adcre_id={{IDEA_ID}}&deeplink_url={{DEEPLINK_URL}}&callback={{CALLBACK_URL}}&sign={{SIGN}}&external_material_id={{COMBID}}&show_time={{TS}}&click_id={{CLICK_ID}}";
                } else if ($param->convert_source_type === ConvertSourceType::SDK) {
                    $display_track_url = "{$domain}/baidu/ods_media_show_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&android_id={{ANDROID_ID_MD5}}&account_id={{USER_ID}}&muid={{IMEI_MD5}}&mac={{MAC_MD5}}&oaid={{OAID}}&moaid={{OAID_MD5}}&os={{OS}}&ip={{IP}}&ua={{UA}}&campaign_id={{PLAN_ID}}&adgroup_id={{UNIT_ID}}&adcre_id={{IDEA_ID}}&deeplink_url={{DEEPLINK_URL}}&sign={{SIGN}}&external_material_id={{COMBID}}&show_time={{TS}}&click_id={{CLICK_ID}}";
                } else {
                    $display_track_url = "{$domain}/baidu/ods_media_show_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&android_id={{ANDROID_ID_MD5}}&account_id={{USER_ID}}&muid={{IMEI_MD5}}&mac={{MAC_MD5}}&oaid={{OAID}}&moaid={{OAID_MD5}}&os={{OS}}&ip={{IP}}&ua={{UA}}&campaign_id={{PLAN_ID}}&adgroup_id={{UNIT_ID}}&adcre_id={{IDEA_ID}}&deeplink_url={{DEEPLINK_URL}}&callback={{CALLBACK_URL}}&sign={{SIGN}}&external_material_id={{COMBID}}&show_time={{TS}}&click_id={{CLICK_ID}}";
                }
                break;
            case MediaType::BAIDU_SEARCH:
                if ($param->plat_id == PlatId::MINI) {
                    if ($param->game_type === 'IOS') {
                        $display_track_url = "{$domain}/baidu_search/ods_media_show_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&campaign_id={{PLAN_ID}}&adgroup_id={{UNIT_ID}}&adcre_id={{IDEA_ID}}&account_id={{USER_ID}}&ip={{IP}}&ua={{UA}}&os={{OS}}&idfa={{IDFA}}&external_material_id={{COMBID}}&show_time={{TS}}&click_id={{CLICK_ID}}";
                    } else {
                        $display_track_url = "{$domain}/baidu_search/ods_media_show_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&campaign_id={{PLAN_ID}}&adgroup_id={{UNIT_ID}}&adcre_id={{IDEA_ID}}&account_id={{USER_ID}}&ip={{IP}}&ua={{UA}}&os={{OS}}&muid={{IMEI_MD5}}&android_id={{ANDROID_ID_MD5}}&oaid={{OAID}}&external_material_id={{COMBID}}&show_time={{TS}}&click_id={{CLICK_ID}}";
                    }
                } else if ($param->game_type === 'IOS') {
                    $display_track_url = "{$domain}/baidu_search/ods_media_show_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&account_id={{USER_ID}}&idfa={{IDFA}}&mac={{MAC_MD5}}&os={{OS}}&ip={{IP}}&ua={{UA}}&campaign_id={{PLAN_ID}}&adgroup_id={{UNIT_ID}}&adcre_id={{IDEA_ID}}&deeplink_url={{DEEPLINK_URL}}&callback={{CALLBACK_URL}}&sign={{SIGN}}&external_material_id={{COMBID}}&show_time={{TS}}&click_id={{CLICK_ID}}";
                } else if ($param->convert_source_type === ConvertSourceType::SDK) {
                    $display_track_url = "{$domain}/baidu_search/ods_media_show_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&android_id={{ANDROID_ID_MD5}}&account_id={{USER_ID}}&muid={{IMEI_MD5}}&mac={{MAC_MD5}}&oaid={{OAID}}&moaid={{OAID_MD5}}&os={{OS}}&ip={{IP}}&ua={{UA}}&campaign_id={{PLAN_ID}}&adgroup_id={{UNIT_ID}}&adcre_id={{IDEA_ID}}&deeplink_url={{DEEPLINK_URL}}&sign={{SIGN}}&external_material_id={{COMBID}}&show_time={{TS}}&click_id={{CLICK_ID}}";
                } else {
                    $display_track_url = "{$domain}/baidu_search/ods_media_show_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&android_id={{ANDROID_ID_MD5}}&account_id={{USER_ID}}&muid={{IMEI_MD5}}&mac={{MAC_MD5}}&oaid={{OAID}}&moaid={{OAID_MD5}}&os={{OS}}&ip={{IP}}&ua={{UA}}&campaign_id={{PLAN_ID}}&adgroup_id={{UNIT_ID}}&adcre_id={{IDEA_ID}}&deeplink_url={{DEEPLINK_URL}}&callback={{CALLBACK_URL}}&sign={{SIGN}}&external_material_id={{COMBID}}&show_time={{TS}}&click_id={{CLICK_ID}}";
                }
                break;
            case MediaType::TENCENT:
            case MediaType::MEDIA_SCHEDULE:
            case MediaType::CHANNELS_LIVE:
                if ($param->action_track_type === ActionTrackType::DEFAULT) {
                    if ($param->game_type === 'IOS') {
                        $display_track_url = "{$domain}/gdt/ods_media_show_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&account_id=__ACCOUNT_ID__&os=__DEVICE_OS_TYPE__&callback=__CALLBACK__&deeplink_url=__UNIVERSAL_LINK__&campaign_id=__CAMPAIGN_ID__&adgroup_id=__ADGROUP_ID__&adcre_id=__AD_ID__&csite=__SITE_SET_NAME__&ad_platform_type=__AD_PLATFORM_TYPE__&ip=__IP__&ua=__USER_AGENT__&muid=__MUID__&app_id=__PROMOTED_OBJECT_ID__&click_cost=__REAL_COST__&ctype=__PROMOTED_OBJECT_TYPE__&ipv6=__IPV6__&request_id=__REQUEST_ID__&impression_id=__IMPRESSION_ID__&rta_id=__RTA_TRACE_ID__&rta_exp_id=__RTA_EXP_ID__&union_site=__ENCRYPTED_POSITION_ID__&rta_valid_features=__RTA_VALID_FEATURES__&outer_target_id=__RTA_TARGET_ID__&ipv6=__IPV6__&port_version=1.0&show_time=__IMPRESSION_TIME__";
                    } else {
                        $display_track_url = "{$domain}/gdt/ods_media_show_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&android_id=__HASH_ANDROID_ID__&account_id=__ACCOUNT_ID__&os=__DEVICE_OS_TYPE__&callback=__CALLBACK__&deeplink_url=__DEEPLINK_URL__&campaign_id=__CAMPAIGN_ID__&adgroup_id=__ADGROUP_ID__&adcre_id=__AD_ID__&csite=__SITE_SET_NAME__&ad_platform_type=__AD_PLATFORM_TYPE__&ip=__IP__&ua=__USER_AGENT__&muid=__MUID__&oaid=__OAID__&app_id=__PROMOTED_OBJECT_ID__&click_cost=__REAL_COST__&ctype=__PROMOTED_OBJECT_TYPE__&ipv6=__IPV6__&moaid=__HASH_OAID__&request_id=__REQUEST_ID__&impression_id=__IMPRESSION_ID__&rta_id=__RTA_TRACE_ID__&rta_exp_id=__RTA_EXP_ID__&union_site=__ENCRYPTED_POSITION_ID__&rta_valid_features=__RTA_VALID_FEATURES__&outer_target_id=__RTA_TARGET_ID__&ipv6=__IPV6__&port_version=1.0&show_time=__IMPRESSION_TIME__";
                    }
                    if ($param->plat_id == PlatId::MINI) {
                        $display_track_url = "{$domain}/gdt/ods_media_show_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&rta_exp_id=__PKAM_EXP_IDS__&os=__DEVICE_OS_TYPE__&request_id=__REQUEST_ID__&csite=__SITE_SET__&ctype=__AD_PLATFORM_TYPE__&click_cost=__REAL_COST__&account_id=__ACCOUNT_ID__&adcre_id=__AD_ID__&callback=__CALLBACK__&union_site=__ENCRYPTED_POSITION_ID__&campaign_id=__CAMPAIGN_ID__&impression_id=__IMPRESSION_ID__&wechat_open_id=__WECHAT_OPEN_ID__&adgroup_id=__ADGROUP_ID__&app_id=__PROMOTED_OBJECT_ID__&port_version=1.0&show_time=__IMPRESSION_TIME__";
                    }
                } else if ($param->action_track_type === ActionTrackType::TENCENT_ADA) {
                    if ($param->game_type === 'IOS') {
                        $display_track_url = "{$domain}/gdt/ods_media_show_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&account_id=__ACCOUNT_ID__&os=__DEVICE_OS_TYPE__&callback=__CALLBACK__&deeplink_url=__UNIVERSAL_LINK__&campaign_id=__PROJECT_ID__&csite=__SITE_SET_NAME__&ad_platform_type=__AD_PLATFORM_TYPE__&ip=__IP__&ua=__USER_AGENT__&muid=__MUID__&app_id=__PROMOTED_OBJECT_ID__&click_cost=__REAL_COST__&ctype=__PROMOTED_OBJECT_TYPE__&ipv6=__IPV6__&request_id=__REQUEST_ID__&impression_id=__IMPRESSION_ID__&rta_id=__RTA_TRACE_ID__&rta_exp_id=__RTA_EXP_ID__&union_site=__ENCRYPTED_POSITION_ID__&rta_valid_features=__RTA_VALID_FEATURES__&outer_target_id=__RTA_TARGET_ID__&ipv6=__IPV6__&video_material_id=__ELEMENT_INFO__&port_version=2.0&show_time=__IMPRESSION_TIME__";
                    } else {
                        $display_track_url = "{$domain}/gdt/ods_media_show_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&android_id=__HASH_ANDROID_ID__&account_id=__ACCOUNT_ID__&os=__DEVICE_OS_TYPE__&callback=__CALLBACK__&deeplink_url=__DEEPLINK_URL__&campaign_id=__PROJECT_ID__&csite=__SITE_SET_NAME__&ad_platform_type=__AD_PLATFORM_TYPE__&ip=__IP__&ua=__USER_AGENT__&muid=__MUID__&oaid=__OAID__&app_id=__PROMOTED_OBJECT_ID__&click_cost=__REAL_COST__&ctype=__PROMOTED_OBJECT_TYPE__&ipv6=__IPV6__&moaid=__HASH_OAID__&request_id=__REQUEST_ID__&impression_id=__IMPRESSION_ID__&rta_id=__RTA_TRACE_ID__&rta_exp_id=__RTA_EXP_ID__&union_site=__ENCRYPTED_POSITION_ID__&rta_valid_features=__RTA_VALID_FEATURES__&outer_target_id=__RTA_TARGET_ID__&ipv6=__IPV6__&video_material_id=__ELEMENT_INFO__&port_version=2.0&show_time=__IMPRESSION_TIME__";
                    }
                    if ($param->plat_id == PlatId::MINI) {
                        $display_track_url = "{$domain}/gdt/ods_media_show_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&rta_exp_id=__PKAM_EXP_IDS__&os=__DEVICE_OS_TYPE__&request_id=__REQUEST_ID__&csite=__SITE_SET__&ctype=__AD_PLATFORM_TYPE__&click_cost=__REAL_COST__&account_id=__ACCOUNT_ID__&callback=__CALLBACK__&union_site=__ENCRYPTED_POSITION_ID__&campaign_id=__PROJECT_ID__&impression_id=__IMPRESSION_ID__&wechat_open_id=__WECHAT_OPEN_ID__&app_id=__PROMOTED_OBJECT_ID__&video_material_id=__ELEMENT_INFO__&port_version=2.0&show_time=__IMPRESSION_TIME__";
                    }
                } else {
                    if ($param->game_type === 'IOS') {
                        $display_track_url = "{$domain}/gdt/ods_media_show_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&account_id=__ACCOUNT_ID__&os=__DEVICE_OS_TYPE__&callback=__CALLBACK__&deeplink_url=__UNIVERSAL_LINK__&campaign_id=__ADGROUP_ID__&adgroup_id=__ADGROUP_ID__&adcre_id=__DYNAMIC_CREATIVE_ID__&csite=__SITE_SET_NAME__&ad_platform_type=__AD_PLATFORM_TYPE__&ip=__IP__&ua=__USER_AGENT__&muid=__MUID__&app_id=__PROMOTED_OBJECT_ID__&click_cost=__REAL_COST__&ctype=__PROMOTED_OBJECT_TYPE__&ipv6=__IPV6__&request_id=__REQUEST_ID__&impression_id=__IMPRESSION_ID__&rta_id=__RTA_TRACE_ID__&rta_exp_id=__RTA_EXP_ID__&union_site=__ENCRYPTED_POSITION_ID__&rta_valid_features=__RTA_VALID_FEATURES__&outer_target_id=__RTA_TARGET_ID__&ipv6=__IPV6__&video_material_id=__ELEMENT_INFO__&show_time=__IMPRESSION_TIME__&port_version=3.0";
                    } else {
                        $display_track_url = "{$domain}/gdt/ods_media_show_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&android_id=__HASH_ANDROID_ID__&account_id=__ACCOUNT_ID__&os=__DEVICE_OS_TYPE__&callback=__CALLBACK__&deeplink_url=__DEEPLINK_URL__&campaign_id=__ADGROUP_ID__&adgroup_id=__ADGROUP_ID__&adcre_id=__DYNAMIC_CREATIVE_ID__&csite=__SITE_SET_NAME__&ad_platform_type=__AD_PLATFORM_TYPE__&ip=__IP__&ua=__USER_AGENT__&muid=__MUID__&oaid=__OAID__&app_id=__PROMOTED_OBJECT_ID__&click_cost=__REAL_COST__&ctype=__PROMOTED_OBJECT_TYPE__&ipv6=__IPV6__&moaid=__HASH_OAID__&request_id=__REQUEST_ID__&impression_id=__IMPRESSION_ID__&rta_id=__RTA_TRACE_ID__&rta_exp_id=__RTA_EXP_ID__&union_site=__ENCRYPTED_POSITION_ID__&rta_valid_features=__RTA_VALID_FEATURES__&outer_target_id=__RTA_TARGET_ID__&ipv6=__IPV6__&video_material_id=__ELEMENT_INFO__&show_time=__IMPRESSION_TIME__&port_version=3.0";
                    }
                    if ($param->plat_id == PlatId::MINI) {
                        $display_track_url = "{$domain}/gdt/ods_media_show_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&rta_exp_id=__PKAM_EXP_IDS__&os=__DEVICE_OS_TYPE__&request_id=__REQUEST_ID__&csite=__SITE_SET__&ctype=__AD_PLATFORM_TYPE__&click_cost=__REAL_COST__&account_id=__ACCOUNT_ID__&callback=__CALLBACK__&union_site=__ENCRYPTED_POSITION_ID__&campaign_id=__ADGROUP_ID__&adgroup_id=__ADGROUP_ID__&adcre_id=__DYNAMIC_CREATIVE_ID__&impression_id=__IMPRESSION_ID__&wechat_open_id=__WECHAT_OPEN_ID__&app_id=__PROMOTED_OBJECT_ID__&video_material_id=__ELEMENT_INFO__&show_time=__IMPRESSION_TIME__&port_version=3.0";
                    }
                }
                break;
            case MediaType::AD_OWN_CASH:
                if ($param->game_type === 'IOS') {
                    $display_track_url = "{$domain}/ad_own_cash/ods_media_show_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&ip=__IPV4__&ipv6=__IPV6__&muid=__IDFA_MD5__&ua=__UA__&campaign_id=__PLAN_ID__&adgroup_id=__ADID__&adcre_id=__CREATIVE_ID__&device_model=__MODEL__";
                } else {
                    $display_track_url = "{$domain}/ad_own_cash/ods_media_show_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&android_id=__ANDROID_ID_MD5__&ip=__IPV4__&ipv6=__IPV6__&muid=__IMEI_MD5__&moaid=__OAID_MD5__&ua=__UA__&campaign_id=__PLAN_ID__&adgroup_id=__ADID__&adcre_id=__CREATIVE_ID__&device_model=__MODEL__";
                }
                break;
        }
        return $display_track_url;
    }

    /**
     * 检查是否用内部监测链接
     * @param SiteConfigParam $param
     * @return bool
     */
    public function isInsideReport(SiteConfigParam $param)
    {
        if ($param->media_type === MediaType::TENCENT && (int)$param->plat_id === PlatId::MINI) {
            return true;
        }
        return false;
    }

    /**
     * 获取微信公众号token
     * @param $app_id
     * @return array
     */
    abstract public function getWechatAppToken($app_id);

    /* ------------- 广告变现 ------------- */
    /**
     * @param ADMonetizationGroupParam $param
     * @return array
     */
    abstract public function addADMonetizationGroup(ADMonetizationGroupParam $param);

    abstract public function editADMonetizationGroup(ADMonetizationGroupParam $param);

    /**
     * @param ADMonetizationGroupParam $param
     * @return array
     */
    abstract public function AddADMonetizationPlan(ADMonetizationPlanParam $param);

    abstract public function editADMonetizationPlan(ADMonetizationPlanParam $param);

    abstract public function batchUPADMonetizationPlan($planIds, $state, $user_name);
    /* ----------------------------------- */

    /**
     * 获取预约监测链接
     * @param SiteConfigParam $param
     * @param $site_data
     * @return string
     */
    protected function getSubscribeTrackUrl(SiteConfigParam $param, $site_data)
    {
        $subscribe_track_url = '';
        $domain = $this->action_track_domain;

        switch ($param->media_type) {
            case MediaType::FANZHUO:
                $subscribe_track_url = "{$domain}/fanzhuo/ods_media_clue_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&idfa={idfa}&appid={appid}&source={source}";
                break;
            case MediaType::YUNSI:
                $subscribe_track_url = "{$domain}/yunsi/ods_media_clue_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&idfa={idfa}&appid={appid}&source={source}";
                break;
            case MediaType::YUCHE:
                $subscribe_track_url = "{$domain}/yuche/ods_media_clue_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&idfa={idfa}&appid={appid}&source=yuche";
                break;
            case MediaType::WEILAIYOU:
                $subscribe_track_url = "{$domain}/weilaiyou/ods_media_clue_log/{$param->platform}/log.gif?game_id={$param->game_id}&agent_id={$param->agent_id}&site_id={$site_data['site_id']}&idfa={idfa}&appid={appid}&source=weilaiyou";
                break;
        }
        return $subscribe_track_url;
    }
}
