<?php

namespace App\Service\GroupAssistant;

use App\Exception\AppException;
use App\Model\HttpModel\Feishu\AbstractFeishuModel;
use App\Model\HttpModel\Feishu\AuthModel;
use App\Model\HttpModel\Feishu\Docs\ContentModel;
use App\Model\HttpModel\Feishu\Docs\FileModel;
use App\Model\HttpModel\Feishu\Docs\WikiModel;
use App\Model\HttpModel\Feishu\Drive\ImportExportTaskModel;
use App\Model\HttpModel\Feishu\IM\MessageModel;
use App\Model\HttpModel\Volcengine\Knowledge\AbstractKnowledgeModel;
use App\Model\HttpModel\Volcengine\Knowledge\DocModel;
use App\Model\HttpModel\Volcengine\Knowledge\PointModel;
use App\Model\SqlModel\Zeda\FeiShuDocMessagePointIDRelationModel;
use App\Model\SqlModel\Zeda\FeiShuDocsTokenUpdateTimeModel;
use App\Model\SqlModel\Zeda\FeiShuMessageDocsModel;
use App\Model\SqlModel\Zeda\FeiShuMessageModel;
use App\Model\SqlModel\Zeda\FeishuMessageSummary;
use App\Service\FeiShuService;
use App\Service\FileToMarkdown;
use App\Struct\RedisCache;
use App\Task\DataBotTask;
use App\Utils\Helpers;
use Common\EnvConfig;
use Illuminate\Support\Collection;
use RedisException;

class MessageFormat
{

    /**
     * 处理文本，识别并在匹配到的飞书云文档链接后追加自定义内容
     *
     * @param string $text 输入的文本内容
     * @param string $message_id 消息id
     * @return string      处理后的文本内容
     */
    static public function handlerText(string $text, string $message_id, &$cut_off, $update)
    {
        $logger = Helpers::getLogger('doc_resolve');
        // 修改后的正则表达式，排除常见的标点符号
//        $pattern = '/(https?:\/\/[^\/]+\.feishu\.cn\/(file|docs|docx|sheets|base|wiki)\/([A-Za-z0-9]+))(?:[?#][a-zA-Z0-9\-_&=%.]*)?/iu';
        $pattern = '/(https?:\/\/[^\/]+\.(?:feishu\.cn|larkoffice\.com)\/(file|docs|docx|sheets|base|wiki)\/([A-Za-z0-9]+))(?:[?#][a-zA-Z0-9\-_&=%.]*)?/iu';


        // 使用 preg_replace_callback 进行匹配和替换
        return preg_replace_callback($pattern, function ($matches) use ($logger, $text, $message_id, &$cut_off, $update) {
            $full_url = $matches[0];           // 完整的匹配 URL，不包括尾随的标点符号
            $type = strtolower($matches[2]);  // 文档类型，转为小写
            $token = $matches[3];             // 文档的 token

            $logger->info('匹配飞书云文档成功', ['message_id' => $message_id, 'text' => $text, 'full_url' => $full_url, 'type' => $type, 'token' => $token]);

            $filename = '';
            $doc_content = self::getDocContent($type, $token, $filename, $message_id, $update);

            $logger->info('文档内容读取结果：', ['message_id' => $message_id, 'text' => $text, 'doc_content' => mb_substr($doc_content, 0, 1000)]);
            if ($doc_content) {
                $cut_off = 0;  // 标识它是匹配到了云文档，不需要截断
                // 返回原 URL 加上文档内容，使用换行符分隔
                $tag = "{$type}_{$token}";
                return "{$full_url}\n这是一份云文档链接，云文档名称：{$filename}。云文档内容如下：\n<$tag>\n{$doc_content}\n</$tag>";
            } else {
                return $full_url;
            }

        }, $text);

    }

    /**
     * 处理图片消息
     *
     * @param $message_id
     * @param $file_key
     * @param int $level 级别 不能一直无限递归处理
     * @param bool $post post的消息不需要自动重试 外部处理
     * @return mixed|string
     */
    static public function handlerImage($message_id, $file_key, int $level = 1, bool $post = false)
    {
        $access_token = (new AuthModel())->tenantAccessTokenByAppType(AbstractFeishuModel::INTERNAL, GroupAssistantService::APP_ID, GroupAssistantService::APP_SECRET);
        $logger = Helpers::getLogger('feishu_resources_img');
        $filename = $file_key . '.png';
        $res = "[图片]";
        $message_model = new MessageModel();
        try {
            $logger->info('需要下载图片,文件名：' . $filename, ['message_id' => $message_id]);
            // 获取图片。
            $file_info = $message_model->getMessageResources($message_id, $file_key, $filename, $access_token);
            if ($file_info['code'] === 0) {
                $logger->info('图片下载成功：' . $filename);
                $abs_filename = $file_info['tmp_file_path'];
                // 获取图片的内容
                $logger->info('开始去获取图片内容', ['message_id' => $message_id]);
                $res = FileToMarkdown::convert($abs_filename);
                $logger->info('图片内容获取完成', ['message_id' => $message_id]);

                // 如果图片的内容超过1000个字符，则要去LLM获取总结
                if (mb_strlen($res, 'UTF-8') > 1000) {
                    $logger->info('需要去总结图片内容', ['message_id' => $message_id]);
                    $summary = self::docSummary($res, $message_id);
                    // 说明总结失败
                    if (!trim($summary)) {
                        $res = mb_substr($res, 0, 1000);
                        $logger->error('总结图片败', ['message_id' => $message_id, 'file_name' => $filename, 'file_key' => $file_key]);
                    } else {
                        $logger->info('图片内容总结完成', ['message_id' => $message_id]);
                        $res = $summary;
                    }
                }

                @unlink($abs_filename);
            }

            // 这种情况大概率是转发的消息导致的，那么需要重新获取一下消息内容，去再处理一遍
            if ($file_info['code'] == 234003 && $level === 1 && $post === false) {
                // 从接口拉取消息详情
                $message_detail = self::getMessageDetail($message_id);
                if ($message_detail && isset($message_detail['data']['items'][0])) {
                    $message_detail = $message_detail['data']['items'][0];
                    $content = json_decode($message_detail['body']['content'], true);
                    return self::handlerImage($message_id, $content['image_key'], 2);
                }
            }


        } catch (AppException $exception) {
            $logger->error($exception->getMessage(), ['message_id' => $message_id]);
        }

        return $res;
    }

    /**
     * 处理文件消息
     *
     * @param $message_id
     * @param $file_key
     * @param $filename
     * @param $cut_off
     * @param int $level
     * @return mixed|string
     */
    static public function handlerFile($message_id, $file_key, $filename, &$cut_off, int $level = 1)
    {
        $access_token = (new AuthModel())->tenantAccessTokenByAppType(AbstractFeishuModel::INTERNAL,
            GroupAssistantService::APP_ID, GroupAssistantService::APP_SECRET);
        $logger = Helpers::getLogger('feishu_resources_file');

        $message_model = new MessageModel();
        $res = "[文件]";
        // 获取文件的内容
        try {
            $logger->info('需要下载文件,文件名：' . $filename, ['message_id' => $message_id, 'file_key' => $file_key]);
            // 获取文件内容。
            $file_info = $message_model->getMessageResources($message_id, $file_key, $filename, $access_token);

            // 判断一下状态码
            if ($file_info['code'] === 0) {
                $logger->info('文件下载成功', ['message_id' => $message_id, 'file_name' => $filename, 'file_key' => $file_key]);
                $abs_filename = $file_info['tmp_file_path'];

                $logger->info('开始转换文件内容', ['message_id' => $message_id, 'file_name' => $filename, 'file_key' => $file_key, 'abs_filename' => $abs_filename]);
                $res = FileToMarkdown::convert($abs_filename);
                $logger->info('文件内容转换成功', ['message_id' => $message_id, 'file_name' => $filename, 'file_key' => $file_key]);
                // 如果文件内容超过1000个字符，则要去LLM获取总结 分段总结
                if (mb_strlen($res, 'UTF-8') > 1000) {
                    $logger->info('开始文件内容总结', ['message_id' => $message_id, 'file_name' => $filename, 'file_key' => $file_key]);
                    $summary = self::docSummary($res, $message_id);
                    // 说明总结失败
                    if (!trim($summary)) {
                        $res = mb_substr($res, 0, 1000);
                        $logger->error('文件总结失败', ['message_id' => $message_id, 'file_name' => $filename, 'file_key' => $file_key]);
                    } else {
                        // 文档内容用标签包起来，方便后面替换
                        $doc_type = 'file';
                        $token = $file_key;
                        $tag = "{$doc_type}_{$token}";
                        $res = "<$tag>\n{$summary}\n</$tag>";

                        $logger->info('文件总结成功', ['message_id' => $message_id, 'file_name' => $filename, 'file_key' => $file_key]);

                        // 这种情况下还需要把文件上传到知识库
                        // 对齐云文档那边的逻辑
                        $extension = pathinfo($filename, PATHINFO_EXTENSION);
                        if ($extension === 'md') {
                            $extension = 'markdown';
                        }
                        $file_info = [
                            'filename'       => $filename,
                            'cn_filename'    => $filename,
                            'file_extension' => $extension,
                        ];
                        try {
                            $logger->info('开始上传到知识库', ['message_id' => $message_id, 'file_info' => $file_info]);
                            self::uploadKnowledgeBase($doc_type, $token, $file_info);
                            $logger->info('知识库上传完成', ['message_id' => $message_id, 'file_info' => $file_info]);
                            // 入库
                            (new FeiShuMessageDocsModel())->relaceAdd(['message_id' => $message_id, 'doc_type' => $doc_type, 'doc_token' => $token, 'type' => 2]);
                            $cut_off = 0;
                        } catch (\Exception $exception) {
                            $logger->error('知识库上传失败', ['message_id' => $message_id, 'file_info' => $file_info]);
                        }
                    }
                }

                // 延迟删除
                (new FileDeletionManager())->addFileToQueue($abs_filename);
            }


            // 这种情况大概率是转发的消息导致的，那么需要重新获取一下消息内容，去再处理一遍
            if ($file_info['code'] == 234003 && $level === 1) {
                // 从接口拉取消息详情
                $message_detail = self::getMessageDetail($message_id);
                if ($message_detail && isset($message_detail['data']['items'][0])) {
                    $message_detail = $message_detail['data']['items'][0];
                    $content = json_decode($message_detail['body']['content'], true);
                    return self::handlerFile($message_id, $content['file_key'], $content['file_name'], $cut_off, 2);
                }
            }


        } catch (\Throwable $exception) {
            $logger->error($exception->getMessage(), ['s' => $exception->getTraceAsString()]);
        }
        $logger->info('文件处理完成', ['message_id' => $message_id, 'file_name' => $filename, 'file_key' => $file_key]);
        return $res;
    }


    /**
     * 处理语音消息
     *
     * @param $message_id
     * @param $file_key
     * @param $access_token
     * @return mixed|string
     */
    static public function handlerAudio($message_id, $file_key, $access_token)
    {
        try {
            return FeiShuService::handleAudio($message_id, $file_key, $access_token);
        } catch (AppException $exception) {
            // 转换失败 兜底
            return "[语音消息]";
        }
    }

    /**
     * 处理post格式的消息
     */
    static public function handlerPost($message_id, $content, &$cut_off, $update = false, $level = 1)
    {
        $tenant_access_token = (new AuthModel())->tenantAccessTokenByAppType(AbstractFeishuModel::INTERNAL, GroupAssistantService::APP_ID, GroupAssistantService::APP_SECRET);
        foreach ($content['content'] as &$items) {
            if (empty($items)) {
                continue;
            }
            foreach ($items as &$item) {
                if ($item['tag'] === 'at') {
                    $item['text'] = "@{$item['user_name']}";
                }
                if ($item['tag'] === 'img' && $item['image_key']) {
                    $img_desc = MessageFormat::handlerImage($message_id, $item['image_key'], $level, true);
                    if ($img_desc === '[图片]' && $level === 1) {
                        try {
                            $message_detail = (new MessageModel())->getMessageDetail($message_id, $tenant_access_token);
                            if (isset($message_detail['data']['items'][0])) {
                                $message_detail = $message_detail['data']['items'][0];
                                $content = json_decode($message_detail['body']['content'], true);
                                return self::handlerPost($message_id, $content, $cut_off, $update, 2);
                            }
                        } catch (AppException $exception) {
                            Helpers::getLogger('feishu_resources_img')->warning('获取消息详情失败', ['message_id' => $message_id, 's' => $exception->getTraceAsString(), 'error_message' => $exception->getMessage()]);
                        }

                    }
                    $item['text'] = $img_desc;
                }
                if ($item['tag'] === 'text') {
                    $item['text'] = MessageFormat::handlerText($item['text'], $message_id, $cut_off, $update);
                }
                // 超链接
                if ($item['tag'] === 'a') {
                    $item['text'] = MessageFormat::handlerText($item['href'], $message_id, $cut_off, $update);
                }
                // 其他类型原样保留
            }
        }
        return json_encode($content, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    }


    /**
     * 根据type和token去获取云文档内容
     *
     * @param string $type 文档类型（如 file, docs, docx, sheets, base, wiki）
     * @param string $token 文档的 token
     * @param string $filename 文档的 名称
     * @param string $message_id 消息id
     * @return string        文档内容
     */
    static public function getDocContent(string &$type, string &$token, string &$filename, string $message_id, $update)
    {
        // 先获取access_token
        $tenant_access_token = (new AuthModel())->tenantAccessTokenByAppType(AbstractFeishuModel::INTERNAL,
            GroupAssistantService::APP_ID, GroupAssistantService::APP_SECRET);

        // 根据 type 和 token 获取内容
        try {
            switch ($type) {
                // 这几种类型暂不支持
                case 'file':
                default:
                    Helpers::getLogger('doc_resolve')->info("$type 类型不处理。", ['message_id' => $message_id]);
                    return false;
                case 'base':
                    $file_extension = 'xlsx';
                    $type = 'bitable';
                    break;
                case 'sheets':
                    $type = 'sheet';
                    $file_extension = 'xlsx';
                    break;
                case 'docs':
                    $type = 'doc';
                    $file_extension = 'docx';
                    break;
                case 'docx':
                    $file_extension = 'docx';
                    break;
                case 'wiki':
                    // 先获取节点
                    $node_info = (new WikiModel())->getNode($tenant_access_token, $token);
                    if ($node_info['code'] == 0) {
                        $node = $node_info['data']['node'];
                        // 更新token和type
                        $token = $node['obj_token'];
                        $type = $node['obj_type'];
                        Helpers::getLogger('doc_resolve')->info("获取到了节点信息", ['message_id' => $message_id, 'type' => $type, 'token' => $token]);
                        if ($type === 'bitable') {
                            $file_extension = 'xlsx';
                        } elseif ($type === 'sheet') {
                            $file_extension = 'xlsx';
                        } elseif ($type === 'doc') {
                            // 注意这里的doc跟上面的docs是不一样的 实际上是同一个东西
                            $file_extension = 'docx';
                        } elseif ($type === 'docx') {
                            $file_extension = 'docx';
                        } else {
                            Helpers::getLogger('doc_resolve')->info("$type 类型不处理。", ['message_id' => $message_id]);
                            return false;
                        }

                    } else {
                        Helpers::getLogger('doc_resolve')->info("获取节点信息失败", ['message_id' => $message_id, 'node_info' => $node_info]);
                        return false;
                    }
                    break;
            }

            // 说明是电子表格，需要去获取sub_id
            $sheet_id = '';
            if ($type === 'sheet') {
                $sheets_info = (new FileModel())->sheetsQuery($tenant_access_token, $token);
                $sheet_id = $sheets_info['data']['sheets'][0]['sheet_id'];
            }

            // 判断库里有没有存在过这个文档
            $db_doc_info = (new FeiShuDocsTokenUpdateTimeModel())->getLastUpdateTimestamp($type, $token);
            if ($db_doc_info->isNotEmpty() && $update === false) {
                Helpers::getLogger('doc_resolve')->info("文档库已经存在，直接使用", ['message_id' => $message_id, 'doc_type' => $type, 'doc_token' => $token]);
                // 存在，则用库里的内容就行了
                $summary = $db_doc_info->first()->summary;
                // 获取到内容之后，需要关联一下消息id和这份云文档的关系
                (new FeiShuMessageDocsModel())->relaceAdd(['message_id' => $message_id, 'doc_type' => $type, 'doc_token' => $token]);

                return $summary;
            }

            /**
             * 不存在，或者是更新，则要上传到知识库等操作
             */
            // 下载文件 有些因为权限问题下载不了的 还需要去获取一下纯文本
            $transform = false;
            $content = '';
            try {
                $file_info = FeiShuService::downExportFile($type, $token, $file_extension, $sheet_id);
                if (!$file_info) {
                    Helpers::getLogger('doc_resolve')->error("文件下载失败", ['message_id' => $message_id, 'doc_type' => $type, 'doc_token' => $token]);
                    return false;
                }
                $filename = $file_info['cn_filename'];
                $transform = true;
            } catch (AppException $exception) {
                Helpers::getLogger('doc_resolve')->error("文件下载失败,尝试获取纯文本信息", ['message_id' => $message_id, 'doc_type' => $type, 'doc_token' => $token]);
                $content = self::getDocTextContent($type, $token, $message_id);
                if (!$content) {
                    Helpers::getLogger('doc_resolve')->error("文件获取纯文本失败", ['message_id' => $message_id, 'doc_type' => $type, 'doc_token' => $token]);
                    return false;
                }

                // 如果获取成功，则需要对齐$file_info
                $filename = $type . '_' . $token . ".md";
                $tmp_file_path = ImportExportTaskModel::FEISHU_RESOURCES . "/$filename";
                file_put_contents($tmp_file_path, $content, LOCK_EX);

                $file_info = [
                    'filename'       => $filename,
                    'file_extension' => 'markdown',
                    'cn_filename'    => $filename,
                    'abs_filename'   => $tmp_file_path,
                ];
                Helpers::getLogger('doc_resolve')->info("获取纯文本信息成功", ['message_id' => $message_id, 'doc_type' => $type, 'doc_token' => $token]);
            }


            Helpers::getLogger('doc_resolve')->info("文件下载成功，开始上传到知识库", ['message_id' => $message_id, 'file_info' => $file_info]);
            // 上传到知识库
            self::uploadKnowledgeBase($type, $token, $file_info);
            Helpers::getLogger('doc_resolve')->info("上传到知识库成功", ['message_id' => $message_id, 'file_info' => $file_info]);


            if ($transform) {
                Helpers::getLogger('doc_resolve')->info("开始转换文件内容", ['message_id' => $message_id, 'file_info' => $file_info]);
                // 下载到了文件，去转换成markdown
                $content = FileToMarkdown::convert($file_info['abs_filename']);
                Helpers::getLogger('doc_resolve')->info("文件内容转换成功。去总结文件", ['message_id' => $message_id, 'file_info' => $file_info]);
            }

            // 文件总结，里面会有重试机制
            $summary = self::docSummary($content, $message_id);
            if (!trim($summary)) {
                // 达到最大重试次数仍然失败
                Helpers::getLogger('doc_resolve')->error("文件总结失败。", ['message_id' => $message_id, 'content' => $content]);
                return false;
            }

            Helpers::getLogger('doc_resolve')->info("文件总结成功", ['message_id' => $message_id, 'file_info' => $file_info]);

            // 入库
            (new FeiShuMessageDocsModel())->relaceAdd(['message_id' => $message_id, 'doc_type' => $type, 'doc_token' => $token]);
            (new FeiShuDocsTokenUpdateTimeModel())->relaceAdd(['last_update_timestamp' => time(), 'doc_type' => $type, 'doc_token' => $token, 'summary' => $summary]);


            // 延迟删除
            (new FileDeletionManager())->addFileToQueue($file_info['abs_filename']);
            // 删除临时文件夹(如果有）
            Helpers::deleteDirectory('extracted_' . $file_info['abs_filename']);

            return $summary;

        } catch (\Throwable $exception) {
            Helpers::getLogger('doc_resolve')->error('其他错误，错误信息：' . $exception->getMessage(), ['message_id' => $message_id, 's' => $exception->getTraceAsString()]);
            return false;
        }

    }

    /**
     * 读取纯文本内容，当链接不可下载时，则用这个读取
     *
     * @param string $type
     * @param string $token
     * @param string $message_id
     * @return string
     */
    static public function getDocTextContent(string $type, string $token, string $message_id): string
    {
        $content = '';
        // 先获取access_token
        $tenant_access_token = (new AuthModel())->tenantAccessTokenByAppType(AbstractFeishuModel::INTERNAL,
            GroupAssistantService::APP_ID, GroupAssistantService::APP_SECRET);

        // 判断object type
        if ($type === 'docx') {
            // 新版文档
            $res = (new ContentModel())->getDocsContent($tenant_access_token, $token);
            $content = $res['data']['content'];
        } else if ($type === 'doc') {
            // 旧版文档
            $res = (new ContentModel())->getOldDocTextContent($tenant_access_token, $token);
            $content = $res['data']['content'];
        } else {
            Helpers::getLogger('doc_resolve')->info("$type 类型不处理。", ['message_id' => $message_id]);
        }

        return $content;
    }

    /**
     * 分段总结文件内容（云文档通用）
     *
     * @param $content
     * @param string $message_id
     * @return string
     */
    static public function docSummary($content, string $message_id = '')
    {
        $max_length = 200000;
        $chunk_size = 60000;
        // 文件内容的总结，如果文件过大，要分批次去总结
        // 先截断超过20万字符的部分
        $truncated_content = mb_strlen($content) > $max_length ? mb_substr($content, 0, $max_length) : $content;

        // 分割成 60000 字符的块
        $chunks = mb_str_split($truncated_content, $chunk_size, "UTF-8");


        $summary_content_list = [];

        $group_assistant_service = new GroupAssistantService();

        $max_retry = 5;
        foreach ($chunks as $index => $chunk) {
            // 把前面总结的内容当前背景信息
            $last_summary = implode("\n", $summary_content_list);
            // 去总结
            Helpers::getLogger('doc_resolve')->info("第{$index}文件总结开始", ['message_id' => $message_id]);

            // 每个文件总结如果失败则重试， 重试5次
            $retry = 1;
            $summary = '';
            while ($retry <= $max_retry) {
                $summary = $group_assistant_service->docSummary($last_summary, $chunk);
                if ($summary) {
                    // 如果成功，跳出循环
                    break;
                }
                // 如果失败，记录日志并增加重试次数
                Helpers::getLogger('doc_resolve')->info("文件总结失败。重试第{$retry}次", ['message_id' => $message_id]);
                $retry++;
            }

            if (!$summary) {
                // 达到最大重试次数仍然失败
                Helpers::getLogger('doc_resolve')->error("文件总结失败，已达最大重试次数。", ['message_id' => $message_id, 'content' => $content]);
                continue;
            }


            $summary_content_list[] = $summary;
            Helpers::getLogger('doc_resolve')->info("第{$index}文件总结结束", ['message_id' => $message_id]);
        }

        return implode("\n", $summary_content_list);

    }


    /**
     * 上传文档到知识库
     *
     * @return void
     */
    static public function uploadKnowledgeBase($doc_type, $token, $file_info)
    {
        $doc_id = $doc_type . '_' . $token;
        // 文档名称需要唯一，所以添加随机数前缀
        $doc_name = uniqid() . $file_info['cn_filename'];


        // 等待一下文档状态，防止有其他操作 这里可以直接覆盖上传，不需要删除
        $doc_info = (new MessageSummary())->waitStatus($doc_id, 600, AbstractKnowledgeModel::DOC_RESOURCE_ID);
        $filename = $file_info['filename'];

        // 写完上传 为了防止cdn缓存，增加随机数
        $url = EnvConfig::DOMAIN . '/upload/summary/' . $filename . "?timestamp=" . time();


        $res = (new DocModel())->add($doc_id, $doc_name, $url, [], $file_info['file_extension'], AbstractKnowledgeModel::DOC_RESOURCE_ID);
        if ($res['code'] == 0) {
            Helpers::getLogger('doc_resolve')->info('文件上传成功', ['res' => $res]);
        } else {
            Helpers::getLogger('doc_resolve')->error('文件上传失败', ['res' => $res, 'doc_id' => $doc_id, 'doc_name' => $doc_name]);
            throw new AppException($res['message']);
        }

    }


    protected function batchQueryMetas($request_data)
    {
        // 先获取access_token
        $tenant_access_token = (new AuthModel())->tenantAccessTokenByAppType(AbstractFeishuModel::INTERNAL,
            GroupAssistantService::APP_ID, GroupAssistantService::APP_SECRET);
        $res_metas_list = (new FileModel())->getMetas($tenant_access_token, $request_data);
        $metas_list = $res_metas_list['data']['metas'];

        // 做一个索引
        $res = [];
        foreach ($metas_list as $item) {
            $key = $item['doc_token'] . '_' . $item['doc_type'];
            $res[$key] = $item;
        }

        return $res;
    }


    /**
     * 飞书云文档更新的逻辑处理
     *
     * @param $doc_type
     * @param $doc_token
     * @return void
     */
    public function handlerFileUpdate($doc_type, $doc_token)
    {
        $logger = Helpers::getLogger('doc_update');
        $feishu_message_docs_model = new FeiShuMessageDocsModel();
        $relation_model = new FeiShuDocMessagePointIDRelationModel();
        $message_id_list = $feishu_message_docs_model->getMessageIdByDocToken($doc_token, $doc_type);

        // 消息id去重
        $message_id_list = $message_id_list->pluck('message_id')->unique()->toArray();
        $logger->info('根据文档获取到的消息id,处理消息', ['message_id_list' => $message_id_list, 'doc_type' => $doc_type, 'doc_token' => $doc_token]);


        // 1.找到云文档对应的消息id,然后更新消息内容个
        $this->updateMessage($message_id_list);

        $logger->info('消息处理完成：', ['message_id_list' => $message_id_list, 'doc_type' => $doc_type, 'doc_token' => $doc_token]);

        // 2.再找到消息id对应的总结内容，去更新总结切片
        $relation_list = $relation_model->getListByMessageId($message_id_list);

        // 根据切片id去重
        $point_id_list = $relation_list->unique('point_id');

        $logger->info('根据消息id获取到了切片信息，处理切片', ['point_id_list' => $point_id_list, 'doc_type' => $doc_type, 'doc_token' => $doc_token]);

        // 更新切片
        $this->updatePoint($point_id_list, $doc_type, $doc_token);

    }

    public function updateMessage($message_id_list)
    {
        $logger = Helpers::getLogger('doc_update');
        $group_assistant_service = new GroupAssistantService();
        $message_model = new MessageModel();

        // 更新消息id里面的内容
        $update = true;
        foreach ($message_id_list as $message_id) {
            try {
                // 先从数据库把消息详情拿出来
                $message_db_info = (new FeiShuMessageModel())->getDetailByMessageId($message_id);
                if (!$message_db_info) {
                    $logger->info('消息不存在', ['message_id' => $message_id]);
                    continue;
                }

                // 先获取access_token
                $tenant_access_token = (new AuthModel())->tenantAccessTokenByAppType(AbstractFeishuModel::INTERNAL,
                    GroupAssistantService::APP_ID, GroupAssistantService::APP_SECRET);

                // 从接口拉取消息详情
                $message_detail = $message_model->getMessageDetail($message_id, $tenant_access_token);
                if (!isset($message_detail['data']['items'][0])) {
                    $logger->info('从接口获取消息的时候失败了', ['message_id' => $message_id]);
                    continue;
                }
                $message_info = $message_detail['data']['items'][0];


                try {
                    $format_message = $group_assistant_service->formatmessage($message_info, $tenant_access_token, $message_db_info->chat_id, $update);
                    // 只需要更新一次文档 需要确保成功的情况下才把update 改为false
                    $update = false;
                } catch (\Throwable $throwable) {
                    $logger->error('消息更新失败，继续下一个消息。', ['message_id' => $message_id, 'message' => $throwable->getMessage(), 's' => $throwable->getTraceAsString()]);
                    continue;
                }


                // 过滤掉的消息不需要处理
                if (!$format_message) {
                    $logger->info('过滤消息', ['message_id' => $message_id]);
                    continue;
                }

                (new FeiShuMessageModel())->updateMessage($message_id, json_encode($format_message), $format_message['cut_off']);


                $logger->info('消息更新完成', ['message_id' => $message_id]);
            } catch (\Throwable $throwable) {
                $logger->error('消息更新失败，继续下一个消息。', ['message_id' => $message_id, 'message' => $throwable->getMessage(), 's' => $throwable->getTraceAsString()]);
                continue;
            }

        }
    }

    /**
     * 更新切片
     *
     * @param Collection $point_id_list
     * @param $doc_type
     * @param $doc_token
     * @return void
     */
    private function updatePoint(Collection $point_id_list, $doc_type, $doc_token)
    {
        $logger = Helpers::getLogger('doc_update');
        $summary_model = new FeishuMessageSummary();
        $message_summary = new MessageSummary();

        $point_id_list = $point_id_list->groupBy('summary_id');

        $relation_data = [];
        $deleted_message_id_list = [];

        $logger->info('根据summary_id聚合后的数据', ['point_id_list' => $point_id_list, 'doc_type' => $doc_type, 'doc_token' => $doc_token]);

        // 一个个更新
        foreach ($point_id_list as $summary_id => $item_list) {
            $logger->info('单个summary_id开始处理', ['summary_id' => $summary_id, 'item_list' => $item_list, 'doc_type' => $doc_type, 'doc_token' => $doc_token]);
            // 获取消息内容，重新总结
            $summary_data = $summary_model->getData($summary_id);
            $message_id_list = json_decode($summary_data->msg_id_list, true);
            $message_list = (new FeiShuMessageModel())->getListByMessageIds($message_id_list);

            // 获取上次的群总结内容
            $last_summary_content = $message_summary->getLastSummaryContent($summary_data->chat_id);
            $messages_format_list = GroupAssistantService::formatToLLMMessageList($message_list, 7000);

            $logger->info('开启去LLM总结', ['summary_id' => $summary_id, 'doc_type' => $doc_type, 'doc_token' => $doc_token]);
            $summary = $message_summary->generateSummary($messages_format_list, $last_summary_content);
            $logger->info('LLM总结完成，处理更新知识库', ['summary' => $summary, 'doc_type' => $doc_type, 'doc_token' => $doc_token]);
            (new FeishuMessageSummary())->updateSummary($summary_id, $summary['summary_content'], json_encode($summary['summary_entity'], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES), json_encode($summary['summary_question'], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));

            /**@var Collection $item_list */
            $doc_id = ''; // question的doc_id
            foreach ($item_list as $item) {
                if ($item->type === 'summary_content') {
                    $doc_id = str_replace('summary_content', 'summary_question', $item->doc_id);
                    $content = $summary['summary_content'];
                    $content .= "\n<id>$summary_id<id>";
                    $message_summary->updatePoint($item->point_id, $item->doc_id, $content);
                    $logger->info('summary_content处理完成', ['point_id' => $item->point_id, 'doc_id' => $item->doc_id, 'content' => $content, 'doc_type' => $doc_type, 'doc_token' => $doc_token]);
                }
                if ($item->type === 'summary_entity') {
                    $content = $summary['summary_entity'];
                    // 实体的话是聚合在一起当成一个切片
                    if (is_array($content)) {
                        $content = implode(' ', $content);
                    }
                    $content .= "\n<id>$summary_id<id>";
                    $message_summary->updatePoint($item->point_id, $item->doc_id, $content);
                    $logger->info('summary_entity处理完成', ['point_id' => $item->point_id, 'doc_id' => $item->doc_id, 'content' => $content, 'doc_type' => $doc_type, 'doc_token' => $doc_token]);
                }
                // 先删除，再添加切片
                if ($item->type === 'summary_question') {
                    $doc_id = $item->doc_id; // 获取到文档id
                    (new PointModel())->delete($item->point_id);
                    (new FeiShuDocMessagePointIDRelationModel())->deleteByPointID($item->point_id);
                    $deleted_message_id_list[] = $item->message_id;
                    $logger->info('summary_question的切片删除完成', ['point_id' => $item->point_id, 'message_id' => $item->message_id, 'doc_type' => $doc_type, 'doc_token' => $doc_token]);
                }
            }


            // 添加切片
            $tmp_point_id_list = [];
            $content = $summary['summary_question'];
            // 提问的话，是一个提问一个切片, 最好判断一下是否为数组，因为大模型返回的东西，不好说
            if (is_array($content)) {
                foreach ($content as $content_item) {
                    $content_item .= "\n<id>$summary_id<id>";
                    $point_id = $message_summary->addPoint($doc_id, $content_item);
                    $tmp_point_id_list[] = $point_id;
                }
            } else {
                $content .= "\n<id>$summary_id<id>";
                $point_id = $message_summary->addPoint($doc_id, $content);
                $tmp_point_id_list[] = $point_id;
            }
            $logger->info('summary_question的切片添加完成', ['point_id_list' => $tmp_point_id_list, 'doc_type' => $doc_type, 'doc_token' => $doc_token]);

            $deleted_message_id_list = array_unique($deleted_message_id_list);
            foreach ($deleted_message_id_list as $message_id) {
                foreach ($tmp_point_id_list as $point_id) {
                    $relation_data[] = [
                        'message_id' => $message_id,
                        'point_id'   => $point_id,
                        'summary_id' => $summary_id,
                        'type'       => 'summary_question',
                        'doc_id'     => $doc_id,
                    ];
                }
            }

            (new FeiShuDocMessagePointIDRelationModel())->addMultiple($relation_data);
            $logger->info('summary_question的切片入库完成，单个summary_id处理结束', ['relation_data' => $relation_data, 'summary_id' => $summary_id, 'item_list' => $item_list, 'doc_type' => $doc_type, 'doc_token' => $doc_token]);
        }
    }


    static public function getMessageDetail($message_id)
    {
        $tenant_access_token = (new AuthModel())->tenantAccessTokenByAppType(AbstractFeishuModel::INTERNAL, GroupAssistantService::APP_ID, GroupAssistantService::APP_SECRET);
        // 从接口拉取消息详情
        try {
            return (new MessageModel())->getMessageDetail($message_id, $tenant_access_token);
        } catch (AppException $e) {
            return [];
        }
    }

    /**
     * 轮询所有的消息存在的飞书文档，并且去更新消息内容
     *
     * @return void
     */
    public function pollDocList()
    {
        $logger = Helpers::getLogger('doc_update');
        $logger->info('本轮任务开始');
        // 获取需要轮询的文档
        $all_list = (new FeiShuDocsTokenUpdateTimeModel())->getAll();

        // 200个分一批，因为接口不能超过200个
        $chunk_list = $all_list->chunk(200);

        foreach ($chunk_list as $list) {
            /*** @var $list Collection */
            // 批量去接口获取文档信息
            $request_data = [];
            foreach ($list as $item) {
                $request_data[] = [
                    'doc_token' => $item->doc_token,
                    'doc_type'  => $item->doc_type,
                ];
            }

            // 获取文件元数据列表，并且带上索引的
            $metas_index_list = $this->batchQueryMetas($request_data);
            $logger->info('批量获取文档信息成功,开始一个个处理');

            // 一个个判断
            foreach ($list as $item) {
                $key = $item->doc_token . '_' . $item->doc_type;
                if (!$this->lock($key)) {
                    $logger->info('无法获取执行锁，等待下一轮任务', ['doc_type' => $item->doc_type, 'doc_token' => $item->doc_token]);
                    continue;
                }
                if (isset($metas_index_list[$key])) {
                    $metas_info = $metas_index_list[$key];

                    // 说明更新了，需要去触发更新逻辑
                    if ($metas_info['latest_modify_time'] > $item->last_update_timestamp) {
                        $logger->info('触发了更新逻辑，开始处理更新逻辑', ['doc_type' => $item->doc_type, 'doc_token' => $item->doc_token, 'latest_modify_time' => $metas_info['latest_modify_time'], 'last_update_timestamp' => $item->last_update_timestamp]);
                        try {
                            $now = time();
                            $this->handlerFileUpdate($metas_info['doc_type'], $metas_info['doc_token']);
                            // 更新完之后再更新last_update_timestamp
                            (new FeiShuDocsTokenUpdateTimeModel())->updateLastUpdateTimestamp($item->doc_type, $item->doc_token, $now);
                        } catch (\Throwable $throwable) {
                            $logger->error('遇到未知错误，错误信息：' . $throwable->getMessage(), ['doc_type' => $item->doc_type, 'doc_token' => $item->doc_token, 's' => $throwable->getTraceAsString()]);
                        }
                        $logger->info('单个文档处理完成', ['doc_type' => $item->doc_type, 'doc_token' => $item->doc_token]);

                    }
                }

                // 最后面解锁
                $this->unlock($key);
            }
        }

        $logger->info('本轮任务结束');


    }

    /**
     * 获取执行锁，用来判断当前任务是否正在执行。
     * 锁住的最大时间24小时
     *
     * @param $doc_token_id
     *
     * @return bool  获锁成功返回true 否则返回false
     * @throws RedisException
     */
    public function lock($doc_token_id)
    {
        $key = 'running_handler_doc_update_' . $doc_token_id;
        if (RedisCache::getInstance()->set($key, '1', ['NX', 'EX' => 86400])) {
            Helpers::getLogger('doc_update')->info('成功获取执行锁', ['doc_token_id' => $doc_token_id]);
            return true;
        } else {
            Helpers::getLogger('doc_update')->info('获取执行锁失败', ['doc_token_id' => $doc_token_id]);
            return false;
        }
    }

    /**
     * 解锁
     *
     * @param $doc_token_id
     * @throws RedisException
     */
    public function unlock($doc_token_id)
    {
        $key = 'running_handler_doc_update_' . $doc_token_id;
        RedisCache::getInstance()->del($key);
        Helpers::getLogger('doc_update')->info('解锁成功', ['doc_token_id' => $doc_token_id]);
    }

}