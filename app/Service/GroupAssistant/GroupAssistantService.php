<?php

namespace App\Service\GroupAssistant;

use App\Exception\AppException;
use App\Exception\ScheduleException;
use App\Model\HttpModel\Feishu\AbstractFeishuModel;
use App\Model\HttpModel\Feishu\AuthModel;
use App\Model\HttpModel\Feishu\BotModel;
use App\Model\HttpModel\Feishu\Calendar\CalendarsModel;
use App\Model\HttpModel\Feishu\IM\ChatsModel;
use App\Model\HttpModel\Feishu\IM\MessageModel;
use App\Model\HttpModel\OpenAI\OpenAIDeepSeekModel;
use App\Model\SqlModel\Zeda\FeiShuAssistantBotChatModel;
use App\Model\SqlModel\Zeda\FeiShuAssistantUserMessageTimeModel;
use App\Model\SqlModel\Zeda\FeishuChatScheduleModel;
use App\Model\SqlModel\Zeda\FeiShuGroupAssistantRemindModel;
use App\Model\SqlModel\Zeda\FeiShuMessageModel;
use App\Model\SqlModel\Zeda\FeishuUserChatModel;
use App\Model\SqlModel\Zeda\FeiShuUserScheduleModel;
use App\Param\FeishuStreamCardParam;
use App\Param\Knowledge\KnowledgeParam;
use App\Param\OpenAIParam;
use App\Service\DataBot\DataBotService;
use App\Service\FeiShuService;
use App\Service\OpenAI\OpenAI;
use App\Struct\RedisCache;
use App\Task\GroupAssistantTask;
use App\Utils\Helpers;
use Common\EnvConfig;
use Exception;
use Illuminate\Support\Collection;
use RedisException;

class GroupAssistantService
{

    /**
     * @var MessageModel
     */
    protected $message_model;

    /**
     * @var OpenAI
     */
    protected $openai_service;

    protected $model = OpenAIDeepSeekModel::DOUBAO_MODEL;

    const APP_ID = EnvConfig::FEISHU['qunxiaomi']['app_id'];


    const APP_SECRET = EnvConfig::FEISHU['qunxiaomi']['app_secret'];

    /**
     * 点击卡片的redis key, 确保卡片只能点击一次
     */
    const CARD_ACTION_KEY = 'car_unique_';

    /**
     * 创建日程的卡片模板id
     */
    const CARD_TEMPLATE = 'AAqBIrNWmRTsX';


    /**
     * 群活跃的最后时间，时间戳
     */
    const CHAT_ACTIVITY_LAST_TIME_KEY = 'chat_activity_last_time_';

    /**
     * 授权回调的URL，这个URL要写入飞书机器人的安全配置里面
     */
    const GRANT_REDIRECT_URL = 'https://dms.zx.com/feishu/saveGroupAssistantUAT';

    /**
     * 日历ID TODO 这里是写死的，上线的时候需要创建一个，替换掉这个ID
     * 这个是灰度机器人的
     */
//    const CALENDAR_ID = '<EMAIL>';

    /**
     * 群小秘
     */
    const CALENDAR_ID = EnvConfig::FEISHU['qunxiaomi']['calendar_id'];

    /**
     * 飞书群小秘机器人的 Encrypt Key
     */
    const ENCRYPT_KEY = EnvConfig::FEISHU['qunxiaomi']['encrypt_key'];

    /**
     * 飞书群小秘的union_id
     */
    const BOT_UNION_ID = EnvConfig::FEISHU['qunxiaomi']['union_id'];


    /**
     * 飞书的tenant_key
     */
    const TENANT_KEY = '105189295a17175f';


    const SESSION_EXPIRE_TIME = 7200;

    const SESSION_MAX_LENGTH_STR = 100;

    const INTENT_SESSION_KEY = 'group_assistant_intent_';

    const BOT_ADDED_RUNNING_KEY = 'bot_added_running_';

    public function __construct()
    {
        $this->message_model = new MessageModel();
        $this->openai_service = new OpenAI();
    }


    /**
     * 解析消息
     *
     * @param array $decrypt
     * @param $tenant_access_token
     * @return array
     * @throws RedisException
     */
    public function resolveMessage(array $decrypt, $tenant_access_token)
    {
        $logger = Helpers::getLogger('group_assistant_single_message');
        $chat_type = $decrypt['event']['message']['chat_type'];
        // 转换一下消息格式，对齐一下拉取列表的消息格式
        $message = $this->convertMessage($decrypt);


        // 群聊的话设置一下群活跃时间，按消息发送时间来算
        $chat_id = '0';
        $message_create_time = intval($message['create_time'] / 1000);
        if ($chat_type === 'group') {
            $chat_id = $decrypt['event']['message']['chat_id'];
            $this->setChatLastMessageActivityTime($chat_id, $message_create_time);
        } else {
            // 单聊的话插入union_id表。存在则无视，不需要更新其他字段
            (new FeiShuAssistantUserMessageTimeModel())->addData($message['sender']['sender_id']['union_id']);
        }

        // 解析消息
        $logger->info('开始解析消息', ['chat_id' => $chat_id, 'union_id' => $message['sender']['sender_id']['union_id'], 'message_id' => $message['message_id']]);
        $format_message = $this->formatMessage($message, $tenant_access_token, $chat_id);
        $logger->info('消息解析完成。', ['chat_id' => $chat_id, 'union_id' => $message['sender']['sender_id']['union_id'], 'message_id' => $message['message_id'], 'format_message' => $format_message]);

        // 过滤掉的消息不需要处理
        if (!$format_message) {
            return [];
        }

        // 群聊的消息内容和发个群助手的消息，都存起来
        $message_model = new FeiShuMessageModel();
        $insert_data = [
            'message_id'          => $message['message_id'],
            'chat_id'             => $chat_id,
            'decrypt'             => json_encode($decrypt),
            'format_message'      => json_encode($format_message),
            'msg_type'            => $message['msg_type'],
            'message_create_time' => $message_create_time,
            'parent_id'           => $message['parent_id'] ?? '',
            'union_id'            => $message['sender']['sender_id']['union_id'],
            'cut_off'             => $format_message['cut_off'],
            'at'                  => $format_message['at'],
        ];

        // 如果解析了云文档或者文件，回复一个OK
        if ($format_message['cut_off'] === 0) {
            $this->reactions($message['message_id']);
        }

        // 判断需要入库的消息 逻辑：1.群聊的全部入库。2.p2p的，cut_off=0的入库。msg_type为file、merge_forward、 image的也入库。
        $should_store = ($chat_type === 'group') || ($format_message['cut_off'] === 0) || in_array($format_message['msg_type'], ['file', 'merge_forward', 'image']);
        if ($should_store) {
            $message_model->replaceOne($insert_data);
            $logger->info('消息入库完成', ['chat_id' => $chat_id, 'union_id' => $message['sender']['sender_id']['union_id']]);
        }

        return [$insert_data, $format_message];
    }


    /**
     * 处理单条消息
     *
     * @param array $decrypt 完整的请求体
     * @throws RedisException
     * @throws Exception
     */
    public function handlerSingleMessage(array $decrypt)
    {
        $logger = Helpers::getLogger('group_assistant_single_message');
        $chat_type = $decrypt['event']['message']['chat_type'];
        $chat_id = $chat_type === 'group' ? $decrypt['event']['message']['chat_id'] : '';
        $union_id = $decrypt['event']['sender']['sender_id']['union_id'];
        $open_id = $decrypt['event']['sender']['sender_id']['open_id'];

        // 判断有没有人@群机器人
        $at = false;
        if (isset($decrypt['event']['message']['mentions'])) {
            foreach ($decrypt['event']['message']['mentions'] as $mention) {
                if ($mention['id']['union_id'] === self::BOT_UNION_ID) {
                    // 说明有人@我
                    $at = true;
                    break;
                }
            }
        }

        // 先获取access_token
        $tenant_access_token = (new AuthModel())->tenantAccessTokenByAppType(AbstractFeishuModel::INTERNAL, self::APP_ID, self::APP_SECRET);
        // 初始化流式输出的卡片参数
        $message_param = new FeishuStreamCardParam([
            'receive_id'          => $chat_type === 'group' ? $chat_id : $union_id,
            'receive_id_type'     => $chat_type === 'group' ? 'chat_id' : 'union_id',
            'tenant_access_token' => $tenant_access_token,
            'content'             => '正在解析消息...',
        ]);


        // 群小秘被@、或者私聊，则发个消息让用户先等等
        if ($at || $chat_type === 'p2p') {
            FeiShuService::stream($message_param);
        }

        // 解析消息先
        [$insert_data, $format_message] = $this->resolveMessage($decrypt, $tenant_access_token);
        if (!$format_message) {
            return;
        }


        if (!$at && $chat_type === 'group') {
            $logger->info('机器人没有被at,单条消息流程结束', ['chat_id' => $chat_id, 'union_id' => $union_id, 'mentions' => $decrypt['event']['message']['mentions'] ?? []]);
            return;
        }

        $logger->info('机器人被at或者是p2p,则进行意图识别', ['chat_id' => $chat_id, 'union_id' => $union_id, 'message' => $format_message['content']]);

        // 判断哪些消息不需要意图识别
        if ($this->reactionMessageType($format_message, $chat_type)) {
            if ($format_message['msg_type'] === 'merge_forward') {
                $message_param->setContent('消息已收到。请继续提问');
            } else {
                $message_param->setContent('消息已入知识库。');
            }

            FeiShuService::stream($message_param);
            // 关闭流式输出
            FeiShuService::updateStreamModel($message_param);

            // 格式化一下，存入上下文
            $message_list = Collection::make([(object)$insert_data]);
            $format_message_list = self::formatToLLMMessageList($message_list);
            $this->addToSessionList(KnowledgeService::QA_SESSION_KEY . $union_id, [
                [
                    'role'    => 'user',
                    'content' => $format_message_list
                ]
            ]);
            $logger->info('消息已存入上下文，流程结束', ['chat_id' => $chat_id, 'union_id' => $union_id]);
            return;
        }

        // 提示用户
        $message_param->setContent('正在进行意图识别...');

        FeiShuService::stream($message_param);


        // post的消息需要解析一下
        if ($format_message['msg_type'] === 'post') {
            $query = self::getPostContent($format_message['content'], $format_message['cut_off'] == 1 ? 500 : 0);
        } else {
            $query = $format_message['content'];
        }

        $result = $this->intent($query, $chat_id, $chat_type, $union_id);
        $intent = $result['intent'];
        $logger->info('意图识别完成', ['chat_id' => $chat_id, 'union_id' => $union_id, 'intent' => $intent]);

        if ($intent === '日程提醒') {
            // 提示用户
            $message_param->setContent('正在处理日程...');
            FeiShuService::stream($message_param);
            try {
                // 这里需要判断是群日程还是私聊日程
                if ($chat_type === 'group') {
                    $this->scheduleGroup($chat_id);
                } else {
                    $this->schedulePerson($query, $open_id, $union_id);
                }
                $message_param->setContent('日程处理完成。请注意查看日历');
            } catch (ScheduleException $exception) {
                $message_param->setContent($exception->getMessage());
            }
            FeiShuService::stream($message_param);

            // 关闭流式输出
            FeiShuService::updateStreamModel($message_param);
            $logger->info('日程流程处理完成', ['chat_id' => $chat_id, 'union_id' => $union_id]);
            return;
        }


        // 知识问答
        // 判断一下机器人是否在处理群的历史记录
        if ($chat_type === 'group' && RedisCache::getInstance()->get(self::BOT_ADDED_RUNNING_KEY . $chat_id)) {
            $message_param->setContent('稍等一下，我还在翻阅历史消息');
            FeiShuService::stream($message_param);
            $logger->info('机器人正在翻阅历史消息，单条消息流程结束，处理完成', ['query' => $query, 'chat_id' => $chat_id]);
            // 关闭流式输出
            FeiShuService::updateStreamModel($message_param);
            return;
        }

        $message_param->setContent('正在思考，请稍后....');
        FeiShuService::stream($message_param);
        $param = ['query' => $query, 'union_id' => $union_id, 'chat_id' => $chat_id, 'type' => $chat_type, 'start_date' => $result['start_date'], 'end_date' => $result['end_date']];
        $param = new KnowledgeParam($param);


        (new KnowledgeService())->getKnowledgeAnswer($param, $message_param);
        // 关闭流式输出
        FeiShuService::updateStreamModel($message_param);

        $logger->info('单条消息流程结束，处理完成', ['query' => $query]);

    }

    /**
     * 判断一下这个消息需不需要做意图识别
     * 不需要的话直接回复用户就行
     *
     * @param $format_message
     * @param $chat_type
     * @return bool
     */
    private function reactionMessageType($format_message, $chat_type)
    {
        if ($chat_type === 'group') {
            return false;
        }

        if ($format_message['message_type'] === 'merge_forward') {
            return true;
        }

        if ($format_message['cut_off'] === 0) {
            return true;
        }

        return false;
    }

    /**
     * 意图识别
     * @param $query
     * @param $chat_id
     * @param $chat_type
     * @param $union_id
     * @return array
     * @throws Exception
     */
    public function intent($query, $chat_id, $chat_type, $union_id)
    {
        $system_content = GroupAssistantPrompt::INTENT;
        [$today, $day_of_week_cn] = (new DataBotService())->getCurrentDate();
        $start_date = date('Y-m-d', strtotime("-7 days"));
        $end_date = date('Y-m-d');
        $system_content = str_replace(['{{today}}', '{{day_of_week_cn}}', '{{start_date}}', '{{end_date}}'], [$today, $day_of_week_cn, $start_date, $end_date], $system_content);

        $messages = [
            [
                "role"    => "system",
                "content" => $system_content,
            ]
        ];

        if ($chat_type === 'group') {
            $session_key = self::INTENT_SESSION_KEY . "group_{$chat_id}";
        } else {
            $session_key = self::INTENT_SESSION_KEY . $union_id;
        }

        // 获取历史session 的 message
        $list = $this->getSessionList($session_key);
        foreach ($list as $item) {
            $messages[] = $item[0];
            if (isset($item[1]) && $item[1]) {
                $messages[] = $item[1];
            }
        }

        $messages[] = [
            "role"    => "user",
            "content" => $query,
        ];
        $gpt_content = [
            "messages"    => $messages,
            "temperature" => 0.3,
            "top_p"       => 0.9,
            "model"       => OpenAIDeepSeekModel::V3_MODEL, // 意图识别用v3模型 快一点
            'log'         => '意图识别',
        ];

        $open_ai_param = new OpenAIParam($gpt_content);
        $result = $this->openai_service->chatByJson($open_ai_param);
        // 识别完成直接加入session
        $this->addToSessionList($session_key, [
            [
                'role'    => 'user',
                'content' => $query
            ],
            [
                'role'    => 'assistant',
                'content' => json_encode($result, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_UNICODE),
            ]
        ]);

        return $result;
    }

    /**
     * 群日程流程创建的意图处理
     *
     * @param $chat_id
     * @throws Exception
     */
    private function scheduleGroup($chat_id)
    {
        $logger = Helpers::getLogger('group_assistant_schedule');
        // 立即触发创建日程的流程
        $logger->info('机器人被at,立即触发创建日程的流程', ['chat_id' => $chat_id]);
        $now = time();
        $bot_chat_model = new FeiShuAssistantBotChatModel();
        $chat_data_info = $bot_chat_model->getData($chat_id);
        if (!$chat_data_info) {
            $logger->info('找不到群信息，日程创建流程结束', ['chat_id' => $chat_id]);
            throw new ScheduleException('日程创建失败，失败原因：找不到群信息，chat_id: ' . $chat_id);
        }
        // 自旋锁
        $this->waitLock($chat_id);
        try {
            // 开始获取消息，从当前时间开始往上获取消息 时间范围应该是：上一次处理消息的时间～当前时间
            $last_message_timestamp = $chat_data_info->last_message_timestamp;
            if ($last_message_timestamp > 0) {
                $message_start_time = date("Y-m-d H:i:s", $chat_data_info->last_message_timestamp);
            } else {
                $message_start_time = date("Y-m-d H:i:s", strtotime("-1 day"));
            }

            $message_end_time = date("Y-m-d H:i:s", $now);
            $schedule_data_list = $this->handlerCheckSchedule($chat_id, $message_start_time, $message_end_time);

            // 最后重置一下last_message_timestamp
            $bot_chat_model->updateLastMessageTime($chat_id, $now);
            $this->unlock($chat_id);

            if (empty($schedule_data_list)) {
                $logger->info('创建日程失败，返回空日程信息', ['chat_id' => $chat_id]);
                throw new ScheduleException('日程无需更新或创建');
            }
        } catch (AppException $throwable) {
            // 解锁
            $this->unlock($chat_id);
            $logger->error('创建日程流程结束，遇到未知错误：' . $throwable->getMessage(), ['chat_id' => $chat_id, 'strace' => $throwable->getTraceAsString()]);
            throw new ScheduleException('日程创建失败，请重试');
        }
    }

    /**
     * 处理私聊的日程创建
     *
     * @param $query
     * @param $open_id
     * @param $union_id
     * @return void
     * @throws Exception
     */
    private function schedulePerson($query, $open_id, $union_id)
    {
        $logger = Helpers::getLogger('group_assistant_schedule_person');
        // 立即触发创建日程的流程
        $logger->info('机器人私聊,触发创建日程的流程', ['union_id' => $union_id, 'query' => $query]);

        // 自旋锁
        $this->waitLock($union_id);

        // 开始获取消息，从当前时间开始往上获取消息 时间范围应该是：上一次处理消息的时间～当前时间
        $now = time();
        $last_info = (new FeiShuAssistantUserMessageTimeModel())->getTimeByUnionId($union_id);
        if ($last_info->isEmpty()) {
            $message_start_time = date("Y-m-d H:i:s", strtotime("-1 day"));
        } else {
            $message_start_time = date("Y-m-d H:i:s", $last_info->first()->last_message_timestamp);
        }

        $message_end_time = date("Y-m-d H:i:s", $now);

        $message_model = new FeiShuMessageModel();

        // 活跃状态消息池里面的消息列表
        $pool_message_list = $message_model->getListByUnionAndTime($union_id, $message_start_time, $message_end_time);

        // 活跃之前的消息，有消息条数限制。目前限制30条,三天
        $last_message_list = $message_model->getListByChatId('0', date("Y-m-d H:i:s", strtotime($message_start_time) - 86400 * 3), $message_start_time, 30, $union_id);

        // 格式化两个消息,活跃之前的消息要限制一下长度大小，总字符限制3000个
        $pool_message_list_format = self::formatToLLMMessageList($pool_message_list, 40000);
        $history_message_list_format = self::formatToLLMMessageList($last_message_list, 5000);

        // 还要去获取一下历史的日程
        // 判断日程结束时间大于当前时间的
        $chat_schedule_list = (new FeiShuUserScheduleModel())->getListByChatIdAndTime($union_id, date('Y-m-d H:i:s'));

        $chat_schedule_list_format = [];
        if ($chat_schedule_list->isNotEmpty()) {
            foreach ($chat_schedule_list as $chat_schedule) {
                // 先把日程信息格式化
                $tmp = [
                    'id'          => $chat_schedule->id,
                    'summary'     => $chat_schedule->summary,
                    'description' => $chat_schedule->description,
                    'start_time'  => $chat_schedule->start_time,
                    'end_time'    => $chat_schedule->end_time,
                    'update_time' => $chat_schedule->update_time,
                ];
                $chat_schedule_list_format[] = $tmp;
            }
        }
        $chat_schedule_list_format = json_encode($chat_schedule_list_format, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);

        $username = FeiShuService::getNameByUnionId($union_id);
        try {
            $schedule_data_list = $this->extractScheduleDataPerson($history_message_list_format, $pool_message_list_format, $chat_schedule_list_format, $query, $username);
            // 返回的是日程列表
            foreach ($schedule_data_list as $schedule_data) {
                // 判断是否有id，有id，则更新日程
                if (isset($schedule_data['id']) && $schedule_data['id']) {
                    $logger->info('需要进行日程更新，流程开始', ['union_id' => $union_id, 'query' => $query, 'schedule_data' => $schedule_data]);
                    $this->handlerUpdateSchedulePerson($schedule_data, $union_id);
                    continue;
                }

                // 创建创建日程流程
                $logger->info('提取到日程参数，流程开始', ['union_id' => $union_id, 'query' => $query, 'schedule_data' => $schedule_data]);
                $this->handlerCreatePersonSchedule($open_id, $schedule_data, $union_id);
            }
            if (empty($schedule_data_list)) {
                $logger->info('没有创建或修改日程，请重新发问', ['union_id' => $union_id, 'query' => $query]);

                $this->unlock($union_id);
                throw new ScheduleException('没有创建或修改日程，请重新发问');
            }

            // 最后重置一下last_message_timestamp
            (new FeiShuAssistantUserMessageTimeModel())->setLastMessageTimestamp($union_id, $now);

            // 解锁
            $this->unlock($union_id);
        } catch (AppException $throwable) {
            $logger->info('个人创建日程出现异常，失败信息:' . $throwable->getMessage(), ['union_id' => $union_id]);
            $this->unlock($union_id);
        }
        $logger->info('机器人私聊,触发创建日程的流程结束', ['union_id' => $union_id, 'query' => $query]);
    }


    /**
     * 处理日程检测，判断是否需要创建日程
     *
     * @param $chat_id
     * @param $message_start_time
     * @param $message_end_time
     * @return array|void
     * @throws Exception
     */
    public function handlerCheckSchedule($chat_id, $message_start_time, $message_end_time)
    {
        $logger = Helpers::getLogger('group_assistant_schedule');
        $logger->info('开始处理日程的检测逻辑', ['chat_id' => $chat_id, 'message_start_time' => $message_start_time, 'message_end_time' => $message_end_time]);
        $message_model = new FeiShuMessageModel();

        // 1. 让LLM判断当前消息是否需要创建日程

        // 活跃状态消息池里面的消息列表
        $pool_message_list = $message_model->getListByChatIdAndTime($chat_id, $message_start_time, $message_end_time);

        // 活跃之前的消息，有消息条数限制。目前限制30条
        $last_message_list = $message_model->getListByChatId($chat_id, date("Y-m-d H:i:s", strtotime($message_start_time) - 86400 * 3), $message_start_time, 30);

        // 格式化两个消息,活跃之前的消息要限制一下长度大小，总字符限制3000个
        $last_message_list_format = self::formatToLLMMessageList($last_message_list, 3000, 300);
        $pool_message_list_format = self::formatToLLMMessageList($pool_message_list, 40000);

        // 获取机器人信息
        $tenant_access_token = (new AuthModel())->tenantAccessTokenByAppType(AbstractFeishuModel::INTERNAL,
            GroupAssistantService::APP_ID, GroupAssistantService::APP_SECRET);
        $bot_info = (new BotModel())->getBotInfo($tenant_access_token);

        // 第一步判断
        $single = $this->singleMessageDecide($last_message_list_format, $pool_message_list_format, $bot_info['app_name']);

        if (!$single['need_reminder']) {
            $logger->info('不需要创建直接结束当前处理', ['chat_id' => $chat_id]);
            return;
        }

        // 第二步，去进一步判断。这里的限制比第一步小一点
        $history_message_list_format = self::formatToLLMMessageList($last_message_list, 5000);

        // 还要去获取一下历史的日程
        // 判断日程结束时间大于当前时间的
        $chat_schedule_list = (new FeishuChatScheduleModel())->getListByChatIdAndTime($chat_id, date('Y-m-d H:i:s'));

        $chat_schedule_list_format = [];
        if ($chat_schedule_list->isNotEmpty()) {
            foreach ($chat_schedule_list as $chat_schedule) {
                // 先把日程信息格式化
                $tmp = [
                    'id'          => $chat_schedule->id,
                    'summary'     => $chat_schedule->summary,
                    'description' => $chat_schedule->description,
                    'start_time'  => $chat_schedule->start_time,
                    'end_time'    => $chat_schedule->end_time,
                    'update_time' => $chat_schedule->update_time,
                ];
                $participants = json_decode($chat_schedule->participants, true);
                $tmp['participants'] = $participants;
                $chat_schedule_list_format[] = $tmp;
            }
        }
        $chat_schedule_list_format = json_encode($chat_schedule_list_format, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);

        // 请求大模型，提取参数
        $schedule_data_list = $this->extractScheduleData($history_message_list_format, $pool_message_list_format, $chat_schedule_list_format, $bot_info['app_name']);

        // 返回的是日程列表
        foreach ($schedule_data_list as $schedule_data) {
            // 判断一下关键信息，不能为空，不然无法创建
            if (!($schedule_data['participants'] && $schedule_data['start_time'] && $schedule_data['end_time'])) {
                // 缺失关键信息，不处理
                $logger->info('缺失关键信息，不处理', ['chat_id' => $chat_id, 'schedule_data' => $schedule_data,]);
                continue;
            }

            // 判断是否有id，有id，则更新日程
            if (isset($schedule_data['id']) && $schedule_data['id']) {
                $logger->info('需要进行日程更新，流程开始', ['chat_id' => $chat_id, 'schedule_data' => $schedule_data]);
                $this->handlerUpdateSchedule($schedule_data, $chat_id);
                continue;
            }

            // 创建创建日程流程
            $logger->info('需要进行日程创建，流程开始', ['chat_id' => $chat_id, 'schedule_data' => $schedule_data]);
            $this->handlerCreateSchedule($chat_id, $schedule_data);
        }
        $logger->info('完成处理日程的检测逻辑', ['chat_id' => $chat_id, 'schedule_data' => $schedule_data_list]);
        return $schedule_data_list;
    }


    /**
     * 处理更新流程
     *
     */
    private function handlerUpdateSchedule($schedule_data, $chat_id)
    {
        $chat_model = new ChatsModel();
        $logger = Helpers::getLogger('group_assistant_schedule');
        // 更新日程
        // 先去数据库获取更新的日程id
        $chat_schedule_data = (new FeishuChatScheduleModel())->getData($schedule_data['id']);
        if (!$chat_schedule_data) {
            $logger->error('数据库找不到日程信息', ['chat_id' => $chat_id, 'schedule_data' => $schedule_data]);
            return;
        }

        // 先获取access_token
        $tenant_access_token = (new AuthModel())->tenantAccessTokenByAppType(AbstractFeishuModel::INTERNAL,
            GroupAssistantService::APP_ID, GroupAssistantService::APP_SECRET);

        // 更新一下日程表
        (new FeishuChatScheduleModel())->updateData($schedule_data['id'], $schedule_data);

        /* --- 新增 ----*/
        // 获取权限范围
//        $permission_open_id_list = (new ContactModel())->visibility($tenant_access_token, GroupAssistantService::APP_ID);
//        $permission_open_id_list = array_column($permission_open_id_list['data']['users'], 'open_id');

        // 先获取群成员
        $members = $chat_model->getALLChatMembers($tenant_access_token, $chat_id, 'open_id');
        // 创建一个map
        $map = [];
        foreach ($members as $member) {
            // 非本公司的用户，不需要创建日程
            if ($member['tenant_key'] !== self::TENANT_KEY) {
                continue;
            }
            $map[$member['name']] = $member['member_id'];
        }


        // 获取所有人的设置列表
        $remind_list = (new FeiShuGroupAssistantRemindModel())->getAll()->keyBy('open_id')->toArray();

        // 组装参与人的数组
        $attendees = [];
        foreach ($schedule_data['participants'] as $participant) {
            // 获取发送者的open_id
            $open_id = $map[$participant] ?? '';
            if (!$open_id) {
                $logger->info('创建日程时，根据用户姓名获取用户的open_id失败', ['chat_id' => $chat_id, $participant]);
                continue;
            }
//            $logger->info('权限判断开始', ['permission_open_id_list' => $permission_open_id_list, 'map' => $map]);
//            // 判断是否有权限,不在permission_open_id_list说明没有权限申请，不需要添加进日程
//            if (!in_array($open_id, $permission_open_id_list, true)) {
//                $logger->info('当前联系人没有机器人权限', ['chat_id' => $chat_id, $participant]);
//                continue;
//            }
            // 判断用户自己的提醒设置
            $logger->info('提醒判断开始', ['remind_list' => $remind_list]);
            if (isset($remind_list[$open_id])) {
                if ($remind_list[$open_id]->state == FeiShuGroupAssistantRemindModel::STATE_NEVER) {
                    $logger->info('用户设置了永久不提醒', ['chat_id' => $chat_id, $participant]);
                    continue;
                }
                // 判断当前时间有没有超过用户的提醒设置时间
                $now = date('Y-m-d H:i:s');
                if ($now < $remind_list[$open_id]->expired_at) {
                    $logger->info('用户设置了不提醒', ['chat_id' => $chat_id, 'user' => $participant, 'expired_at' => $remind_list[$open_id]->expired_at]);
                    continue;
                }
            }

            $attendees[] = [
                'type'        => 'user',
                'is_optional' => true,
                'user_id'     => $open_id,
            ];
        }
        /* --- 新增 ----*/

        $chat_model = new ChatsModel();
        // 获取一下群分享链接
        $link_info = $chat_model->getChatLink($chat_id, $tenant_access_token);
        // 获取群信息
        $chat_info = $chat_model->chatInfo($tenant_access_token, $chat_id);
        $chat_name = $chat_info['name'];

        $description = "<a href=\"{$link_info['data']['share_link']}\">来自群：$chat_name</a><br>" . $schedule_data['description'];

        $update_schedule_data = [
            'summary'     => $schedule_data['summary'],
            'description' => $description,
            'start_time'  => [
                'timestamp' => strtotime($schedule_data['start_time']),
            ],
            'end_time'    => [
                'timestamp' => strtotime($schedule_data['end_time']),
            ]
        ];
        // 再更新日程
        $res = (new CalendarsModel())->updateScheduleData($tenant_access_token, $update_schedule_data, self::CALENDAR_ID, $chat_schedule_data->event_id);

        /* --- 新增 ----*/
        // 添加日程用户
        if ($attendees) {
            (new CalendarsModel())->calendarAttendees($tenant_access_token, $attendees, self::CALENDAR_ID, $chat_schedule_data->event_id, 'open_id');
            $logger->info('日程用户添加成功', ['chat_id' => $chat_id, 'attendees' => $attendees]);
        }
        /* --- 新增 ----*/
        $logger->info('日程更新成功', ['chat_id' => $chat_id, 'res' => $res]);

    }

    /**
     * 处理更新流程(个人流程)
     *
     */
    private function handlerUpdateSchedulePerson($schedule_data, $union_id)
    {
        $logger = Helpers::getLogger('group_assistant_schedule_person');
        // 更新日程
        // 先去数据库获取更新的日程id
        $chat_schedule_data = (new FeishuUserScheduleModel())->getData($schedule_data['id']);
        if (!$chat_schedule_data) {
            $logger->error('数据库找不到日程信息', ['union_id' => $union_id, 'schedule_data' => $schedule_data]);
            return;
        }

        // 先获取access_token
        $tenant_access_token = (new AuthModel())->tenantAccessTokenByAppType(AbstractFeishuModel::INTERNAL,
            GroupAssistantService::APP_ID, GroupAssistantService::APP_SECRET);

        // 更新一下日程表
        (new FeishuUserScheduleModel())->updateData($schedule_data['id'], $schedule_data);


        $update_schedule_data = [
            'summary'     => $schedule_data['summary'],
            'description' => $schedule_data['description'],
            'start_time'  => [
                'timestamp' => strtotime($schedule_data['start_time']),
            ],
            'end_time'    => [
                'timestamp' => strtotime($schedule_data['end_time']),
            ]
        ];
        // 再更新日程
        $res = (new CalendarsModel())->updateScheduleData($tenant_access_token, $update_schedule_data, self::CALENDAR_ID, $chat_schedule_data->event_id);

        $logger->info('日程更新成功', ['union_id' => $union_id, 'res' => $res]);

    }


    /**
     * 处理创建流程
     *
     * @param $chat_id
     * @param $schedule_data
     * @return void
     */
    public function handlerCreateSchedule($chat_id, $schedule_data)
    {
        $logger = Helpers::getLogger('group_assistant_schedule');
        $chat_model = new ChatsModel();

        // 先获取access_token
        $tenant_access_token = (new AuthModel())->tenantAccessTokenByAppType(AbstractFeishuModel::INTERNAL,
            GroupAssistantService::APP_ID, GroupAssistantService::APP_SECRET);


        // 获取权限范围
//        $permission_open_id_list = (new ContactModel())->visibility($tenant_access_token, GroupAssistantService::APP_ID);
//        $permission_open_id_list = array_column($permission_open_id_list['data']['users'], 'open_id');

        // 先获取群成员
        $members = $chat_model->getALLChatMembers($tenant_access_token, $chat_id, 'open_id');
        // 创建一个map
        $map = [];
        foreach ($members as $member) {
            // 非本公司的用户，不需要创建日程
            if ($member['tenant_key'] !== self::TENANT_KEY) {
                continue;
            }
            $map[$member['name']] = $member['member_id'];
        }


        // 获取所有人的设置列表
        $remind_list = (new FeiShuGroupAssistantRemindModel())->getAll()->keyBy('open_id')->toArray();

        // 组装参与人的数组
        $attendees = [];
        foreach ($schedule_data['participants'] as $participant) {
            // 获取发送者的open_id
            $open_id = $map[$participant] ?? '';
            if (!$open_id) {
                $logger->info('创建日程时，根据用户姓名获取用户的open_id失败', ['chat_id' => $chat_id, $participant]);
                continue;
            }
//            $logger->info('权限判断开始', ['permission_open_id_list' => $permission_open_id_list, 'map' => $map]);
//            // 判断是否有权限,不在permission_open_id_list说明没有权限申请，不需要添加进日程
//            if (!in_array($open_id, $permission_open_id_list, true)) {
//                $logger->info('当前联系人没有机器人权限', ['chat_id' => $chat_id, $participant]);
//                continue;
//            }
            // 判断用户自己的提醒设置
            $logger->info('提醒判断开始', ['remind_list' => $remind_list]);
            if (isset($remind_list[$open_id])) {
                if ($remind_list[$open_id]->state == FeiShuGroupAssistantRemindModel::STATE_NEVER) {
                    $logger->info('用户设置了永久不提醒', ['chat_id' => $chat_id, $participant]);
                    continue;
                }
                // 判断当前时间有没有超过用户的提醒设置时间
                $now = date('Y-m-d H:i:s');
                if ($now < $remind_list[$open_id]->expired_at) {
                    $logger->info('用户设置了不提醒', ['chat_id' => $chat_id, 'user' => $participant, 'expired_at' => $remind_list[$open_id]->expired_at]);
                    continue;
                }
            }

            $attendees[] = [
                'type'        => 'user',
                'is_optional' => true,
                'user_id'     => $open_id,
            ];
        }

        // 获取一下群分享链接
        $link_info = $chat_model->getChatLink($chat_id, $tenant_access_token);
        // 获取群信息
        $chat_info = $chat_model->chatInfo($tenant_access_token, $chat_id);
        $chat_name = $chat_info['name'];
        $description = "<a href=\"{$link_info['data']['share_link']}\">来自群：$chat_name</a><br>" . $schedule_data['description'];

        // 创建的日程参数
        $create_schedule_data = [
            'summary'          => $schedule_data['summary'],
            'description'      => $description,
            'start_time'       => [
                'timestamp' => strtotime($schedule_data['start_time']),
            ],
            'end_time'         => [
                'timestamp' => strtotime($schedule_data['end_time']),
            ],
            'visibility'       => 'public',
            'attendee_ability' => 'can_modify_event',
            'free_busy_status' => 'free',
        ];


        // 再创建日程
        $res = (new CalendarsModel())->createScheduleData($tenant_access_token, $create_schedule_data, self::CALENDAR_ID);
        $event_id = $res['data']['event']['event_id'];
        $logger->info('日程创建成功', ['chat_id' => $chat_id, 'event_id' => $event_id]);
        // 添加日程用户
        if ($attendees) {
            (new CalendarsModel())->calendarAttendees($tenant_access_token, $attendees, self::CALENDAR_ID, $event_id, 'open_id');
            $logger->info('日程用户添加成功', ['chat_id' => $chat_id, 'attendees' => $attendees]);
        }


        // 在数据库，插入一条记录，用来标识本次场景的日程创建。
        $chat_schedule_id = (new FeishuChatScheduleModel())->addData([
            'chat_id'      => $chat_id,
            'summary'      => $schedule_data['summary'],// 日程标题
            'description'  => $schedule_data['description'],
            'start_time'   => $schedule_data['start_time'],
            'end_time'     => $schedule_data['end_time'],
            'event_id'     => $event_id,
            'participants' => json_encode($schedule_data['participants']),
        ]);
        $logger->info('插入日程表成功', ['chat_id' => $chat_id, 'chat_schedule_id' => $chat_schedule_id]);
    }

    /**
     * 处理创建流程（个人创建）
     *
     * @param $open_id
     * @param $schedule_data
     * @param $union_id
     * @return void
     */
    public function handlerCreatePersonSchedule($open_id, $schedule_data, $union_id)
    {
        $logger = Helpers::getLogger('group_assistant_schedule_person');

        // 先获取access_token
        $tenant_access_token = (new AuthModel())->tenantAccessTokenByAppType(AbstractFeishuModel::INTERNAL,
            GroupAssistantService::APP_ID, GroupAssistantService::APP_SECRET);


        // 获取权限范围
//        $permission_open_id_list = (new ContactModel())->visibility($tenant_access_token, GroupAssistantService::APP_ID);
//        $permission_open_id_list = array_column($permission_open_id_list['data']['users'], 'open_id');

        // 获取用户的名称
        $participant = FeiShuService::getNameByOpenId($open_id);

        // 获取不到名字，说明不是本公司的员工，不需要创建
        if (!$participant) {
            $logger->error('找不到员工，无法创建日程', ['union_id' => $union_id, 'open_id' => $open_id]);
            throw new ScheduleException('找不到员工，无法创建日程');
        }


        // 组装参与人的数组
        $attendees = [];

//        $logger->info('权限判断开始', ['union_id' => $union_id, 'permission_open_id_list' => $permission_open_id_list]);
//        // 判断是否有权限,不在permission_open_id_list说明没有权限申请，不需要添加进日程
//        if (!in_array($open_id, $permission_open_id_list, true)) {
//            $logger->info('当前联系人没有机器人权限', ['union_id' => $union_id, 'participant' => $participant]);
//            throw new ScheduleException('当前用户没有机器人权限');
//        }

        $attendees[] = [
            'type'        => 'user',
            'is_optional' => true,
            'user_id'     => $open_id,
        ];

        // 创建的日程参数
        $create_schedule_data = [
            'summary'          => $schedule_data['summary'],
            'description'      => $schedule_data['description'],
            'start_time'       => [
                'timestamp' => strtotime($schedule_data['start_time']),
            ],
            'end_time'         => [
                'timestamp' => strtotime($schedule_data['end_time']),
            ],
            'visibility'       => 'public',
            'attendee_ability' => 'can_modify_event',
            'free_busy_status' => 'free',
        ];


        // 再创建日程
        $res = (new CalendarsModel())->createScheduleData($tenant_access_token, $create_schedule_data, self::CALENDAR_ID);
        $event_id = $res['data']['event']['event_id'];
        $logger->info('日程创建成功，日程id: ', ['union_id' => $union_id, 'event_id' => $event_id]);

        // 添加日程用户
        (new CalendarsModel())->calendarAttendees($tenant_access_token, $attendees, self::CALENDAR_ID, $event_id, 'open_id');
        $logger->info('日程用户添加成功', ['union_id' => $union_id, 'attendees' => $attendees]);

        // 在数据库，插入一条记录，用来标识本次场景的日程创建。
        $chat_schedule_id = (new FeiShuUserScheduleModel())->addData([
            'union_id'    => $union_id,
            'summary'     => $schedule_data['summary'],// 日程标题
            'description' => $schedule_data['description'],
            'start_time'  => $schedule_data['start_time'],
            'end_time'    => $schedule_data['end_time'],
            'event_id'    => $event_id,
        ]);
        $logger->info('插入日程表成功，id: ', ['union_id' => $union_id, 'id' => $chat_schedule_id]);
    }

//    /**
//     * 发送一条创建日程消息的卡片，询问用户是否创建
//     *
//     * @param $chat_id
//     * @param $union_id
//     * @param $schedule_data
//     * @param $chat_schedule_id
//     * @param $participant
//     * @return void
//     * @throws RedisException
//     */
//    public function sendScheduleMessageCard($chat_id, $union_id, $schedule_data, $chat_schedule_id, $participant)
//    {
//        $logger = Helpers::getLogger('group_assistant');
//
//        // 替换一下@某个人变成真正的@
//        $description = FeiShuService::replaceChatGroupNameToAT($chat_id, $schedule_data['description'], GroupAssistantService::APP_ID, GroupAssistantService::APP_SECRET);
//
//        // 发一条卡片消息给用户，让用户选择是否需要创建 这里发送卡片消息模板
//        $content = [
//            'type' => 'template',
//            'data' => [
//                "template_id"       => self::CARD_TEMPLATE,
//                'template_variable' => [
//                    'start_time'  => $schedule_data['end_time'],
//                    'title'       => $schedule_data['summary'],
//                    'description' => $description,
//                ],
//            ],
//        ];
//        // 发送成功后会有一个message_id，根据这个id做上下文关联
//        $content = json_encode($content);
//        $send_res = FeiShuService::sendCardMessage(self::APP_ID, self::APP_SECRET, $union_id, 'union_id', $content);
//        $logger->info('提醒创建的消息发送成功，发送对象:' . $participant);
//
//        $res_message_id = $send_res['data']['message_id'];
//        $key = self::CARD_ACTION_KEY . $res_message_id;
//        $value = [
//            'message_id'    => $res_message_id,
//            'schedule_data' => [
//                'start_time'       => $schedule_data['start_time'],
//                'end_time'         => $schedule_data['end_time'],
//                'summary'          => $schedule_data['summary'],
//                'description'      => $schedule_data['description'],
//                'chat_schedule_id' => $chat_schedule_id,
//            ]
//        ];
//        RedisCache::getInstance()->setex($key, 7200, serialize($value));
//    }

//    /**
//     * 创建日程
//     * @return void
//     * @deprecated  这里不需要了 ，流程不需要走到这一步
//     */
//    public function createSchedule($union_id, $schedule_data)
//    {
//        $logger = Helpers::getLogger('group_assistant_schedule');
//        // 获取uat
//        $auth_data = (new FeishuUATModel())->getData($union_id, self::APP_ID);
//        if (!$auth_data) {
//            // 不应该找不到的 找不到进来是非常异常的状态
//            $logger->error('找不到用户的uat', ['union_id' => $union_id, 'schedule_data' => $schedule_data]);
//            throw new AppException('找不到用户的user_access_token');
//        }
//        $user_access_token = $auth_data->user_access_token;
//
//        // 先获取用户的主日历ID
//        $primary_calendars_info = (new CalendarsModel())->primary($user_access_token);
//        $calendar_id = $primary_calendars_info['data']['calendars'][0]['calendar']['calendar_id'];
//
//        $logger->info('获取用户主日历成功,日历id:' . $calendar_id);
//
//        // 先拿到chat_schedule_id，等会更新用
//        $chat_schedule_id = $schedule_data['chat_schedule_id'];
//        // 创建的日程参数
//        $create_schedule_data = [
//            'summary'     => $schedule_data['summary'],
//            'description' => $schedule_data['description'],
//            'start_time'  => [
//                'timestamp' => strtotime($schedule_data['start_time']),
//            ],
//            'end_time'    => [
//                'timestamp' => strtotime($schedule_data['end_time']),
//            ]
//        ];
//
//        // 再创建日程
//        $res = (new CalendarsModel())->createScheduleData($user_access_token, $create_schedule_data, $calendar_id);
//        $app_link = $res['data']['event']['app_link'];
//        $event_id = $res['data']['event']['event_id'];
//
//        $logger->info('日程创建成功，日程id: ' . $event_id);
//
//        // 日程创建成功，插入用户日程表
//        $name = FeiShuService::getNameByUnionId($union_id) ?: '';
//        (new FeishuUserScheduleModel())->addData([
//            'schedule_id' => $chat_schedule_id,
//            'event_id'    => $event_id,
//            'union_id'    => $union_id,
//            'name'        => $name,
//        ]);
//
//        // 发消息通知用户，日程创建成功。
//        $content = FeiShuService::getMDCard("日程创建成功，[点击]($app_link)查看详情");
//        FeiShuService::sendCardMessage(self::APP_ID, self::APP_SECRET, $union_id, 'union_id', $content);
//    }


//    /**
//     * 更新日程
//     * @return void
//     */
//    public function updateSchedule($chat_id, $union_id, $schedule_data, $event_id)
//    {
//        $logger = Helpers::getLogger('group_assistant_schedule');
//        // 获取uat
//        $auth_data = (new FeishuUATModel())->getData($union_id, self::APP_ID);
//        if (!$auth_data) {
//            // 不应该找不到的 找不到进来是非常异常的状态
//            $logger->error('找不到用户的uat', ['union_id' => $union_id, 'schedule_data' => $schedule_data]);
//            throw new AppException('找不到用户的user_access_token');
//        }
//        $user_access_token = $auth_data->user_access_token;
//
//        // 先获取用户的主日历ID
//        $primary_calendars_info = (new CalendarsModel())->primary($user_access_token);
//        $calendar_id = $primary_calendars_info['data']['calendars'][0]['calendar']['calendar_id'];
//
//        $logger->info('获取用户主日历成功,日历id:' . $calendar_id);
//
//
//        // 日程参数
//        $update_schedule_data = [
//            'summary'     => $schedule_data['summary'],
//            'description' => $schedule_data['description'],
//            'start_time'  => [
//                'timestamp' => strtotime($schedule_data['start_time']),
//            ],
//            'end_time'    => [
//                'timestamp' => strtotime($schedule_data['end_time']),
//            ]
//        ];
//
//        // 再更新日程
//        $res = (new CalendarsModel())->updateScheduleData($user_access_token, $update_schedule_data, $calendar_id, $event_id);
//        $app_link = $res['data']['event']['app_link'];
//
//        /** 发消息通知用户，日程更新成功 **/
//        // 先替换一下AT的人名
//        $description = FeiShuService::replaceChatGroupNameToAT($chat_id, $schedule_data['description'], GroupAssistantService::APP_ID, GroupAssistantService::APP_SECRET);
//
//        // 发一条卡片消息给用户
//        $content = [
//            'type' => 'template',
//            'data' => [
//                "template_id"       => self::CARD_EDIT_TEMPLATE,
//                'template_variable' => [
//                    'start_time'  => $schedule_data['end_time'],
//                    'title'       => $schedule_data['summary'],
//                    'description' => $description,
//                    'app_link'    => $app_link,
//                ],
//            ],
//        ];
//        // 发送成功后会有一个message_id，根据这个id做上下文关联
//        $content = json_encode($content);
//        FeiShuService::sendCardMessage(self::APP_ID, self::APP_SECRET, $union_id, 'union_id', $content);
//        $logger->info('日程更新成功，日程信息：', $res);
//    }

    /**
     * 转换一下消息体。让$decrypt里面的消息内容对齐从接口获取的message_list的内容
     *
     * @param $decrypt
     * @return array
     */
    public function convertMessage($decrypt)
    {
        $message = $decrypt['event']['message'];
        $message['sender'] = $decrypt['event']['sender'];
        $message['sender']['id'] = $decrypt['event']['sender']['sender_id']['open_id'];
        $message['body']['content'] = $message['content'];
        $message['msg_type'] = $message['message_type'];
        return $message;
    }


//    /**
//     * 总结聊天记录
//     *
//     * @param $tenant_access_token
//     * @param $user_query
//     * @param $decrypt
//     * @return array
//     */
//    public function summary($tenant_access_token, $user_query, $chat_id)
//    {
//        // 默认总结当天的聊天记录
//        $start_time = strtotime(date("Y-m-d")) - 86400;
//        $end_time = strtotime(date("Y-m-d 23:59:59"));
//
//
//        // 先去获取聊天记录
//        $message_list = (new MessageModel())->getMessageList($tenant_access_token, 'chat', $chat_id, $start_time, $end_time);
//
//        Helpers::getLogger('group_assistant')->info('获取到了消息历史', $message_list);
//        // 解析聊天记录
//        $format_message_list = [];
//        foreach ($message_list as $message) {
//            $format_message_str = $this->formatMessage($message, $tenant_access_token, $chat_id);
//            if ($format_message_str) {
//                $format_message_list[] = $format_message_str;
//            }
//        }
//        dd($format_message_list);
//
//        $summary = implode("\n", $format_message_list);
//
//        // 判断一下是否超出最大值，目前最大值设置为50k 留下1k冗余，因为毕竟是粗略计算的
//        $token = Helpers::estimateTokenCount($summary);
//        $max_tokens = 50000;
//        if ($token > $max_tokens) {
//            // 简单截断策略，假设每个字符平均 2 个 token
//            $average_tokens_per_char = 2;
//            $max_length = $max_tokens / $average_tokens_per_char;
//            $text = mb_substr($summary, 0, $max_length);
//            Helpers::getLogger('group_assistant')->info('发生了token溢出，已经截取', [
//                'ori_summary' => $summary,
//                'token'       => $token,
//                'summary'     => $text,
//            ]);
//            $summary = $text;
//        }
//        Helpers::getLogger('group_assistant')->info('最终的summary', ['summary' => $summary]);
//        $system_content = "\n-----------------------------------------------\n";
//        $system_content .= '根据以上的历史对话记录，是总结对话内容';
//        $messages = [
//            [
//                "role"    => "system",
//                "content" => $summary . $system_content,
//            ]
//        ];
//
//
//        $gpt_content = [
//            "messages"    => $messages,
//            "temperature" => 0.2,
//            "top_p"       => 0.9
//        ];
//        return $this->requestGPTToMarkdown($gpt_content, '总结对话内容', '对话总结结果');
//    }


    public function summaryMessage($message_list, $history_message_list, $user_count, $message_count)
    {

        $system_content = GroupAssistantPrompt::SUMMARY;

        $system_content = str_replace(['{{history_message_list}}', '{{message_list}}', '{{user_count}}', '{{message_count}}'], [$history_message_list, $message_list, $user_count, $message_count], $system_content);

        $messages = [
            [
                "role"    => "user",
                "content" => $system_content,
            ]
        ];

        $gpt_content = [
            "messages"    => $messages,
            "temperature" => 0.3,
            "top_p"       => 0.9,
            "model"       => $this->model,
            "stream"      => false,
            'log'         => '对话总结结果',
        ];

        $open_ai_param = new OpenAIParam($gpt_content);
        $result = $this->openai_service->chat($open_ai_param);

        return $result['gpt_message'];
    }


    /**
     * 格式化飞书的各种消息类型
     *
     * @param $message
     * @param $tenant_access_token
     * @param string $chat_id
     * @param bool $update 是否是更新消息，重新格式化
     * @return array
     */
    public function formatMessage($message, $tenant_access_token, string $chat_id = '', bool $update = false)
    {
        // 过滤一下机器人发的消息
        $sender_type = $message['sender']['sender_type'];
        if ($sender_type !== 'user') {
            return [];
        }
        // 过滤撤回的消息
        if ($message['body']['content'] === 'This message was recalled') {
            return [];
        }

        $create_time = date("Y-m-d H:i:s", $message['create_time'] / 1000);

        // 直接用open_id获取名字
        $open_id = $message['sender']['id'];
        // 先去企业里面获取一下
        $sender = FeiShuService::getNameByOpenId($open_id);
        // 如果获取不到open_id，则需要去飞书接口获取群成员，拿open_id
        if ($chat_id && !$sender) {
            // 先获取群成员
            $members = (new ChatsModel())->getALLChatMembers($tenant_access_token, $chat_id, 'open_id');
            foreach ($members as $member) {
                if ($member['member_id'] === $open_id) {
                    $sender = $member['name'];
                    break;
                }
            }
        }
        // 如果最终还是拿不到sender,就直接用id
        if (!$sender) {
            $sender = $open_id;
        }

        $cut_off = 1;
        switch ($message['msg_type']) {
            case 'text':
                $content = json_decode($message['body']['content'], true);
                $text = $content['text'];
                // 说明这条消息是@的内容
                if (!empty($message['mentions'])) {
                    $search = [];
                    $replace = [];
                    foreach ($message['mentions'] as $mentions) {
                        $search[] = $mentions['key'];
                        $replace[] = "@" . $mentions['name'];
                    }
                    $text = str_replace($search, $replace, $text);
                }
                $format_content = MessageFormat::handlerText($text, $message['message_id'], $cut_off, $update);
                break;
            case 'interactive':
                $format_content = $message['body']['content'];
                break;
            case 'post':
                $content = json_decode($message['body']['content'], true);
                $format_content = MessageFormat::handlerPost($message['message_id'], $content, $cut_off, $update);
                break;

            // image：图片
            case 'image':
                $content = json_decode($message['body']['content'], true);
                $img_desc = MessageFormat::handlerImage($message['message_id'], $content['image_key']);
                $format_content = "发了一张图片，图片内容：$img_desc";
                break;
            case 'audio':
                $content = json_decode($message['body']['content'], true);
                $format_content = MessageFormat::handlerAudio($message['message_id'], $content['file_key'], $tenant_access_token);
                break;
            case 'media':
                $format_content = "发了一个视频。[视频]";
                break;
            case 'sticker':
                $format_content = "[表情包]";
                break;
            // file：文件
            case 'file':
                $content = json_decode($message['body']['content'], true);
                $file_content = MessageFormat::handlerFile($message['message_id'], $content['file_key'], $content['file_name'], $cut_off);
                $format_content = "发了一份文件，文件名：{$content['file_name']}, 文件内容如下: \n$file_content";
                break;
            // 转发的聊天记录
            case 'merge_forward':
                $format_content = [];
                // 转发里面的表情回复暂时没办法获取
                $respond = (new MessageModel())->getMessageDetail($message['message_id'], $tenant_access_token);
                $sub_message_list = $respond['data']['items'];
                foreach ($sub_message_list as $index => $sub_message) {
                    if ($index == 0) continue;

                    $sub_format_content = $this->formatMessage($sub_message, $tenant_access_token, $chat_id, $update);
                    if ($sub_format_content) {
                        $format_content[] = $sub_format_content;
                    }
                }
                break;
            default:
                return [];
        }

        // 判断一下@的消息
        $at = 0;
        if (isset($message['mentions'])) {
            foreach ($message['mentions'] as $mention) {
                if ($mention['name'] === '群小秘') {
                    $at = 1;
                    break;
                }
            }
        }


        return [
            'content'             => $format_content,
            'parent_id'           => $message['parent_id'] ?? '',
            'message_id'          => $message['message_id'],
            'message_create_time' => $create_time,
            'sender'              => $sender,
            'msg_type'            => $message['msg_type'],
            'cut_off'             => $cut_off,
            'at'                  => $at,
        ];
    }


    /**
     * 处理群助手的卡片回调
     *
     * @param $union_id
     * @param $value
     * @param $message_id
     * @return void
     * @throws RedisException
     */
    public function handleGroupAssistantCardEvent($union_id, $value, $message_id)
    {
        // 判断该消息有没有被消耗过
        $key = GroupAssistantService::CARD_ACTION_KEY . $message_id;
        $message_model = new MessageModel();
        $context = RedisCache::getInstance()->get($key);

        $tenant_access_token = (new AuthModel())->tenantAccessTokenByAppType(AbstractFeishuModel::INTERNAL,
            GroupAssistantService::APP_ID, GroupAssistantService::APP_SECRET);
        if (!$context) {
            // 说明过期或者重复点击，不处理,发个消息提醒一下用户
            $message_model->sendText($tenant_access_token, $union_id, '该链接已过期');
            Helpers::getLogger('group_assistant_card')->info('该链接已过期');
            return;
        }

        $queue = [
            'union_id'     => $union_id,
            'action_value' => $value,
            'context'      => unserialize($context),
        ];

        try {
            RedisCache::getInstance()->lPush(GroupAssistantTask::CARD_QUEUE_KEY, json_encode($queue, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));
            // 删除key
            RedisCache::getInstance()->del($key);
        } catch (RedisException $e) {
            Helpers::getLogger('group_assistant_card')->error('消息入队失败, error : ' . $e->getMessage(), $queue);
        }
    }


    static public function limitMaxToken($format_message_list)
    {
        // 判断一下是否超出最大值，目前最大值设置为40k 留下2k冗余，因为毕竟是粗略计算的
        $token = Helpers::estimateTokenCount($format_message_list);
        $max_tokens = 40000;
        if ($token > $max_tokens) {
            // 转换为整数处理避免浮点误差（精度放大10倍）
            $max_int = (int)($max_tokens * 10);
            $current_int = (int)($token * 10);
            // 循环前面的字符
            while ($current_int > $max_int && mb_strlen($format_message_list, 'UTF-8') > 0) {
                $first_char = mb_substr($format_message_list, 0, 1, 'UTF-8');

                // 根据字符类型减少token计数
                if (preg_match('/[\x{4E00}-\x{9FFF}]/u', $first_char)) {
                    $current_int -= 6; // 每个中文字符对应0.6token
                } else {
                    $current_int -= 3; // 其他字符对应0.3token
                }

                // 截断第一个字符
                $format_message_list = mb_substr($format_message_list, 1, null, 'UTF-8');
            }

            return $format_message_list;
        }

        return $format_message_list;
    }


    /**
     * 创建授权链接
     * @param $union_id
     * @param $schedule_data
     * @return string
     * @throws RedisException|\Random\RandomException
     */
    public function createGrantUrl($union_id, $schedule_data)
    {
        $redirect_url = self::GRANT_REDIRECT_URL;
        $scope = "offline_access calendar:calendar:readonly calendar:calendar.event:create calendar:calendar:read calendar:calendar.event:update";

        // 随机字符串
        $bytes = random_bytes(16);
        $random_str = substr(bin2hex($bytes), 0, 16);
        // 使用标识，链接用过一次就不能再用
        $used_flag_key = 'auto_feishu_user_' . $random_str;
        RedisCache::getInstance()->setex($used_flag_key, 600, 1);
        $state = json_encode([
            'redirect_url'  => $redirect_url,
            'union_id'      => $union_id,
            'used_flag'     => $used_flag_key,
            'schedule_data' => $schedule_data,
        ]);
        return FeiShuService::createAuthUrl(self::APP_ID, $redirect_url, $scope, $state);
    }

    /**
     * 单挑消息去判断用户是否需要创建日程
     *
     * @param $history_message_list
     * @param $message_list
     * @param $bot_name
     * @return array
     */
    public function singleMessageDecide($history_message_list, $message_list, $bot_name)
    {
        $system_content = GroupAssistantPrompt::SINGLE_MESSAGE_DECIDE;
        $system_content = str_replace(['{{history_message_list}}', '{{message_list}}', '{{bot_name}}'], [$history_message_list, $message_list, $bot_name], $system_content);
        $messages = [
            [
                "role"    => "user",
                "content" => $system_content,
            ]
        ];
        $gpt_content = [
            "messages"    => $messages,
            "temperature" => 0.3,
            "top_p"       => 0.9
        ];
        // 设置模型和stream
        $gpt_content['model'] = $this->model;
        $gpt_content['stream'] = false;
        $gpt_content['log'] = '单句消息判断';
        $open_ai_param = new OpenAIParam($gpt_content);

        return $this->openai_service->chatByJson($open_ai_param);
    }


    /**
     * 根据历史消息记录去提取用户的日程信息
     *
     * @param $history_message_list_format
     * @param $message_list
     * @param $chat_schedule_list_format
     * @param $bot_name
     * @return array
     * @throws Exception
     */
    public function extractScheduleData($history_message_list_format, $message_list, $chat_schedule_list_format, $bot_name)
    {
        $system_content = GroupAssistantPrompt::EXTRACT_SCHEDULE_DATA;
        [$today, $day_of_week_cn] = (new DataBotService())->getCurrentDate();

        $system_content = str_replace(['{{history_message_list}}', '{{message_list}}', '{{today}}', '{{schedule_list}}', '{{bot_name}}', '{{day_of_week_cn}}'], [$history_message_list_format, $message_list, $today, $chat_schedule_list_format, $bot_name, $day_of_week_cn], $system_content);
        $messages = [
            [
                "role"    => "user",
                "content" => $system_content,
            ]
        ];
        $gpt_content = [
            "messages"    => $messages,
            "temperature" => 0.3,
            "top_p"       => 0.9
        ];
        // 设置模型和stream
        $gpt_content['model'] = $this->model;
        $gpt_content['stream'] = false;
        $gpt_content['log'] = '根据历史消息提取日程信息';

        $open_ai_param = new OpenAIParam($gpt_content);
        return $this->openai_service->chatByJson($open_ai_param);
    }

    /**
     * 根据历史消息记录去提取用户的日程信息(个人)
     *
     * @param $history_message_list_format
     * @param $message_list
     * @param $chat_schedule_list_format
     * @param $query
     * @param $username
     * @return array
     * @throws Exception
     */
    public function extractScheduleDataPerson($history_message_list_format, $message_list, $chat_schedule_list_format, $query, $username)
    {
        $system_content = GroupAssistantPrompt::EXTRACT_SCHEDULE_DATA_PERSON;
        [$today, $day_of_week_cn] = (new DataBotService())->getCurrentDate();

        $system_content = str_replace(
            ['{{history_message_list}}', '{{message_list}}', '{{query}}', '{{today}}', '{{schedule_list}}', '{{username}}', '{{day_of_week_cn}}'],
            [$history_message_list_format, $message_list, $query, $today, $chat_schedule_list_format, $username, $day_of_week_cn],
            $system_content);
        $messages = [
            [
                "role"    => "user",
                "content" => $system_content,
            ]
        ];
        $gpt_content = [
            "messages"    => $messages,
            "temperature" => 0.3,
            "top_p"       => 0.9
        ];
        // 设置模型和stream
        $gpt_content['model'] = $this->model;
        $gpt_content['stream'] = false;
        $gpt_content['log'] = '根据个人消息提取日程信息';

        $open_ai_param = new OpenAIParam($gpt_content);

        return $this->openai_service->chatByJson($open_ai_param);
    }


    /**
     * 处理机器人进群事件，拉取历史记录
     *
     * @param $chat_id
     * @param $chat_name
     * @param $union_id
     * @return void
     */
    public function handlerGroupAssistantBotAdded($chat_id, $chat_name, $union_id)
    {
        $logger = Helpers::getLogger('group_assistant_bot');
        RedisCache::getInstance()->set(self::BOT_ADDED_RUNNING_KEY . $chat_id, 1, 86400);
        $logger->info("开始处理机器人进群事件,chat_id" . $chat_id);

        try {
            $data = [
                'chat_id'   => $chat_id,
                'chat_name' => $chat_name,
                'union_id'  => $union_id
            ];
            (new FeiShuAssistantBotChatModel())->addData($data);
            $logger->info('添加入库成功，开始拉取历史消息. chat_id' . $chat_id);

            // 先获取access_token
            $tenant_access_token = (new AuthModel())->tenantAccessTokenByAppType(AbstractFeishuModel::INTERNAL,
                GroupAssistantService::APP_ID, GroupAssistantService::APP_SECRET);
            // 拉取历史消息
            // 先去获取聊天记录
            $start_time = strtotime(date("Y-m-d H:i:s", strtotime("-31 day")));
            $end_time = strtotime(date("Y-m-d H:i:s"));
            $message_list = (new MessageModel())->getMessageList($tenant_access_token, 'chat', $chat_id, $start_time, $end_time, 50, 'ByCreateTimeAsc');

            $logger->info('获取到了消息历史，开始解析聊天记录', ['chat_id' => $chat_id, 'message_list' => $message_list]);
            $message_model = new FeiShuMessageModel();

            // 解析聊天记录
            foreach ($message_list as $message) {
                // 先获取access_token 防止长时间循环token过期
                $tenant_access_token = (new AuthModel())->tenantAccessTokenByAppType(AbstractFeishuModel::INTERNAL,
                    GroupAssistantService::APP_ID, GroupAssistantService::APP_SECRET);

                $format_message = $this->formatmessage($message, $tenant_access_token, $chat_id);
                // 过滤掉的消息不需要处理
                if (!$format_message) {
                    $logger->info('过滤消息', ['chat_id' => $chat_id]);
                    continue;
                }

                // 列表是找不到union_id的，得去转换一下
                $open_id = $message['sender']['id'];
                $union_id = FeiShuService::getUnionIdByOpenId($open_id);
                // 找不到，则去飞书接口找一下,通过消息详情去曲线获取
                if (!$union_id) {
                    $message_detail = (new MessageModel())->getMessageDetail($message['message_id'], $tenant_access_token, 'union_id');
                    $union_id = $message_detail['data']['items'][0]['sender']['id'];
                }
                $insert_data = [
                    'message_id'          => $message['message_id'],
                    'chat_id'             => $chat_id,
                    'decrypt'             => json_encode($message),
                    'format_message'      => json_encode($format_message),
                    'msg_type'            => $message['msg_type'],
                    'message_create_time' => intval($message['create_time'] / 1000),
                    'parent_id'           => $message['parent_id'] ?? '',
                    'union_id'            => $union_id,
                    'cut_off'             => $format_message['cut_off'],
                    'at'                  => $format_message['at'],
                ];
                $message_model->replaceone($insert_data);

                $logger->info('消息入库完成', ['chat_id' => $chat_id, 'message_id' => $message['message_id']]);
            }
            // 标记一下 群用户跟群的关系
            $this->handlerUserChat($chat_id);
        } catch (\Throwable $exception) {
            $logger->error($exception->getMessage(), ['strace' => $exception->getTraceAsString()]);
        }

        RedisCache::getInstance()->del(self::BOT_ADDED_RUNNING_KEY . $chat_id);
        $logger->info('任务完成', ['chat_id' => $chat_id]);
    }

    /**
     * 处理机器人移出群
     *
     * @param $chat_id
     * @return void
     */
    public function handlerGroupAssistantBotDeleted($chat_id)
    {
        $logger = Helpers::getLogger('group_assistant_bot');
        $logger->info("group_assistant_bot_delete,chat_id: " . $chat_id);

        (new FeiShuAssistantBotChatModel())->deleteChat($chat_id);
        (new FeishuUserChatModel())->deleteByChatId($chat_id);

        $logger->info("退群完成。chat_id: " . $chat_id);
    }

    public function handleGroupAssistantUserDeleted($chat_id, $user_list)
    {
        $logger = Helpers::getLogger('group_assistant_user_delete');
        $logger->info("用户退群", ['chat_id' => $chat_id, 'user_list' => $user_list]);

        (new FeishuUserChatModel())->deleteByUnionId($user_list, $chat_id);

        $logger->info("用户退群完成。chat_id: " . $chat_id);
    }

    public function handleGroupAssistantUserAdded($chat_id, $user_list)
    {
        $logger = Helpers::getLogger('group_assistant_user_added');
        $logger->info("用户进群", ['chat_id' => $chat_id, 'user_list' => $user_list]);

        $user_chat_model = (new FeishuUserChatModel());

        $insert_data = [];
        foreach ($user_list as $union_id) {
            $insert_data[] = ['union_id' => $union_id, 'chat_id' => $chat_id];
        }

        $user_chat_model->addMultiple($insert_data);

        $logger->info("用户进群完成。chat_id: " . $chat_id);
    }


    public function handlerGroupAssistantNewSession($union_id)
    {
        $session_key = KnowledgeService::QA_SESSION_KEY . $union_id;
        $redis = RedisCache::getInstance();
        $redis->del($session_key);

        // 同时给用户发消息，提醒开启新话题
        $tenant_access_token = (new AuthModel())->tenantAccessTokenByAppType(AbstractFeishuModel::INTERNAL,
            GroupAssistantService::APP_ID, GroupAssistantService::APP_SECRET);
        (new MessageModel())->sendText($tenant_access_token, $union_id, '已开启新话题');

        return 0;
    }

    /**
     * 处理机器人菜单选择事件
     *
     */
    public function handlerGroupAssistantMenu($open_id, $state, $expired_at)
    {
        $logger = Helpers::getLogger('group_assistant_menu');
        $data = [
            'open_id'    => $open_id,
            'state'      => $state,
            'expired_at' => $expired_at
        ];
        $res = (new FeiShuGroupAssistantRemindModel())->insertUpdate($data);
        $logger->info('设置入库成功, 开始给用户发消息提醒', [$res]);

        // 获取机器人信息
        $tenant_access_token = (new AuthModel())->tenantAccessTokenByAppType(AbstractFeishuModel::INTERNAL,
            GroupAssistantService::APP_ID, GroupAssistantService::APP_SECRET);

        $text = "设置成功, {$expired_at}之前群小秘不再推送日程提醒";
        if ($state == FeiShuGroupAssistantRemindModel::STATE_NONE) {
            $text = "设置成功，不限制日程推送提醒";
        }
        if ($state == FeiShuGroupAssistantRemindModel::STATE_NEVER) {
            $text = "设置成功，群小秘不再推送日程提醒";
        }
        (new MessageModel())->sendText($tenant_access_token, $open_id, $text, 'open_id');
    }


    /**
     * 获取某个群聊的最后活跃时间
     *
     * @param $chat_id
     * @return false|int|mixed|\Redis|string
     * @throws RedisException
     */
    public function getChatLastMessageActivityTime($chat_id)
    {
        $key = self::CHAT_ACTIVITY_LAST_TIME_KEY . $chat_id;
        return RedisCache::getInstance()->get($key) ?: 0;
    }

    /**
     * 设置某个群聊的最后活跃时间
     *
     * @param $chat_id
     * @param $last_timestamp
     * @return bool|\Redis
     * @throws RedisException
     */
    public function setChatLastMessageActivityTime($chat_id, $last_timestamp)
    {
        $key = self::CHAT_ACTIVITY_LAST_TIME_KEY . $chat_id;
        return RedisCache::getInstance()->set($key, $last_timestamp);
    }

    /**
     * 从数据库拿出来的消息，格式化成大模型能识别的消息格式
     * 大概这样： "<msg>id: 时间：$create_time 发送者：$sender 发送内容：{{content}}</msg>";
     *
     * @param Collection $message_list
     * @param int $total_content_limit 总消息的内容长度限制 0表示不限制
     * @param int $content_limit 单条消息的内容长度限制 0表示不限制
     * @param int $post_limit
     * @return string
     */
    static public function formatToLLMMessageList(Collection $message_list, int $total_content_limit = 0, int $content_limit = 500, int $post_limit = 1000)
    {
        // 先把parent_id拿出来
        $parent_id_list = $message_list->filter(function ($item) {
            return $item->parent_id != '';
        })->pluck('parent_id')->toArray();
        $message_id_list = $message_list->pluck('message_id')->toArray();

        // 找出不在message_id_list里面的parent_id
        $missing_parent_ids = array_diff($parent_id_list, $message_id_list);

        // 再去拿一遍消息列表
        $missing_parent_id_message_list = (new FeiShuMessageModel())->getListByMessageIds($missing_parent_ids);
        if ($missing_parent_id_message_list->isNotEmpty()) {
            // 不为空，则合并
            $message_list = $missing_parent_id_message_list->merge($message_list);
        }

        // 先做一个消息id的映射
        $msg_id_map = [];
        foreach ($message_list as $index => $message) {
            $msg_id_map[$message->message_id] = $index;
        }


        $res_message_list = [];
        foreach ($message_list as $message) {
            $message_content = json_decode($message->format_message, true);
            $massage_content_format = self::formatLLMMessage($message_content, $msg_id_map, $content_limit, $post_limit);
            $res_message_list[] = $massage_content_format;
        }

        // 如果限制了总长度，还要倒序截取，倒序完还要再倒序回来，丢
        if ($total_content_limit) {
            // 先倒序
            $res_message_list = array_reverse($res_message_list);
            $res_message_list_limit = [];
            $total_count = 0;
            foreach ($res_message_list as $massage_content_format) {
                $res_message_list_limit[] = $massage_content_format;
                $total_count += mb_strlen($massage_content_format) - 79;// 79是消息格式固定字符的长度
                if ($total_count > $total_content_limit) {
                    break;
                }
            }
            // 再翻转过来
            $res_message_list = array_reverse($res_message_list_limit);
        }

        return implode("\n", $res_message_list);
    }


    /**
     * 格式化成大模型易懂的消息格式，消息id用自定义的（目前没有）
     *
     * @param $message
     * @param $msg_id_map
     * @param $content_limit
     * @param $post_limit
     * @return string
     */
    static public function formatLLMMessage($message, $msg_id_map, $content_limit, $post_limit)
    {
        $formatted_content = '';
        if (!isset($message['content'])) {
            return '';
        }


        // 判断一下，消息是否需要截断 飞书云文档不需要截断
        if (isset($message['cut_off']) && $message['cut_off'] == 0) {
            $content_limit = 10000; // TODO
            $post_limit = 10000;
        }

        // 检查是否有嵌套的内容
        if (is_array($message['content'])) {
            foreach ($message['content'] as $nested_message) {
                $formatted_content .= self::formatLLMMessage($nested_message, $msg_id_map, $content_limit, $post_limit) . "\n";
            }
        }

        // 如果转发的消息里面的id不存在我们系统MySQL表里面，则用回他自己的原始id TODO 由于分段问题，直接使用message_id
        $id_str = $msg_id_map[$message['message_id']] ?? $message['message_id'];
        $id_str = $message['message_id'];
        // 格式化当前消息
        $formatted_message = "<msg>id:$id_str 时间:{$message['message_create_time']} 发送者:{$message['sender']} 发送内容:";

        // 存在parent_id并且映射表也有的情况下，加上回复
        if ($message['parent_id']) {
//            $repay_id = $msg_id_map[$message['parent_id']] ?? $message['parent_id'];
            // TODO 由于分段问题，直接使用message_id
            $repay_id = $message['parent_id'];
            $formatted_message .= "回复了消息id为{$repay_id}的消息，回复内容:";
        }


        if ($formatted_content) {
            $formatted_message .= "转发了一份聊天记录，聊天记录内容是：\n{$formatted_content}";
        } else {
            // 富文本的消息还要再处理一下
            if (isset($message['msg_type']) && $message['msg_type'] === 'post') {
                $content = self::getPostContent($message['content'], $content_limit, $post_limit);
            } else {
                $content = self::cutContent($message['content'], $content_limit);
            }


            $formatted_message .= $content;
        }

        $formatted_message .= "</msg>";

        return $formatted_message;
    }

    /**
     * 格式化post的消息内容。变成纯文本形式
     *
     * @param $content
     * @param int $content_limit
     * @param int $post_limit
     * @return string
     */
    static public function getPostContent($content, int $content_limit = 500, int $post_limit = 200)
    {
        $text_content = '';
        $content = json_decode($content, true);
        if ($content['title']) {
            $text_content .= $content['title'] . "\n";
        }
        foreach ($content['content'] as $items) {
            if (empty($items)) {
                $text_content .= "\n";
            }
            foreach ($items as $item) {
                if ($item['tag'] === 'at' || $item['tag'] === 'text') {
                    $text_content .= self::cutContent($item['text'], $content_limit);
                }
                if ($item['tag'] === 'img') {
                    $img_desc = self::cutContent($item['text'], $post_limit);
                    $text_content .= "[图片]，图片内容：$img_desc\n";
                }
                if ($item['tag'] === 'code_block') {
                    $code = self::cutContent($item['text'], $post_limit);
                    $text_content .= "[代码块]\n```\n$code\n```\n";
                }
            }
        }
        return $text_content;
    }

    static public function cutContent($content, $content_limit)
    {
        if ($content_limit) {
            if (mb_strlen($content, 'UTF-8') > $content_limit) {
                $content = mb_substr($content, 0, $content_limit, 'UTF-8');
                $content .= "\n省略...";
            }
        }
        return $content;
    }

    /**
     * 总结云文档内容
     *
     * @param $last_summary
     * @param $current_content
     * @return mixed
     */
    public function docSummary($last_summary, $current_content)
    {
        $system_content = GroupAssistantPrompt::DOC_SUMMARY;

        $system_content = str_replace(['{{last_summary}}', '{{current_content}}'], [$last_summary, $current_content], $system_content);

        $messages = [
            [
                "role"    => "user",
                "content" => $system_content,
            ]
        ];


        $gpt_content = [
            "messages"    => $messages,
            "temperature" => 0.7,
            "top_p"       => 0.9,
            'model'       => $this->model,
            'stream'      => false,
            'log'         => '总结云文档内容',
        ];

        $open_ai_param = new OpenAIParam($gpt_content);
        $result = $this->openai_service->chat($open_ai_param);
        return $result['gpt_message'];
    }

    /**
     *
     * @return void
     */
    public function initUserChatInfo()
    {
        $logger = Helpers::getLogger('init_user_chat');

        $logger->info("开始处理，拉取所有的群");
        // 拉取所有的群
        $chat_list = (new FeiShuAssistantBotChatModel())->getALL();
        $logger->info("群列表拉取完成，开始一个个处理群", ['chat_list' => $chat_list]);

        // 一个个处理
        foreach ($chat_list as $chat) {
            $chat_id = $chat->chat_id;
            $this->handlerUserChat($chat_id);
        }

        $logger->info('所有任务处理完成');
    }

    public function handlerUserChat($chat_id)
    {
        $logger = Helpers::getLogger('init_user_chat');
        $auth_model = new AuthModel();
        $chat_model = new ChatsModel();
        $user_chat_model = new FeishuUserChatModel();
        // 先获取access_token
        $tenant_access_token = $auth_model->tenantAccessTokenByAppType(AbstractFeishuModel::INTERNAL,
            GroupAssistantService::APP_ID, GroupAssistantService::APP_SECRET);

        // 获取群成员
        $members = $chat_model->getALLChatMembers($tenant_access_token, $chat_id, 'union_id');
        $logger->info('群成员获取完成', ['chat_id' => $chat_id, 'members' => $members]);

        $insert_data = [];
        foreach ($members as $member) {
            $insert_data[] = ['union_id' => $member['member_id'], 'chat_id' => $chat_id];
        }

        $user_chat_model->addMultiple($insert_data);
        $logger->info('入库完成', ['chat_id' => $chat_id]);
    }

    /**
     * 出队 这里要把队列的所有元素拿出来
     *
     * @param $list_key
     * @return array
     * @throws RedisException
     */
    public function getSessionList($list_key)
    {
        $redis = RedisCache::getInstance();

        // 获取列表中的所有元素
        $list = $redis->lRange($list_key, 0, -1);

        foreach ($list as &$item) {
            $item = unserialize($item);
        }

        return $list;
    }

    /**
     * 历史记录入队列
     *
     * @param $list_key
     * @param $value
     * @return void
     * @throws RedisException
     */
    public function addToSessionList($list_key, $value)
    {
        $redis = RedisCache::getInstance();
        // 将元素推入列表的右侧

        $value = serialize($value);
        $redis->rPush($list_key, $value);

        // 设置列表的过期时间
        $redis->expire($list_key, self::SESSION_EXPIRE_TIME);

        // 获取列表中的所有元素
        $list = $redis->lRange($list_key, 0, -1);

        $count = count($list);
        $length = 0;
        foreach ($list as &$item) {
            $item = unserialize($item);
            $length += strlen($item[0]['content']);
            // 检查列表长度是否超过最大值,只有一个的时候不需要移除
            if ($length > self::SESSION_MAX_LENGTH_STR && $count > 1) {
                // 移除列表左侧的第一个元素
                $redis->lPop($list_key);
                break;
            }
        }
    }

    /**
     * 自旋锁
     *
     * @param $lock_id
     * @param int $max_wait_time
     */
    private function waitLock($lock_id, int $max_wait_time = 180)
    {
        $task = new GroupAssistantTask();
        $start_time = time();
        while (1) {
            if ($task->lock($lock_id)) {
                break;
            }

            // 检查是否已达到最大等待时间
            if ((time() - $start_time) > $max_wait_time) {
                Helpers::getLogger('group_assistant_lock')->info('已达最大等待时间，中断等待', ['lock_id' => $lock_id]);
                break;
            }
            sleep(1); // 等待 1 秒
        }
    }

    private function unlock($lock_id)
    {
        $task = new GroupAssistantTask();
        $task->unlock($lock_id);
    }

    public function getPostContentFormatMessage($message_id, $content, &$cut_off, $update = false, $level = 1)
    {
        $tenant_access_token = (new AuthModel())->tenantAccessTokenByAppType(AbstractFeishuModel::INTERNAL, GroupAssistantService::APP_ID, GroupAssistantService::APP_SECRET);
        foreach ($content['content'] as &$items) {
            if (empty($items)) {
                continue;
            }
            foreach ($items as &$item) {
                if ($item['tag'] === 'at') {
                    $item['text'] = "@{$item['user_name']}";
                }
                if ($item['tag'] === 'img' && $item['image_key']) {
                    $img_desc = MessageFormat::handlerImage($message_id, $item['image_key'], $level, true);
                    if ($img_desc === '[图片]' && $level === 1) {
                        $message_detail = (new MessageModel())->getMessageDetail($message_id, $tenant_access_token);
                        if (isset($message_detail['data']['items'][0])) {
                            $message_detail = $message_detail['data']['items'][0];
                            $content = json_decode($message_detail['body']['content'], true);
                            return $this->getPostContentFormatMessage($message_id, $content, $cut_off, $update, 2);
                        }
                    }
                    $item['text'] = $img_desc;
                }
                if ($item['tag'] === 'text') {
                    $item['text'] = MessageFormat::handlerText($item['text'], $message_id, $cut_off, $update);
                }
                // 其他类型原样保留
            }
        }
        return json_encode($content, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    }


    private function reactions($message_id)
    {
        $tenant_access_token = (new AuthModel())->tenantAccessTokenByAppType(AbstractFeishuModel::INTERNAL, self::APP_ID, self::APP_SECRET);

        $emoji_type = 'OK'; // 目前固定是OK
        try {
            (new MessageModel())->reactions($message_id, $emoji_type, $tenant_access_token);
        } catch (AppException $e) {
            Helpers::getLogger('grop_assistant')->err('发送表情回复出错', ['error_message' => $e->getMessage(), 's' => $e->getTraceAsString()]);
        }

    }
}