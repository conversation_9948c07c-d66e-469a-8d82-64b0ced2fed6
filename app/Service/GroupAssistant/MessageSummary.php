<?php

namespace App\Service\GroupAssistant;

use App\Exception\AppException;
use App\Model\HttpModel\Volcengine\Knowledge\AbstractKnowledgeModel;
use App\Model\HttpModel\Volcengine\Knowledge\DocModel;
use App\Model\HttpModel\Volcengine\Knowledge\PointModel;
use App\Model\SqlModel\Zeda\FeiShuAssistantBotChatModel;
use App\Model\SqlModel\Zeda\FeiShuAssistantUserMessageTimeModel;
use App\Model\SqlModel\Zeda\FeiShuDocMessagePointIDRelationModel;
use App\Model\SqlModel\Zeda\FeiShuMessageDocsModel;
use App\Model\SqlModel\Zeda\FeiShuMessageModel;
use App\Model\SqlModel\Zeda\FeishuMessageSummary;
use App\MysqlConnection;
use App\Param\Knowledge\VikingDBParam;
use App\Param\OpenAIParam;
use App\Service\FeiShuService;
use App\Service\OpenAI\OpenAI;
use App\Struct\RedisCache;
use App\Utils\Helpers;
use Common\EnvConfig;
use Exception;
use Illuminate\Support\Collection;
use Psr\Log\LoggerInterface;
use RedisException;

/**
 * 定时总结所有的消息
 */
class MessageSummary
{

    /**
     * 总结的内容长度
     * @var int
     */
    private $batch_size = 2000;

    protected $logger;

    // summary
    protected $path = EnvConfig::KNOWLEDGE_PATH;


    public function __construct()
    {
        $this->logger = Helpers::getLogger('chat_summary');
    }


    // 获取需要处理的群组列表
    private function getChatList()
    {
        return (new FeiShuAssistantBotChatModel())->getALL();
    }

    private function getAlLPersonList()
    {
        return (new FeiShuAssistantUserMessageTimeModel())->getALL();
    }

    /**
     * 获取单个群组的未处理消息（按时间排序）
     * @param $chat_id
     * @param $last_time
     * @param int $type 1 群聊 2个人私聊
     * @return Collection
     */
    private function getUnprocessedMessages($chat_id, $last_time, int $type = 1)
    {
        $model = new FeiShuMessageModel();
        if ($type === 1) {
            return $model->getListByChatIdAndLastTime($chat_id, $last_time);
        } else {
            return $model->getListByUnionIdAndLastTime($chat_id, $last_time);
        }

    }


    /**
     * 处理单个群(或者个人，当群处理)的消息
     * @param $chat_info
     * @param int $type 1=处理群组，2=处理个人
     * @return void
     */
    public function processChatMessage($chat_info, int $type = 1)
    {
        $logger = $this->logger;
        $chat_info['last_summary_message_date'] = date("Y-m-d H:i:s", $chat_info['last_summary_message_timestamp']);
        $logger->info("开始处理群总结", $chat_info);
        try {
            // 上次消息的处理时间 +1s
            $last_message_time = date("Y-m-d H:i:s", $chat_info['last_summary_message_timestamp'] + 1);
            $chat_id = $chat_info['chat_id'];
            $chat_name = $chat_info['chat_name'];

            $message_list = $this->getUnprocessedMessages($chat_id, $last_message_time, $type);
            if ($message_list->isEmpty()) {
                $logger->info("本轮没有消息，直接退出", $chat_info);
                return;
            }
            // 获取上次的群总结内容
            $last_summary_content = $this->getLastSummaryContent($chat_id);
            // 总消息长度
            $current_length = 0;
            // 消息池
            $message_batch = [];
            foreach ($message_list as $msg) {
                $format_message = json_decode($msg->format_message, true);
                $msg_length = $this->calculateContentLength($format_message);
                // 处理超大消息（单独处理）
                if ($msg_length >= $this->batch_size) {
                    if (!empty($message_batch)) {
                        $logger->info('单条消息触发阈值后，先处理之前的缓冲区', ['chat_id' => $chat_id]);
                        $this->createSummary($chat_id, $chat_name, $message_batch, $last_summary_content, $type);
                        $message_batch = [];
                        $current_length = 0;
                        $logger->info('单次处理结束', ['chat_id' => $chat_id]);
                    }

                    // 单独处理当前大消息
                    $logger->info('单独处理当前大消息，开始处理');
                    $this->createSummary($chat_id, $chat_name, [$msg], $last_summary_content, $type);
                    $logger->info('单次处理结束', ['chat_id' => $chat_id]);
                    continue;
                }

                // 添加消息到当前批次
                $message_batch[] = $msg;
                $current_length += $msg_length;

                // 检查是否需要立即处理
                if ($current_length >= $this->batch_size) {
                    $logger->info('触发阈值，开始处理', ['chat_id' => $chat_id]);
                    $this->createSummary($chat_id, $chat_name, $message_batch, $last_summary_content, $type);

                    $message_batch = [];
                    $current_length = 0;
                    $logger->info('单次处理结束', ['chat_id' => $chat_id]);
                }
            }
            $logger->info('本群消息处理结束', ['chat_id' => $chat_id]);
        } catch (\Throwable $e) {
            $logger->error('发生错误，结束处理，错误信息:' . $e->getMessage(), ['chat_id' => $chat_id]);
        }
    }


    /**
     * 总结消息
     *
     * @param $chat_id
     * @param $chat_name
     * @param $messages
     * @param $last_summary_content
     * @param int $type
     * @param bool $update_last_time
     * @return void
     * @throws Exception
     */
    private function createSummary($chat_id, $chat_name, $messages, $last_summary_content, int $type = 1, bool $update_last_time = true)
    {
        // 从消息里面获取开始时间和结束时间
        $message_list = Collection::make($messages);
        $end_time = $message_list->max('message_create_time');
        $start_time = $message_list->min('message_create_time');

        $start_timestamp = strtotime($start_time);
        $end_timestamp = strtotime($end_time);
        $start_date = date("Y-m-d", $start_timestamp);
        $end_date = date("Y-m-d", $end_timestamp);
//
//        // 文档的id用chat_id + 文档类型 + 开始日期
//        // 先去等待文档状态，有三个文档，先等待内容文档的就行，内容文档比较长。
//        $this->waitStatus($chat_id . '_summary_content_' . $start_date);

        try {
            MysqlConnection::getConnection()->beginTransaction();

            $this->logger->info('开始去LLM总结', ['chat_id' => $chat_id]);
            $messages_format_list = GroupAssistantService::formatToLLMMessageList($message_list, $this->batch_size + 5000);
            $summary = $this->generateSummary($messages_format_list, $last_summary_content);

            $this->logger->info('总结完成，开始入库', ['chat_id' => $chat_id, 'summary' => $summary]);

            $feishu_message_summary_model = new FeishuMessageSummary();
            // 总结内容入库
            $data = [
                'summary'     => $summary['summary_content'],
                'entity'      => json_encode($summary['summary_entity'], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES),
                'question'    => json_encode($summary['summary_question'], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES),
                'chat_id'     => $chat_id,
                'msg_id_list' => $message_list->pluck('message_id')->all(),
                'start_time'  => $start_time,
                'end_time'    => $end_time,
                'type'        => 1,
            ];
            $id = $feishu_message_summary_model->addOne($data);


            // 记录一个最后处理时间, 特殊情况下不需要处理这个时间，比如数据修复插入的时候
            if ($update_last_time) {
                $last_summary_message_time = strtotime($end_time);
                if ($type === 1) {
                    (new FeiShuAssistantBotChatModel())->updateLastSummaryMessageTime($chat_id, $last_summary_message_time);
                } else {
                    (new FeiShuAssistantUserMessageTimeModel())->updateLastSummaryMessageTimestamp($chat_id, $last_summary_message_time);
                }

                $this->logger->info('更新最后处理时间成功', ['chat_id' => $chat_id, 'last_summary_message_time' => date("Y-m-d H:i:s", $last_summary_message_time)]);
            }


            $this->logger->info('入库完成，开始上传到火山云的知识库', ['chat_id' => $chat_id, 'id' => $id]);
            // 添加切片，要添加三个切片，内容、实体、提问
            $param = new VikingDBParam([
                'chat_id'         => $chat_id,
                'chat_name'       => $chat_name,
                'start_date'      => $start_date,
                'end_date'        => $end_date,
                'start_timestamp' => $start_timestamp,
                'end_timestamp'   => $end_timestamp,
            ]);
            $point_id_list = [];
            foreach ($summary as $key => $content) {
                $param->type = $key;
                $param->doc_id = $chat_id . '_' . $key . '_' . $start_date;
                // 等待文档状态，这里要给多一点时间，耐心一下,给个十分钟吧
                $param->doc_info = $this->waitStatus($param->doc_id, 600);
                if ($key != 'summary_question') {
                    // 实体的话是聚合在一起当成一个切片
                    if (is_array($content)) {
                        $content = implode(' ', $content);
                    }
                    $content .= "\n<id>$id<id>";
                    $param->summary = $content;
                    $point_id = $this->summaryToVikingDB($param);
                    $point_id_list[] = ['point_id' => $point_id, 'type' => $key, 'doc_id' => $param->doc_id];
                } else {
                    // 提问的话，是一个提问一个切片, 最好判断一下是否为数组，因为大模型返回的东西，不好说
                    if (is_array($content)) {
                        foreach ($content as $content_item) {
                            $content_item .= "\n<id>$id<id>";
                            $param->summary = $content_item;
                            $point_id = $this->summaryToVikingDB($param);
                            $point_id_list[] = ['point_id' => $point_id, 'type' => $key, 'doc_id' => $param->doc_id];
                        }
                    } else {
                        $content .= "\n<id>$id<id>";
                        $param->summary = $content;
                        $point_id = $this->summaryToVikingDB($param);
                        $point_id_list[] = ['point_id' => $point_id, 'type' => $key, 'doc_id' => $param->doc_id];
                    }
                }
            }
            $this->logger->info('上传完成，开始处理云文档的消息关系', ['point_id_list' => $point_id_list, 'chat_id' => $chat_id]);

            $doc_in_message_list = (new FeiShuMessageDocsModel())->docInMessage($message_list->pluck('message_id')->toArray());
            $relation_data = [];
            foreach ($doc_in_message_list as $doc_in_message) {
                foreach ($point_id_list as $point_item) {
                    $relation_data[] = [
                        'message_id' => $doc_in_message->message_id,
                        'point_id'   => $point_item['point_id'],
                        'summary_id' => $id,
                        'type'       => $point_item['type'],
                        'doc_id'     => $point_item['doc_id'],
                    ];
                }
            }
            (new FeiShuDocMessagePointIDRelationModel())->addMultiple($relation_data);
            $this->logger->info('处理云文档的消息关系完成', ['relation_data' => $relation_data, 'chat_id' => $chat_id]);

            MysqlConnection::getConnection()->commit();
        } catch (AppException $exception) {
            $this->logger->error('发生app error， 本次总结任务结束。' . $exception->getMessage(), ['chat_id' => $chat_id]);
            MysqlConnection::getConnection()->rollBack();
            throw new AppException('发生app error， 本次总结任务结束。');
        } catch (\Throwable $throwable) {
            $this->logger->error('发生未知错误，错误信息:' . $throwable->getMessage(), ['chat_id' => $chat_id]);
            MysqlConnection::getConnection()->rollBack();
            throw new AppException('发生未知错误');
        }
    }


    /**
     * 总结同步到向量数据库
     *
     * @param VikingDBParam $param
     * @return mixed|void
     */
    public function summaryToVikingDB(VikingDBParam $param)
    {
        if ($param->doc_info['code'] == 1001001) {
            $this->logger->info('需要上传文档', ['doc_id' => $param->doc_id]);
            $param->setDocName();
            $this->addDocument($param);

            return $this->getPointID($param);

        } elseif ($param->doc_info['code'] == 0 && $param->doc_info['data']) {
            $this->logger->info('文档存在，直接添加切片', ['doc_id' => $param->doc_id]);
            $point_id = $this->addPoint($param->doc_id, $param->summary);

            $this->logger->info('切片添加完成，开始更新meta', ['point_id' => $point_id, 'doc_id' => $param->doc_id]);
            $this->updateMeta($param);
            $this->logger->info('更新meta成功', ['point_id' => $point_id, 'doc_id' => $param->doc_id]);
            return $point_id;
        } else {
            // 不知道什么情况，先抛个异常再说
            $this->logger->error('查询文档出现未知情况', ['info' => $param->doc_info, 'doc_id' => $param->doc_id]);
            throw new AppException('查询文档出现未知情况');
        }

    }

    public function getPointID(VikingDBParam $param)
    {
        $doc_id = $param->doc_id;
        $param->doc_info = $this->waitStatus($doc_id);
        $res = (new PointModel())->list($doc_id);
        $point_list = $res['data']['point_list'];
        if (isset($point_list[0])) {
            $this->logger->info('获取到了切片id', ['point_id' => $point_list[0]['point_id'], 'doc_id' => $doc_id]);
            return $point_list[0]['point_id'];
        }

        // 到这里说明文档出现问题
        $this->logger->error('添加完文档获取切片出错', ['doc_id' => $doc_id]);
        throw new AppException('添加完文档获取切片出错');
    }


    public function updateMeta(VikingDBParam $param)
    {
        $meta = [
            [
                'field_name'  => 'chat_id',
                'field_type'  => 'string',
                'field_value' => $param->chat_id,
            ],
            [
                'field_name'  => 'type',
                'field_type'  => 'string',
                'field_value' => $param->type,
            ],
            [
                'field_name'  => 'start_date',
                'field_type'  => 'string',
                'field_value' => $param->start_date,
            ],
            [
                'field_name'  => 'end_date',
                'field_type'  => 'string',
                'field_value' => $param->end_date,
            ],
            [
                'field_name'  => 'start_timestamp',
                'field_type'  => 'int64',
                'field_value' => $param->start_timestamp,
            ],
            [
                'field_name'  => 'end_timestamp',
                'field_type'  => 'int64',
                'field_value' => $param->end_timestamp,
            ],
        ];
        (new DocModel())->updateMeta($param->doc_id, $meta);
    }


    /**
     * 添加切片，返回切片id
     *
     * @param $doc_id
     * @param $summary
     * @return mixed
     */
    public function addPoint($doc_id, $summary)
    {
        $this->waitStatus($doc_id);
        $res = (new PointModel())->add($doc_id, $summary);
        if ($res['code'] == 0) {
            $this->logger->info('切片添加成功', ['res' => $res, 'doc_id' => $doc_id]);
            return $res['data']['point_id'];
        } else {
            $this->logger->error('切片添加失败', ['res' => $res, 'doc_id' => $doc_id]);
            throw new AppException($res['message']);
        }

    }

    /**
     * 添加文档， 需要把文档存入到upload目录，因为需要URL能访问的地方才可以传
     * @return void
     */
    public function addDocument(VikingDBParam $param)
    {
        // 先写文件
        $filename = $this->path . '/' . $param->doc_id;
        file_put_contents($filename, $param->summary, LOCK_EX);

        // 写完上传
        $url = EnvConfig::DOMAIN . '/upload/summary/' . $param->doc_id;
        $meta = [
            [
                'field_name'  => 'chat_id',
                'field_type'  => 'string',
                'field_value' => $param->chat_id,
            ],
            [
                'field_name'  => 'type',
                'field_type'  => 'string',
                'field_value' => $param->type,
            ],
            [
                'field_name'  => 'start_date',
                'field_type'  => 'string',
                'field_value' => $param->start_date,
            ],
            [
                'field_name'  => 'end_date',
                'field_type'  => 'string',
                'field_value' => $param->end_date,
            ],
            [
                'field_name'  => 'start_timestamp',
                'field_type'  => 'int64',
                'field_value' => $param->start_timestamp,
            ],
            [
                'field_name'  => 'end_timestamp',
                'field_type'  => 'int64',
                'field_value' => $param->end_timestamp,
            ],
        ];
        $res = (new DocModel())->add($param->doc_id, $param->doc_name, $url, $meta);
        if ($res['code'] == 0) {
            $this->logger->info('文件上传成功', ['res' => $res]);
            // 延迟删除
            (new FileDeletionManager())->addFileToQueue($filename);
        } else {
            $this->logger->error('文件上传失败', ['res' => $res, 'doc_id' => $param->doc_id]);
            throw new AppException($res['message']);
        }


    }


    /**
     * 生成摘要
     *
     * @param $message_list
     * @param $last_summary_content
     * @return array
     */
    public function generateSummary($message_list, $last_summary_content)
    {
        $system_content = GroupAssistantPrompt::QA_SUMMARY;

        // 根据内容长度动态生成提问的问题个数，最小5，最大50。
        $length = mb_strlen($system_content);
        $num = ceil($length / 500);
        $question_num = max(5, min(50, $num)); // 一行代码实现范围限制

        $system_content = str_replace(['{{message_list}}', '{{last_summary_content}}', '{{question_num}}'], [$message_list, $last_summary_content, $question_num], $system_content);

//        $system_content = self::limitMaxToken($system_content);
        $messages = [
            [
                "role"    => "user",
                "content" => $system_content,
            ]
        ];


        $gpt_content = [
            "messages"    => $messages,
            "temperature" => 0.5,
            "top_p"       => 0.9,
            'log'         => '定时去总结对话内容',
        ];

        $open_ai_param = new OpenAIParam($gpt_content);
        return (new OpenAI())->chatByJson($open_ai_param);
    }

    /**
     * 计算消息长度
     * @param $data
     * @return false|int
     */
    private function calculateContentLength($data)
    {
        if (!isset($data['content'])) {
            return 0;
        }
        // 检查 content 是否为 JSON 格式的字符串
        if (is_array($data['content'])) {
            $nested_contents = $data['content'];
            $total_length = 0;
            foreach ($nested_contents as $nested_content) {
                $total_length += $this->calculateContentLength($nested_content);
            }
            return $total_length;
        } else {
            // 计算当前 content 的字符数
            return mb_strlen($data['content']);
        }
    }

    /**
     * 获取执行锁，用来判断当前任务是否正在执行。
     * 锁住的最大时间24小时
     *
     * @param $chat_id
     *
     * @return bool  获锁成功返回true 否则返回false
     * @throws RedisException
     */
    public function lock($chat_id)
    {
        $key = 'running_handler_message_summary_' . $chat_id;
        if (RedisCache::getInstance()->set($key, '1', ['NX', 'EX' => 86400])) {
            $this->logger->info('成功获取执行锁', ['chat_id' => $chat_id]);
            return true;
        } else {
            $this->logger->info('获取执行锁失败', ['chat_id' => $chat_id]);
            return false;
        }
    }

    /**
     * 解锁
     *
     * @param $chat_id
     * @throws RedisException
     */
    public function unlock($chat_id)
    {
        $key = 'running_handler_message_summary_' . $chat_id;
        RedisCache::getInstance()->del($key);
        $this->logger->info('解锁成功', ['chat_id' => $chat_id]);
    }

    public function getLastSummaryContent($chat_id, $summary_id = 0)
    {
        $model = new FeishuMessageSummary();
        $data = $model->getLastSummaryContent($chat_id, $summary_id);
        if ($data) {
            return $data->summary;
        }
        return '';
    }


    /**
     * 等待文档的状态
     *
     * @param $doc_id
     * @param int $max_wait_time
     * @param string $resource_id
     * @return array
     */
    public function waitStatus($doc_id, int $max_wait_time = 600, string $resource_id = AbstractKnowledgeModel::SUMMARY_RESOURCE_ID)
    {
        // 先去判断一下，这个群的文档的状态，如果处理中，则等待
        $doc_info = (new DocModel())->info($doc_id, $resource_id);
        if ($doc_info['code'] == 0 && $doc_info['data']) {
            $start_time = time();
            while (1) {
                // 判断一下文档是否在处理中
                $process_status = $doc_info['data']['status']['process_status'];
                // 文档的处理状态，0表示处理完成，1表示处理失败，2或3表示排队中，5表示删除中，6表示处理中
                if (in_array($process_status, [2, 3, 5, 6])) {
                    $this->logger->info('当前文档还在处理中，先不操作，先等待一会', ['doc_id' => $doc_id]);
                } else {
                    break;
                }
                // 检查是否已达到最大等待时间
                if ((time() - $start_time) > $max_wait_time) {
                    $this->logger->error('已达最大等待时间，中断等待', ['doc_id' => $doc_id]);
                    throw new AppException('已达最大等待时间，中断等待');
                }

                sleep(3); // 等待 3 秒
                // 更新文档状态
                $doc_info = (new DocModel())->info($doc_id, $resource_id);
            }

        }

        return $doc_info;
    }


    /**
     * 更新切片
     *
     * @param $point_id
     * @param $doc_id
     * @param $content
     * @return void
     */
    public function updatePoint($point_id, $doc_id, $content)
    {
        // 等待文档状态
        $this->waitStatus($doc_id);

        // 更新切片
        (new PointModel())->update($point_id, $content);
    }

    /**
     * @return void
     */
    public function run()
    {
        $this->logger->info('本轮任务开始');
        // 获取所有的群列表
        $chat_list = $this->getChatList();
        // 一个个运行
        foreach ($chat_list as $chat_info) {
            $chat_info = (array)$chat_info;
//            if ($chat_info['chat_id'] !== 'oc_e4bf54ccfbd78a40150a7b4380f06a39') {
//                continue;
//            }

            if (!$this->lock($chat_info['chat_id'])) {
                $this->logger->info('等待下一轮任务', ['chat_id' => $chat_info['chat_id']]);
                // 等下一轮
                continue;
            }
            $this->processChatMessage($chat_info);
            $this->unlock($chat_info['chat_id']);
        }

        $this->logger->info('本轮任务结束');
    }

    public function runPerson()
    {
        $this->logger->info('本轮任务开始(个人）');
        // 获取所有的群列表
        $chat_list = $this->getAlLPersonList();
        // 一个个运行
        foreach ($chat_list as $chat_info) {
            $chat_info = (array)$chat_info;
            // 用union_id和用户姓名替代chat_id和chat_name
            $chat_info['chat_id'] = $chat_info['union_id'];
            $chat_info['chat_name'] = FeiShuService::getNameByUnionId($chat_info['union_id']) ?: $chat_info['union_id'];

            if (!$this->lock($chat_info['chat_id'])) {
                $this->logger->info('等待下一轮任务', ['chat_id' => $chat_info['chat_id']]);
                // 等下一轮
                continue;
            }
            $this->processChatMessage($chat_info, 2);
            $this->unlock($chat_info['chat_id']);
        }

        $this->logger->info('本轮任务结束(个人）');
    }

    /**
     * 修复临时聊天记录的时候需要用到这个，
     * 重新添加切片
     *
     * @return void
     */
    public function addSummary()
    {
        $logger = $this->logger;
        $logger->info("开始处理群总结");
        $chat_id = 'oc_e915f7cb432bcb33d7f4c98aa2690f34';
        $chat_name = '运营大佬带飞';
        $type = 1;
        try {

            $message_ids = [
                "om_x100b49f94b0b9af40f3dc4743a774e1",
                "tw_2352d686cd0824128c",
                "tw_3ad51686cd83f39735",
                "om_x100b49e45c765c080f1969424093611",
                "tw_ce305686d062addee2",
                "om_x100b49c2d3e160a00f4298d48669a25",
                "om_x100b49c2d0b6f0c00f23bf57d44fb79",
                "om_x100b49c2d1a900480f2e7b10b342ecf",
                "tw_3e058686f69fc2fef6",
                "tw_ce305686f69ffdac32",
                "tw_ce305686f6a04a0a2d",
                "om_x100b49c2ec965e600f347e0f49af0f4",
                "tw_2352d686f6a280e007",
                "tw_2acad686f6c7a99893",
                "om_x100b49c28663e4000f1376e552d49bc",
                "om_x100b49c295571d7c0e393b21a967b01",
                "tw_3ad51686f6e3494d7c",
                "tw_2352d686f6e8f84cf6",
                "om_x100b487ed87bc6640f1996800f05d20",
                "tw_2352d6874aa7aea78d",
                "om_x100b487ee327205c0f2c656d71048ef",
                "tw_2352d6874aae84e204",
                "om_x100b487f1de336a00f2f0d981ba3dad",
                "tw_ce3056874b6b586916",
                "tw_2352d6874b750cf787",
                "tw_3ad516874b8d8f12dd",
                "om_x100b4841e1c08eb40f27a474d5b0b84",
                "om_x100b4841e15700300f159efcc2053e2",
                "tw_3d78768775b78b37ee",
                "om_x100b4841f1e06d1c0f33d1b114a9893",
                "tw_2352d68775c094a2ec",
                "tw_2352d68775c0aa13af",
                "om_x100b48418080e4f80f262bcbfa1f0c9",
                "om_x100b484181ac74540f4815cae81837f",
                "om_x100b48419e7400740f1798445c40e68",
                "om_x100b48419fe9ab2c0f4067ffff276a0",
                "tw_2352d68775d15f05ff",
                "om_x100b48419c11dc8c0f4e95bc45e30a2",
                "om_x100b48419dec1b500e334fc47c0c711",
                "tw_2352d68775d413e008",
                "tw_2352d68775d4362f75",
                "tw_2352d68775d46b2398",
                "om_x100b48419a7674c40f49cc544c57a08",
                "om_x100b4841afd31fb40f2e46a6f5b0047",
                "om_x100b4841af15f4980f15d4a913a4b3d",
                "tw_2352d68775e2097d5f",
                "tw_ce30568775e2ec0d33",
                "tw_3ad5168775e50afd42",
                "tw_2352d68775e73ae85e",
                "tw_2352d68775e75a1992",
                "om_x100b4841a94778440f4d8fc4fb46cce",
                "om_x100b4841a6f65df80f1ca0e553c4449",
                "om_x100b4841a78c15680f40569d1b04508",
                "om_x100b49c2d29c859c0f310f53c002a3c",
            ];

            $message_list = (new FeiShuMessageModel())->getListByMessageIds($message_ids);

            // 获取上次的群总结内容
            $last_summary_content = $this->getLastSummaryContent($chat_id, 9309);

            // 消息池
            $message_batch = $message_list->toArray();
            $this->createSummary($chat_id, $chat_name, $message_batch, $last_summary_content, $type, false);
            $logger->info('本群消息处理结束', ['chat_id' => $chat_id]);
        } catch (\Throwable $e) {
            $logger->error('发生错误，结束处理，错误信息:' . $e->getMessage(), ['chat_id' => $chat_id]);
        }
    }
}