<?php

namespace App\Controller\API;


use App\Constant\RankMaterial;
use App\Constant\ResponseCode;
use App\Container;
use App\Controller\LY\Controller as LYController;
use App\Exception\AppException;
use App\Logic\API\PermissionLogic;
use App\Logic\UserLogic;
use App\Model\SqlModel\DatahubLY\ViewV2DimSiteIdModel;
use App\Model\SqlModel\Zeda\AgentGroupModel;
use App\Model\SqlModel\Zeda\UserModel;
use App\Model\SqlModel\Zeda\ViewPositionModel;
use App\Param\UserParam;
use App\Struct\Input;
use App\Struct\Session;
use Exception;

class PermissionController extends Controller
{
    /**
     * 设置用户session
     * @param Input $input
     * @return void
     */
    protected function setUserSession(Input $input): void
    {
        if (empty($input['staff_number'] ?? '') && empty($input['user_id'] ?? '')) {
            throw new AppException('员工编号或员工ID必传');
        }

        $user_id = $input['user_id'] ?? 0;
        $user_info = [];
        if (!$user_id) {
            $user_info = (new UserModel())->getDataByStaffNumber($input['staff_number'], 1, $input['search_all'] ?? false);
            if (empty($user_info)) {
                throw new AppException('找不到该用户');
            }

            $user_id = $user_info['id'];
        }

        // 设置用户token
        $token = $input['staff_number'] . '_' . $user_id;
        $session = new Session($token);
        Container::setSession($session);
        if (!(Container::getSession()->user_id > 0)) {
            if (!$user_info) {
                $user_info = (array)(new UserModel())->getData($user_id);
                if (empty($user_info)) {
                    throw new AppException('找不到该用户');
                }
            }
            $session->mSet((new UserLogic())->getLoginSessionData($user_info));
        }
    }

    /**
     * 获取用户所能看到的岗位
     * @CtrlAnnotation(log_type='get', throttle=1000)
     * @param Input $input
     * @return array
     */
    public function getPositionCascader(Input $input)
    {
        $input->verify([
            'module',
            'user_id'
        ]);

        $this->setUserSession($input);

        [$level, $rank_id] = (new PermissionLogic())->getLevelRankByModuleUserId($input['module'], $input['user_id']);

        $card_list = (new ViewPositionModel())->getNextRankByPosition($input['module'], $level, $rank_id);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $card_list
        ];
    }

    /**
     * 获取所选岗位的平台权限
     * @CtrlAnnotation(log_type='get', throttle=1000)
     * @param Input $input
     * @return array
     */
    public function getRankPlatformPermission(Input $input)
    {
        $input->verify([
            'module', 'level', 'rank_id'
        ]);

        $this->setUserSession($input);

        $rank_platform_permission = (new PermissionLogic())->getRankPlatformPermission($input['module'], $input['level'], $input['rank_id']);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $rank_platform_permission
        ];
    }

    /**
     * 获取所选岗位的平台负责人权限
     * @CtrlAnnotation(log_type='get', throttle=1000)
     * @param Input $input
     * @return array
     */
    public function getRankPlatformLeaderPermission(Input $input)
    {
        $input->verify([
            'module', 'level', 'rank_id'
        ]);

        $this->setUserSession($input);

        $list = (new PermissionLogic())->getRankPlatformPermission($input['module'], $input['level'], $input['rank_id']);
        $platform_list = [];

        foreach ($list as $item) {
            $platform_list[] = [
                'platform' => $item['platform'],
                'platform_cn' => $item['platform_cn'],
                'agent_leaders' => $item['agent_leaders'],
            ];
        }

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $platform_list
        ];
    }

    /**
     * 添加用户
     * @CtrlAnnotation(log_type='add', throttle=1000)
     * @param Input $input
     * @return array
     * @throws Exception
     */
    public function addUser(Input $input)
    {
        $input->verify([
            'user_id',
            'account',
            'name',
            'module',
            'platform',
            'level',
            'rank_id',
            'self_staff_number'
        ]);

        $this->setUserSession($input);

        $param = new UserParam($input->getData());
        $param->creator = Container::getSession()->name;
        // 本次申请本人的工号
        $param->staff_number = $input['self_staff_number'];

        // 默认密码
        if (empty($input['password'] ?? '')) {
            $param->password = 'Aa123456';
        }

        $agent_leaders = $input['agent_leaders'] ?? [];

        // 获取所选岗位的平台负责人权限
        // 检查负责人是否与所选岗位一样 不一样要新建岗位
        $is_new_rank = false;
        $logic = new PermissionLogic();
        $rank_platform_permission = $logic->getRankPlatformPermission($input['module'], $input['level'], $input['rank_id']);
        foreach ($rank_platform_permission as $platform_permission) {
            if ($platform_permission['platform'] === $input['platform']) {
                $platform_agent_leaders = $platform_permission['agent_leaders'];
                $new_diff = array_diff($agent_leaders, $platform_agent_leaders);
                $old_diff = array_diff($platform_agent_leaders, $agent_leaders);
                if (!empty($new_diff) || !empty($old_diff)) {
                    $is_new_rank = true;
                }
            }
        }

        if ($is_new_rank) {
            $result = (new PermissionLogic())->addRankPermission(
                $input['module'], $input['level'], $input['rank_id'], $input['name'],
                [
                    $input['platform'] => $agent_leaders
                ]
            );

            if (!$result['result']) {
                throw new AppException("添加岗位权限失败：{$result['msg']}");
            }

            $level = $result['data']['level'];
            $rank_id = $result['data']['rank_id'];
        } else {
            $level = $input['level'];
            $rank_id = $input['rank_id'];
        }

        // 查询岗位信息
        $card_info = (new ViewPositionModel())->getDataByModuleLevelRank($input['module'], $level, $rank_id);
        if (empty($card_info)) {
            throw new AppException("岗位卡片不存在");
        }

        $param->level = $level;
        $param->platform_id = $card_info['platform_id'];
        $param->department_id = $card_info['department_id'];
        $param->department_group_id = $card_info['department_group_id'];
        $param->department_group_position_id = $card_info['department_group_position_id'];
        $param->department_group_position_worker_id = $card_info['department_group_position_worker_id'];
        $param->department_six_id = $card_info['department_six_id'];
        $param->department_seven_id = $card_info['department_seven_id'];
        $param->department_eight_id = $card_info['department_eight_id'];

        // 判断是否可以关联账号
        $logic = new UserLogic();
        $can_link = $logic->canLink($param->account, $input['module']);
        if ($can_link['can_link']) {
            $logic->linkUser($param);
        } else {
            // 添加用户账号
            $logic->addUser($param);
        }

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '添加成功',
        ];
    }

    /**
     * 获取本人账户列表
     * @CtrlAnnotation(log_type='get', throttle=1000)
     * @param Input $input
     * @return array
     * @throws Exception
     */
    public function getSelfAccountList(Input $input)
    {
        $input->verify([
            'module',
        ]);

        // 1. 先判断有没有后台账号
        // 2. 没有的话拿飞书组织架构上的上级
        // 3. 返回上级所拥有的账号
        $staff_number = $input['staff_number'] ?? '';
        $leader_staff_number = $input['leader_staff_number'] ?? '';
        $user_list = (new UserModel())->getUserListInStaffNumbers($input['module'], [$staff_number, $leader_staff_number], 1, $input['search_all'] ?? false);
        if (empty($user_list)) {
            throw new AppException('找不到该用户');
        }

        $search_staff_number = $staff_number;

        $user_info = $user_list->where('staff_number', $staff_number)->toArray();
        if (empty($user_info)) {
            if ($leader_staff_number) {
                $leader_user_info = (array)$user_list->where('staff_number', $leader_staff_number)->first();
                if (empty($leader_user_info)) {
                    throw new AppException('找不到该用户上级的信息');
                }

                $input->setDataField('staff_number', $leader_staff_number);
                $search_staff_number = $leader_staff_number;
            } else {
                return [
                    'code' => ResponseCode::SUCCESS,
                    'message' => '获取成功',
                    'data' => []
                ];
            }
        }

        $this->setUserSession($input);

        $list = (new UserModel())->getUserListByModuleStaffNumber($input['module'], $search_staff_number, 1, $input['search_all'] ?? false);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list
        ];
    }

    /**
     * 获取本人账号及所有下级账号列表
     * @CtrlAnnotation(log_type='get', throttle=1000)
     * @param Input $input
     * @return array
     * @throws Exception
     */
    public function getSubAccountList(Input $input)
    {
        $input->verify([
            'module',
            'user_id'
        ]);

        $this->setUserSession($input);

        [$level, $rank_id] = (new PermissionLogic())->getLevelRankByModuleUserId($input['module'], $input['user_id']);

        // 可查包含已删除的账号 也可查正常的账号
        $list = (new UserModel())->getActiveNextLevelByRankId($rank_id, $level, 1, $input['search_all'] ?? false);

        $list->prepend(['id' => Container::getSession()->id, 'name' => Container::getSession()->name, 'module' => $input['module']]);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list
        ];
    }

    /**
     * 获取所选岗位的上级岗位的平台负责人权限
     * @CtrlAnnotation(log_type='get', throttle=1000)
     * @param Input $input
     * @return array
     * @throws Exception
     */
    public function getRankLeaderPlatformLeaderPermission(Input $input)
    {
        $input->verify([
            'module',
            'level',
            'rank_id'
        ]);

        $this->setUserSession($input);

        $logic = new PermissionLogic();
        [$leader_level, $leader_rank_id] = $logic->getSingleLeaderLevelRank($input['module'], $input['level'], $input['rank_id']);

        $list = (new PermissionLogic())->getRankPlatformPermission($input['module'], $leader_level, $leader_rank_id);
        $platform_list = [];

        foreach ($list as $item) {
            $platform_list[] = [
                'platform' => $item['platform'],
                'platform_cn' => $item['platform_cn'],
                'agent_leaders' => $item['agent_leaders'],
            ];
        }

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $platform_list
        ];
    }

    /**
     * 获取上级岗位的路由权限
     * @CtrlAnnotation(log_type='get', throttle=1000)
     * @param Input $input
     * @return array
     * @throws Exception
     */
    public function getLeaderRoutePermissionOptions(Input $input)
    {
        $input->verify([
            'module',
            'user_id'
        ]);

        $this->setUserSession($input);

        [$level, $rank_id] = (new PermissionLogic())->getLevelRankByModuleUserId($input['module'], $input['user_id']);

        $logic = new PermissionLogic();
        [$leader_level, $leader_rank_id] = $logic->getSingleLeaderLevelRank($input['module'], $level, $rank_id);
        $list = $logic->getRoutePermissionOptions($input['module'], $leader_level, $leader_rank_id);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list
        ];
    }

    /**
     * 获取上级岗位的平台权限
     * @CtrlAnnotation(log_type='get', throttle=1000)
     * @param Input $input
     * @return array
     * @throws Exception
     */
    public function getLeaderPlatformPermissionOptions(Input $input)
    {
        $input->verify([
            'module',
            'user_id'
        ]);

        $this->setUserSession($input);

        [$level, $rank_id] = (new PermissionLogic())->getLevelRankByModuleUserId($input['module'], $input['user_id']);

        $logic = new PermissionLogic();
        [$leader_level, $leader_rank_id] = $logic->getSingleLeaderLevelRank($input['module'], $level, $rank_id);
        $list = $logic->getRankPlatformPermission($input['module'], $leader_level, $leader_rank_id);

        $platform_list = [];
        $agent_group = (new AgentGroupModel())->getAll()->keyBy('id')->toArray();

        $is_ly_module = $input['module'] === LYController::MODULE;
        if ($is_ly_module) {
            $platforms = array_column($list, 'platform');
            $ly_agent_group_list = (new ViewV2DimSiteIdModel())->getAllAgentGroupInPlatformList($platforms);
        }

        foreach ($list as $item) {
            // 回显渠道组名字
            $agent_group_list = [];
            if ($is_ly_module) {
                $platform_ly_agent_group_list = $ly_agent_group_list->where('platform', $item['platform'])->keyBy('agent_group_id')->toArray();
                foreach ($item['agent_group_list'] as $agent_group_id) {
                    $agent_group_list[] = [
                        'agent_group_id' => $agent_group_id,
                        'agent_group_name' => $platform_ly_agent_group_list[$agent_group_id]->agent_group_name ?: '其他'
                    ];
                }
            } else {
                foreach ($item['agent_group_ids'] as $agent_group_id) {
                    $agent_group_list[] = [
                        'agent_group_id' => $agent_group_id,
                        'agent_group_name' => $agent_group[$agent_group_id]->name ?? ''
                    ];
                }
            }

            // 发行中心是以子游戏为权限 需要转成跟其他平台一样是根游戏
            $tmp_root_game_list = [];
            if ($is_ly_module) {
                foreach ($item['game_list'] as $game) {
                    $tmp_root_game_list[] = [
                        'root_game_id' => $game['game_id'],
                        'root_game_name' => $game['game_name'],
                        'plat_id' => 0,
                    ];
                }
            } else {
                $tmp_root_game_list = $item['root_game_list'];
            }

            $platform_list[] = [
                'platform' => $item['platform'],
                'platform_cn' => $item['platform_cn'],
                'root_game_list' => $tmp_root_game_list,
                'agent_leaders' => $item['agent_leaders'] ?? [],
                'agent_group_list' => $agent_group_list
            ];
        }

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $platform_list
        ];
    }

    /**
     * 获取上级岗位的素材权限
     * @CtrlAnnotation(log_type='get', throttle=1000)
     * @param Input $input
     * @return array
     * @throws Exception
     */
    public function getLeaderMaterialPermissionOptions(Input $input)
    {
        $input->verify([
            'module',
            'user_id'
        ]);

        $this->setUserSession($input);

        [$level, $rank_id] = (new PermissionLogic())->getLevelRankByModuleUserId($input['module'], $input['user_id']);

        $logic = new PermissionLogic();
        [$leader_level, $leader_rank_id] = $logic->getSingleLeaderLevelRank($input['module'], $level, $rank_id);
        $list = $logic->getRankPlatformPermission($input['module'], $leader_level, $leader_rank_id);

        $platform_list = [];
        foreach ($list as $item) {
            // 把素材库和素材效果的题材分开
            $material_store_theme = [];
            $material_effect_theme = [];
            foreach ($item['material_theme_list'] as $material_theme) {
                $material_theme = (array)$material_theme;
                if ($material_theme['type'] == RankMaterial::TYPE_STORE) {
                    $material_store_theme[] = [
                        'theme_id' => $material_theme['theme_id'],
                        'theme_name' => $material_theme['theme_name'],
                        'type' => $material_theme['type']
                    ];
                } elseif ($material_theme['type'] == RankMaterial::TYPE_EFFECT) {
                    $material_effect_theme[] = [
                        'theme_id' => $material_theme['theme_id'],
                        'theme_name' => $material_theme['theme_name'],
                        'type' => $material_theme['type']
                    ];
                }
            }

            $platform_list[] = [
                'platform' => $item['platform'],
                'platform_cn' => $item['platform_cn'],
                'material_store_theme' => $material_store_theme,
                'material_effect_theme' => $material_effect_theme,
            ];
        }

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $platform_list
        ];
    }

    /**
     * 路由权限调整
     * @CtrlAnnotation(log_type='edit', throttle=1000)
     * @param Input $input
     * @return array
     * @throws Exception
     */
    public function syncRoutePermission(Input $input)
    {
        $input->verify([
            'module',
            'sync_type',
            'user_id',
            'sync_user_ids'
        ]);

        $this->setUserSession($input);

        $logic = new PermissionLogic();
        $data = $logic->syncRoutePermission(
            $input['sync_type'], $input['module'], $input['sync_user_ids'],
            $input['route_list'] ?? '', $input['route_permission_ids'] ?? []
        );

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '调整成功',
            'data' => $data
        ];
    }

    /**
     * 数据权限调整
     * @CtrlAnnotation(log_type='edit', throttle=1000)
     * @param Input $input
     * @return array
     * @throws Exception
     */
    public function syncPlatformPermission(Input $input)
    {
        $input->verify([
            'module',
            'sync_type',
            'user_id',
            'sync_user_ids'
        ]);

        $this->setUserSession($input);

        $logic = new PermissionLogic();
        $data = $logic->syncPlatformPermission(
            $input['sync_type'], $input['module'], $input['sync_user_ids'],
            $input['platform_list'] ?? ''
        );

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '调整成功',
            'data' => $data
        ];
    }

    /**
     * 素材权限调整
     * @CtrlAnnotation(log_type='edit', throttle=1000)
     * @param Input $input
     * @return array
     * @throws Exception
     */
    public function syncMaterialPermission(Input $input)
    {
        $input->verify([
            'module',
            'sync_type',
            'user_id',
            'sync_user_ids'
        ]);

        $this->setUserSession($input);

        $logic = new PermissionLogic();
        $data = $logic->syncMaterialPermission(
            $input['sync_type'], $input['module'], $input['sync_user_ids'],
            $input['platform_list'] ?? ''
        );

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '调整成功',
            'data' => $data
        ];
    }

    /**
     * 删除中台账号
     * @CtrlAnnotation(log_type='delete', throttle=1000)
     * @param Input $input
     * @return array
     * @throws Exception
     */
    public function deleteUser(Input $input)
    {
        $input->verify([
            'delete_user_ids'
        ]);

        $this->setUserSession($input);

        $logic = new PermissionLogic();
        $logic->deleteUser($input['delete_user_ids']);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '删除成功',
        ];
    }

    /**
     * 判断用户是否存在
     * @CtrlAnnotation(log_type='get', throttle=1000)
     * @param Input $input
     * @return array
     * @throws Exception
     */
    public function checkUserExist(Input $input)
    {
        $input->verify([
            'user_id',
            'account',
            'name',
            'module',
            'platform',
            'level',
            'rank_id',
        ]);

        $this->setUserSession($input);

        $param = new UserParam($input->getData());

        // 判断是否可以关联账号
        $logic = new PermissionLogic();

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $logic->checkUserExist($param)
        ];
    }

    /**
     * 获取本人所有账户列表（包含已删除） 用于实例详情回显使用
     * @CtrlAnnotation(log_type='get', throttle=1000)
     * @param Input $input
     * @return array
     * @throws Exception
     */
    public function getSelfAllAccountList(Input $input)
    {
        $input->verify([
            'staff_number'
        ]);

        $this->setUserSession($input);

        $list = (new UserModel())->getAllUserListByStaffNumber($input['staff_number'], 1, $input['search_all'] ?? false);

        return [
            'code' => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data' => $list
        ];
    }
}
