<?php

namespace App\Controller;

use App\Constant\ResponseCode;
use App\Logic\WechatDataLogic;
use App\Model\HttpModel\DiDi\ApprovalModel;
use App\Model\HttpModel\Feishu\AbstractFeishuModel;
use App\Model\HttpModel\Feishu\AuthModel;
use App\Model\HttpModel\Feishu\IM\MessageModel;
use App\Model\HttpModel\Volcengine\Knowledge\PointModel;
use App\Model\SqlModel\Zeda\ExportFileTaskModel;
use App\Model\SqlModel\Zeda\FeiShuDocMessagePointIDRelationModel;
use App\Model\SqlModel\Zeda\FeiShuMessageModel;
use App\Model\SqlModel\Zeda\FeishuMessageSummary;
use App\Param\FeishuStreamCardParam;
use App\Param\Knowledge\KnowledgeParam;
use App\Service\DataBot\DataBotPy;
use App\Service\DataBot\DataBotService;
use App\Service\DataBot\DataBotSession;
use App\Service\EnterpriseDiDiService;
use App\Service\FeiShuService;
use App\Service\GroupAssistant\GroupAssistantService;
use App\Service\GroupAssistant\KnowledgeService;
use App\Service\GroupAssistant\MessageFormat;
use App\Service\GroupAssistant\MessageSummary;
use App\Struct\RedisCache;
use App\Task\ExportFileTask;
use App\Utils\Helpers;
use Exception;
use Illuminate\Support\Collection;

class TestController extends Controller
{
    protected $pass_method = [
        'exportFileTask',
        'dataBot',
        'knowledge',
        'handlerText',
        'test',
        'didi',
        'addMessage'
    ];

    public function didi()
    {
//        $res = (new ApprovalModel())->getOrder('广州中旭未来科技有限公司', '1125973836630895');

        $res = (new EnterpriseDiDiService())->isApplicationUsed('广州中旭未来科技有限公司', '1125973836630895');

        dd($res);
        $service = new EnterpriseDiDiService();

        $data = '{"uuid":"73c5c8d4ead1d6d84f8b06e4b7656565","event":{"app_id":"cli_a75258559bf8900b","approval_code":"E83AE510-3A47-4126-B7B0-9C0ACE8DB07D","end_time":1750846659,"i18n_resources":[{"is_default":true,"locale":"zh_cn","texts":{"@i18n@48C02BDA8D205CACE9521A4FE292BE34":"公事外出"}}],"instance_code":"DA03CD15-AC59-4B47-98A2-7C94A9027C29","open_id":"ou_3e2622758081e884c302a06a03d39c6f","out_end_time":"2025-06-26 18:30:00","out_image":"","out_interval":21600,"out_name":"@i18n@48C02BDA8D205CACE9521A4FE292BE34","out_reason":"外出参加网络游戏出版分享会","out_start_time":"2025-06-26 12:30:00","out_unit":"HOUR","start_time":1750843309,"tenant_key":"105189295a17175f","type":"out_approval","user_id":"TW7606"},"token":"H2yx8pt59jPl50sJpuVdNhQlgDazfMJ8","ts":"1750846660.054733","type":"event_callback"}';

        $decrypt = json_decode($data, true);
        $service->outApproval($decrypt);
        dd(1);
        //        // 清除缓存
//        $date = '2025-06-23';
//        $employee_no_list = [
//            'TW6513',
//            'TW4573',
//            'TW7102',
//        ];
//        foreach ($employee_no_list as $employee_no) {
//            $service->cleanCache($employee_no, $date);
//        }
////
//        dd(1);

        // 模拟用户打卡
        $data = '{"schema":"2.0","header":{"event_id":"a83c0ddb4cce8c9757fba0318df00d2c","token":"H2yx8pt59jPl50sJpuVdNhQlgDazfMJ8","create_time":"1750688537141","event_type":"attendance.user_flow.created_v1","tenant_key":"105189295a17175f","app_id":"cli_a75258559bf8900b"},"event":{"bssid":"14:84:77:84:31:62","check_time":"1750688533","comment":"","employee_id":"TW7102","employee_no":"TW7102","is_field":false,"is_wifi":true,"latitude":0,"location_name":"Tanwan(14:84:77:84:31:62)","longitude":0,"photo_urls":[],"record_id":"7519149999227912194","risk_result":0,"ssid":"Tanwan","type":0}}';


        $decrypt = json_decode($data, true);
        $service->userClockOut($decrypt);
        dd(1);


        // 模拟用户审批通过
        $data = '{"uuid":"00eed76c4d5ec58b5251bf6a1c8353f0","event":{"app_id":"cli_a75258559bf8900b","approval_code":"744AD97C-BF36-414C-A8FF-06804111B5FF","instance_code":"E6140BB1-1062-4ACD-A31F-F31F8B9AD2F1","instance_operate_time":"1750688511343","operate_time":"1750688511343","status":"APPROVED","tenant_key":"105189295a17175f","type":"approval_instance","uuid":"c25bfb5b"},"token":"H2yx8pt59jPl50sJpuVdNhQlgDazfMJ8","ts":"1750688512.992143","type":"event_callback"}';
        $decrypt = json_decode($data, true);

        $service->clockOutApproval($decrypt);
        return [
            'code' => ResponseCode::SUCCESS,
        ];
    }


    /**
     * 测试
     *
     * @return array
     */
    public function test()
    {
        $service = new  EnterpriseDiDiService();

        $dd_user_info = $service->getUserInfoByDidi('TW0985', 2);
        $logic = new WechatDataLogic();
        $start_time = 1752768000 - 86400;
        $user_id = 151;
        $theme = '测试推送';
        $list = $logic->getDataSumPush($start_time, $user_id, $theme);
        return [
            'list'         => $list,
            'dd_user_info' => $dd_user_info,
        ];
    }

    public function exportFileTask()
    {
        $row_data = (new ExportFileTaskModel())->getUnFinishOne();
        $data = (new ExportFileTask())->getSettlementSummarizeList($row_data);

        return [
            'data' => $data,
        ];
    }

    /**
     * 知识问答测试
     *
     * @return array
     * @throws Exception
     */
    public function knowledge()
    {
        //        // 获取消息内容，重新总结
//        $message_list = (new FeiShuMessageModel())->getListByChatIdAndLastTime('oc_7cbabbc3c5ba6f6d2e1c4d2bd35476b9','2025-07-09 00:00:00');
//
//        $message_list = GroupAssistantService::formatToLLMMessageList($message_list);
//        $message_summary = new MessageSummary();
//
//        $summary = $message_summary->generateSummary($message_list, '');
//
//        dd($summary);


        $query = '之前好像谁总结过一份休闲游戏的爆款逻辑，里面内容是啥？我忘了？';
        $union_id = 'on_b8ef743bfe285ce29e8079b882739aa3'; // dai
        $union_id = 'on_ee55ca12cb03c11fc39c54e4aa705965'; // 仇
        $chat_id = '';

        // 清空session
        $session_key = GroupAssistantService::INTENT_SESSION_KEY . $union_id;
        $redis = RedisCache::getInstance();
        $redis->del($session_key);
        $session_key = KnowledgeService::QA_SESSION_KEY . $union_id;
        $redis->del($session_key);


        //        // 意图识别
//        $result = (new GroupAssistantService())->intent($query, $chat_id, 'p2p', $union_id);
        ////
//        dd($result);
        $param = [
            'query'      => $query,
            'union_id'   => $union_id,
            'chat_id'    => $chat_id,
            'type'       => 'p2p',
            'start_date' => '',
            'end_date'   => ''
        ];
        // 知识问答
        $param = new KnowledgeParam($param);

        // 初始化流式输出的卡片参数 这里固定用dhq的union_id接受消息
        $union_id = 'on_b8ef743bfe285ce29e8079b882739aa3';
        $message_param = new FeishuStreamCardParam([
            'receive_id'          => $union_id,
            'receive_id_type'     => 'union_id',
            'tenant_access_token' => (new AuthModel())->tenantAccessTokenByAppType(AbstractFeishuModel::INTERNAL, GroupAssistantService::APP_ID, GroupAssistantService::APP_SECRET),
            'content'             => '正在解析消息...',
        ]);

        // 发个消息让用户等待一下
        FeiShuService::stream($message_param);

        (new KnowledgeService())->getKnowledgeAnswer($param, $message_param);

        FeiShuService::updateStreamModel($message_param);

        return [
            'code' => ResponseCode::SUCCESS,
        ];
    }


    public function dataBot()
    {
        $data_bot_session = new DataBotSession(151, 1);
        $data_bot_service = new DataBotService('戴焕其', 151, Helpers::getLogger('wechat_data_bot'));
        $data_bot_session->cleanSession();

        $user_id = 151;
        //        $user_query = '再加一个注册数指标';
//        $user_query = '前十的呢？';
        $user_query = '查询野兽领主最近一个月的总付费金额。并且按每日的流水趋势画一个折线图';


        $output = $data_bot_service->run($user_query, 1);

        // 是否是数据分析
        $analyse = $output['analyse'] ?? false;

        if ($analyse) {
            // 多维度数据先不考虑，直接拿一条先 TODO
            $table_list = $output['draw_data'][0] ?? [];
            $analyse_respond = DataBotPy::analyse($user_query, 1, $table_list, $user_id, Helpers::getLogger('wechat_data_bot'));

            $output['analyse_respond'] = $analyse_respond;

        } else {
            $draw_data = $output['draw_data'];
            // 循环，返回多张图
            foreach ($draw_data as $data_item) {
                // 生成图片
                $png_filename = DataBotPy::plotting(1, $data_item, $user_id, Helpers::getLogger('wechat_data_bot'));
            }
        }


        $route_name = RedisCache::getInstance()->get($data_bot_session->genRouterNameSessionKey(1, 151));
        $output['session_list'] = $data_bot_session->getSessionList($data_bot_session->genDataBotSessionKey($route_name, 1, 151));
        $output['dispatch_router'] = $data_bot_session->getSessionList($data_bot_session->genDispatchRouteSessionKey(1, 151));
        $output['all_session_list'] = DataBotSession::getAllConversationHistory(1, $user_id);

        return [
            'code'    => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data'    => $output
        ];
    }


    public function handlerText()
    {
        $message_id = 'om_x100b4a9397f764240f3fbb96378cd51';

        $file_key = 'file_v3_00ni_29f35640-5433-4ed5-87f9-9e4a1f6a64bg';
        $filename = '202505-江西贪玩-任德权-神器-云账户-cps（抖音、快手）1.xlsx';
        $cut_off = 0;
        MessageFormat::handlerFile($message_id, $file_key, $filename, $cut_off);

        dd(1);
        // 示例用法
        $cut_off = 0;
        $inputText = "https://lx3qcyzne8.feishu.cn/wiki/S9sJwhUiIiTb65kmPNJctldtnde?sheet=50pXYr在线表的都看看啊";
        $outputText = MessageFormat::handlerText($inputText, 'om_46d1c07ea1d2c5c39ee5d149e196b6b0', $cut_off, false);
        return [
            'code'    => ResponseCode::SUCCESS,
            'message' => '获取成功',
            'data'    => $outputText
        ];
    }

    public function addMessage()
    {
        $logger = Helpers::getLogger('add_message');

        // 需要添加的群id
        $chat_id = 'oc_9973fb43df065cfd818b845ba3b03c60';
        // 先获取access_token
        $tenant_access_token = (new AuthModel())->tenantAccessTokenByAppType(AbstractFeishuModel::INTERNAL,
            GroupAssistantService::APP_ID, GroupAssistantService::APP_SECRET);
        // 先去获取聊天记录
        $start_time = strtotime(date("Y-m-d H:i:s", strtotime("2025-08-08")));
        $end_time = strtotime(date("Y-m-d H:i:s", strtotime("2025-08-09")));
        $message_list = (new MessageModel())->getMessageList($tenant_access_token, 'chat', $chat_id, $start_time, $end_time, 50, 'ByCreateTimeAsc');


        $logger->info('获取到了消息历史，开始解析聊天记录', ['chat_id' => $chat_id, 'message_list' => $message_list]);
        $message_model = new FeiShuMessageModel();

        $service = new GroupAssistantService();
        // 解析聊天记录
        foreach ($message_list as $message) {

            // 这里还可以指定消息id
            if ($message['message_id'] !== 'om_x100b466d2a55ec2c0f49f5de06908a5') {
                continue;
            }


            // 先获取access_token 防止长时间循环token过期
            $tenant_access_token = (new AuthModel())->tenantAccessTokenByAppType(AbstractFeishuModel::INTERNAL,
                GroupAssistantService::APP_ID, GroupAssistantService::APP_SECRET);

            $format_message = $service->formatmessage($message, $tenant_access_token, $chat_id);
            // 过滤掉的消息不需要处理
            if (!$format_message) {
                $logger->info('过滤消息', ['chat_id' => $chat_id]);
                continue;
            }

            // 列表是找不到union_id的，得去转换一下
            $open_id = $message['sender']['id'];
            $union_id = FeiShuService::getUnionIdByOpenId($open_id);
            // 找不到，则去飞书接口找一下,通过消息详情去曲线获取
            if (!$union_id) {
                $message_detail = (new MessageModel())->getMessageDetail($message['message_id'], $tenant_access_token, 'union_id');
                $union_id = $message_detail['data']['items'][0]['sender']['id'];
            }
            $insert_data = [
                'message_id'          => $message['message_id'],
                'chat_id'             => $chat_id,
                'decrypt'             => json_encode($message),
                'format_message'      => json_encode($format_message),
                'msg_type'            => $message['msg_type'],
                'message_create_time' => intval($message['create_time'] / 1000),
                'parent_id'           => $message['parent_id'] ?? '',
                'union_id'            => $union_id,
                'cut_off'             => $format_message['cut_off'],
                'at'                  => $format_message['at'],
            ];
            $message_model->replaceone($insert_data);

            $logger->info('消息入库完成', ['chat_id' => $chat_id, 'message_id' => $message['message_id']]);
        }

        return [
            'code' => ResponseCode::SUCCESS,
        ];
    }


    /**
     * 更新切片
     * @return array
     */
    public function addSummary()
    {
        $logger = Helpers::getLogger('add_summary');

        dd(1);
        (new MessageSummary())->addSummary();

        return [
            'code' => ResponseCode::SUCCESS,
        ];

    }

}