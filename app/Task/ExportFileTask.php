<?php


namespace App\Task;


use App\Constant\BillTemplate;
use App\Constant\MediaType;
use App\Constant\RouteID;
use App\Container;
use App\Exception\AppException;
use App\Exception\ExportFileException;
use App\Logic\DMS\AdInspireCostLogic;
use App\Logic\DMS\AgentRebateLogic;
use App\Logic\DMS\CostInputLogic;
use App\Logic\DMS\CostShareLogic;
use App\Logic\DMS\DataAnalysis\OverviewLogic;
use App\Logic\DMS\DataAnalysis\PaymentLogic;
use App\Logic\DMS\FinanceAccountLogic;
use App\Logic\DMS\MarketLogic;
use App\Logic\DMS\OperationProfitLogic;
use App\Logic\DMS\OuterLogic;
use App\Logic\DMS\PayEstimateLogic;
use App\Logic\DMS\PermissionLogic;
use App\Logic\DMS\ProfitStatementLogic;
use App\Logic\DMS\RetainLogic;
use App\Logic\DMS\SdkStepLogic;
use App\Logic\DMS\TeamConfigLogic;
use App\Logic\DMS\TeamDataGrowthFactorLogic;
use App\Logic\DMS\UserProfileLogic;
use App\Logic\DSP\ADLiveCostReportLogic;
use App\Logic\DSP\FinanceLogic;
use App\Logic\FA\AttendanceRecordLogic;
use App\Logic\FA\CostLogic;
use App\Logic\FA\SalaryLogic;
use App\Logic\FA\VisualizationLogic;
use App\Logic\FA\WorkflowLogic;
use App\Logic\LogEsLogic;
use App\Logic\LogLogic;
use App\Logic\LY\DataHubLogic;
use App\Logic\LY\MarketLogic as LYMarketLogic;
use App\Model\SqlModel\Tanwan\DataAnalysis\DataAnalysisModel;
use App\Model\SqlModel\Tanwan\V2DWDDayCostLogModel;
use App\Model\SqlModel\Zeda\AgentModel;
use App\Model\SqlModel\Zeda\AlertPayDataModel;
use App\Model\SqlModel\Zeda\ExportFeishuFileTaskModel;
use App\Model\SqlModel\Zeda\ExportFileTaskModel;
use App\Model\SqlModel\Zeda\FAAttendanceAnnualLeaveModel;
use App\Model\SqlModel\Zeda\FAWorkflowProjectModel;
use App\Model\SqlModel\Zeda\FAWorkflowWorkHourModel;
use App\Model\SqlModel\Zeda\SiteSettlementModel;
use App\Model\SqlModel\Zeda\UserModel;
use App\Param\ADLiveCostReportFilterParam;
use App\Param\DimProfitListParam;
use App\Param\DMS\ActionRetainListFilterParam;
use App\Param\DMS\AdInspireCostParam;
use App\Param\DMS\AgentRebateListParam;
use App\Param\DMS\AuditOverviewParam;
use App\Param\DMS\CostShareParam;
use App\Param\DMS\DataAnalysis\OverviewListFilterParam;
use App\Param\DMS\DataAnalysis\PayEstimateParam;
use App\Param\DMS\DataAnalysis\PaymentListFilterParam;
use App\Param\DMS\DataAnalysis\PaymentMonthListParam;
use App\Param\DMS\DataAnalysis\RetainListFilterParam;
use App\Param\DMS\DataAnalysis\TeamConfigDataParam;
use App\Param\DMS\DataAnalysis\UserProfileListFilterParam;
use App\Param\DMS\FinanceAccountParam;
use App\Param\DMS\FlowEstimateParam;
use App\Param\DMS\InternalUserListParam;
use App\Param\DMS\OrderConversionListFilterParam;
use App\Param\DMS\OrderCountListFilterParam;
use App\Param\DMS\OuterOverViewListFilterParam;
use App\Param\DMS\ProfitStatementListParam;
use App\Param\DMS\RechargeRankParam;
use App\Param\DMS\RoleCreateLogParam;
use App\Param\DMS\SdkStepListParam;
use App\Param\DMS\ServerDataOverviewListFilterParam;
use App\Param\DMS\SiteSettlementListParam;
use App\Param\DMS\WastageListFilterParam;
use App\Param\DMS\WebsiteRechargeFilterParam;
use App\Param\DMSCostInputLogParam;
use App\Param\Erp\CombinationListParam;
use App\Param\Erp\ConsumablesListParam;
use App\Param\Erp\CustomerListParam;
use App\Param\Erp\ProductListParam;
use App\Param\Erp\StorageListParam;
use App\Param\Erp\StorehouseListParam;
use App\Param\Erp\VendorListParam;
use App\Param\Erp\WarehouseListParam;
use App\Param\FA\AttendancePersonalRecordParam;
use App\Param\FinanceListParam;
use App\Param\FinanceMailListParam;
use App\Param\FinanceStandingBookListParam;
use App\Param\FundBalanceLogParam;
use App\Param\FundScreenshotMissionAccountListParam;
use App\Param\LogListParam;
use App\Param\LogSearchParam;
use App\Param\LY\AccountBindListFilterParam;
use App\Param\LY\RechargeRankParam as LYRechargeRankParam;
use App\Param\LYProfitListParam;
use App\Param\MasterCostInputLogParam;
use App\Service\DataBot\DataBotSession;
use App\Service\PermissionService;
use App\Service\UserService;
use App\Struct\RedisCache;
use App\Utils\DimensionTool;
use App\Utils\ExportHelper;
use App\Utils\Helpers;
use App\Utils\Math;
use Common\EnvConfig;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;

class ExportFileTask
{

    const TICK_LIST = [
    ];


    /**
     * @var ExportFileTaskModel
     */
    protected $export_file_model;


    public function __construct()
    {
        $this->export_file_model = new ExportFileTaskModel();
    }

    /**
     * 导出的日期字段
     */
    const DATE_COLUMN = ['tdate_1', 'game_reg_date', 'date', 'pay_date', 'tdate', 'game_date', 'action_date', 'sdk_action_date'];

    /**
     * 导出任务
     */
    public function export()
    {
        // 从数据库里查出单个还未执行的导出任务。然后导出
        $row_data = $this->export_file_model->getUnFinishOne();
        if (!$row_data) {
            return;
        }

        // 获得锁才往下执行
        if (!$this->lock($row_data->id)) return;

        Helpers::getLogger('sync-export')->info('任务开始执行', ['任务详情' => $this->getTaskDetail($row_data)]);

        // 先更新锁状态
        Helpers::getLogger('sync-export')->info('更新锁状态');
        $lock_res = $this->export_file_model->taskLock($row_data->id);

        // 这里就可以解锁redis了，因为上面更新了数据库的锁状态
        $this->unlock($row_data->id);
        if (!$lock_res) {
            Helpers::getLogger('sync-export')->info('数据库更新锁失败，不执行任务');
            return;
        }
        // 拼接文件绝对路径
        $abs_path = SRV_DIR . '/' . $row_data->source_filename;

        $limit = 200000;
        $is_accurate = 1;
        try {
            switch ($row_data->route_id) {
                case RouteID::MARKET_OVERVIEW:
                    [$header_format, $content, $limit, $is_accurate] = $this->overview($row_data);
                    break;
                case RouteID::MARKET_HOUR_OVERVIEW:
                case RouteID::OPERATION_HOUR_OVERVIEW:
                    [$header_format, $content, $limit, $is_accurate] = $this->hourOverview($row_data);
                    break;
                case RouteID::MARKET_SIMPLER_HOUR_OVERVIEW:
                case RouteID::OPERATION_SIMPLER_HOUR_OVERVIEW:
                    [$header_format, $content, $limit, $is_accurate] = $this->simpleHourOverview($row_data);
                    break;
                case RouteID::MARKET_RETAIN:
                case RouteID::OPERATION_RETAIN:
                    [$header_format, $content, $limit, $is_accurate] = $this->retain($row_data);
                    break;
                case RouteID::MARKET_ACTION_RETAIN:
                case RouteID::OPERATION_ACTION_RETAIN:
                    [$header_format, $content, $limit, $is_accurate] = $this->actionRetain($row_data);
                    break;
                case RouteID::MARKET_PAYMENT:
                case RouteID::OPERATION_PAYMENT:
                    [$header_format, $content, $limit, $is_accurate] = $this->payment($row_data);
                    break;
                case RouteID::OPERATION_OVERVIEW:
                    [$header_format, $content, $limit, $is_accurate] = $this->operationOverview($row_data);
                    break;
                case RouteID::RECHARGE_RANK:
                    [$header_format, $content, $limit, $is_accurate] = $this->operationRechargeRank($row_data);
                    break;
                case RouteID::ADMIN_LOG:
                    [$header_format, $content, $limit, $is_accurate] = $this->getLogPercentDetail($row_data);
                    break;
                case RouteID::WASTAGE:
                    [$header_format, $content, $limit, $is_accurate] = $this->wastage($row_data);
                    break;
                case RouteID::LY_WASTAGE:
                    [$header_format, $content, $limit, $is_accurate] = $this->lyWastage($row_data);
                    break;
                case RouteID::LY_MARKET_RETAIN:
                    [$header_format, $content, $limit, $is_accurate] = $this->lyRetain($row_data);
                    break;
                case RouteID::LY_MARKET_PAYMENT:
                    [$header_format, $content, $limit, $is_accurate] = $this->lyPayment($row_data);
                    break;
                case RouteID::LY_HOUR_OVERVIEW:
                    [$header_format, $content, $limit, $is_accurate] = $this->lySimpleHourOverview($row_data);
                    break;
                case RouteID::LY_MARKET_OVERVIEW:
                    [$header_format, $content, $limit, $is_accurate] = $this->lyOverview($row_data);
                    break;
                case RouteID::OPERATION_PROFIT:
                    [$header_format, $content, $limit, $is_accurate] = $this->operationProfit($row_data);
                    break;
                case RouteID::LY_CHANNEL_PROFIT:
                    [$header_format, $content, $limit, $is_accurate] = $this->lyChannelProfit($row_data);
                    break;
                case RouteID::LY_RECHARGE_RANK:
                    [$header_format, $content, $limit, $is_accurate] = $this->lyRechargeRank($row_data);
                    break;
                case RouteID::PAY_ESTIMATE:
                    [$header_format, $content, $limit, $is_accurate] = $this->payEstimate($row_data);
                    break;
                case RouteID::LY_ACCOUNT_BIND:
                    [$header_format, $content, $limit, $is_accurate] = $this->lyAccountBind($row_data);
                    break;
                case RouteID::ACCOUNT_BIND:
                    [$header_format, $content, $limit, $is_accurate] = $this->accountBind($row_data);
                    break;
                case RouteID::ALERT_PAY_DATA:
                    [$header_format, $content, $limit, $is_accurate] = $this->alertPayData($row_data);
                    break;
                case RouteID::SERVER_DATA_OVERVIEW:
                    [$header_format, $content, $limit, $is_accurate] = $this->serverDataOverview($row_data);
                    break;
                case RouteID::COST_INPUT:
                    [$header_format, $content, $limit, $is_accurate] = $this->costInput($row_data);
                    break;
                case RouteID::OUTER_OVERVIEW:
                    [$header_format, $content, $limit, $is_accurate] = $this->outOverview($row_data);
                    break;
                case RouteID::FLOW_ESTIMATE:
                    [$header_format, $content, $limit, $is_accurate] = $this->flowEstimateList($row_data);
                    break;
                case RouteID::AUDIT_OVERVIEW:
                    [$header_format, $content, $limit, $is_accurate] = $this->auditOverview($row_data);
                    break;
                case RouteID::INTERNAL_USER:
                    [$header_format, $content, $limit, $is_accurate] = $this->internalUser($row_data);
                    break;
                case RouteID::DSP_FINANCE_FUND:
                    [$header_format, $content, $limit, $is_accurate] = $this->financeFund($row_data);
                    break;
                case RouteID::DSP_FINANCE_REPORT:
                    [$header_format, $content, $limit, $is_accurate] = $this->financeReport($row_data);
                    break;
                case RouteID::DSP_FINANCE_MAIL:
                    [$header_format, $content, $limit, $is_accurate] = $this->financeMail($row_data);
                    break;
                case RouteID::DSP_FINANCE_ACCOUNT_CENTER:
                    [$header_format, $content, $limit, $is_accurate] = $this->financeAccountCenter($row_data);
                    break;
                case RouteID::FA_BASIC_SALARY:
                    [$header_format, $content, $limit, $is_accurate] = $this->basicSalary($row_data);
                    break;
                case RouteID::FA_BASIC_COST:
                    [$header_format, $content, $limit, $is_accurate] = $this->basicCost($row_data);
                    break;
                case RouteID::FA_SHARE_SALARY:
                    [$header_format, $content, $limit, $is_accurate] = $this->shareSalary($row_data);
                    break;
                case RouteID::FA_SHARE_COST:
                    [$header_format, $content, $limit, $is_accurate] = $this->shareCost($row_data);
                    break;
                case RouteID::FA_PROFIT:
                    [$header_format, $content, $limit, $is_accurate] = $this->visualizationProfit($row_data);
                    break;
                case RouteID::FINANCE_ACCOUNT:
                    [$header_format, $content, $limit, $is_accurate] = $this->financeAccountList($row_data);
                    break;
                case RouteID::FRONT_END_CONVERSION:
                    [$header_format, $content, $limit, $is_accurate] = $this->sdkStepList($row_data);
                    break;
                case RouteID::LY_FRONT_END_CONVERSION:
                    [$header_format, $content, $limit, $is_accurate] = $this->lySDKStepList($row_data);
                    break;
                case RouteID::FA_ATTENDANCE_RECORD_LIST:
                    [$header_format, $content, $limit, $is_accurate] = $this->attendanceRecord($row_data);
                    break;
                case RouteID::FA_ATTENDANCE_RECORD_DETAIL:
                    [$header_format, $content, $limit, $is_accurate] = $this->attendanceRecordDetail($row_data);
                    break;
                case RouteID::SERVER_ECOLOGY:
                    [$header_format, $content, $limit, $is_accurate] = $this->layeredPayment($row_data);
                    break;
                case RouteID::MARKET_RECHARGE_RANK:
                    [$header_format, $content, $limit, $is_accurate] = $this->marketRechargeRank($row_data);
                    break;
                case RouteID::DSP_FINANCE_SCREENSHOT_MISSION:
                    [$header_format, $content] = $this->financeScreenshotMission($row_data);
                    break;
                case RouteID::OPERATION_WEBSITE_RECHARGE:
                    [$header_format, $content, $limit, $is_accurate] = $this->websiteRecharge($row_data);
                    break;
                case RouteID::ORDER_CONVERSION:
                    [$header_format, $content, $limit, $is_accurate] = $this->orderList($row_data);
                    break;
                case RouteID::DSP_FINANCE_TRANSACTION_CHECK:
                    [$header_format, $content, $limit, $is_accurate] = $this->transactionCheck($row_data);
                    break;
                case RouteID::DSP_FINANCE_RETURN_GOODS_RECORD:
                    [$header_format, $content, $limit, $is_accurate] = $this->returnGoodsRecord($row_data);
                    break;
                case RouteID::TEAM_CONFIG_DATA:
                    // 团队数据
                    [$header_format, $content, $limit, $is_accurate] = $this->teamConfigDataList($row_data);
                    break;
                case RouteID::PAYMENT_MONTH:
                    [$header_format, $content, $limit, $is_accurate] = $this->paymentMonth($row_data);
                    break;
                case RouteID::FA_WORKFLOW_WORK_HOUR:
                    [$header_format, $content, $limit, $is_accurate] = $this->workflowWorkHour($row_data);
                    break;
                case RouteID::ERP_FINANCE_STOCK:
                    [$header_format, $content, $limit, $is_accurate] = $this->erpStock($row_data);
                    break;
                case RouteID::ERP_FINANCE_PRODUCT:
                    [$header_format, $content, $limit, $is_accurate] = $this->erpProduct($row_data);
                    break;
                case RouteID::ERP_FINANCE_STOCK_ADDRESS:
                    [$header_format, $content, $limit, $is_accurate] = $this->erpStockAddress($row_data);
                    break;
                case RouteID::ERP_FINANCE_CUSTOMER:
                    [$header_format, $content, $limit, $is_accurate] = $this->erpCustomer($row_data);
                    break;
                case RouteID::ERP_FINANCE_SUPPLIER:
                    [$header_format, $content, $limit, $is_accurate] = $this->erpSupplier($row_data);
                    break;
                case RouteID::ERP_FINANCE_COMBINATION:
                    [$header_format, $content, $limit, $is_accurate] = $this->erpCombination($row_data);
                    break;
                case RouteID::ERP_FINANCE_CONSUMABLES:
                    [$header_format, $content, $limit, $is_accurate] = $this->erpConsumables($row_data);
                    break;
                case RouteID::PAY_ESTIMATE_MONTH:
                    [$header_format, $content, $limit, $is_accurate] = $this->payEstimateMonth($row_data);
                    break;
                case RouteID::FA_WORKFLOW_PROJECT:
                    [$header_format, $content, $limit, $is_accurate] = $this->workflowProject($row_data);
                    break;
                case RouteID::FA_ATTENDANCE_ANNUAL_LEAVE:
                    [$header_format, $content, $limit, $is_accurate] = $this->attendanceAnnualLeave($row_data);
                    break;
                case RouteID::COST_SHARE:
                    [$header_format, $content] = $this->costShare($row_data);
                    break;
                case RouteID::AGENT_REBATE:
                    [$header_format, $content] = $this->agentRebate($row_data);
                    break;
                case RouteID::AD_INSPIRE_COST:
                    [$header_format, $content] = $this->adInspireCost($row_data);
                    break;
                case RouteID::OUTER_UN_SETTLEMENT:
                    [$header_format, $content] = $this->getSettlementList($row_data);
                    break;
                case RouteID::OUTER_SETTLED:
                    [$header_format, $content] = $this->getSettlementSummarizeList($row_data);
                    break;
                case RouteID::ADMIN_ES_LOG:
                    [$header_format, $content, $limit] = $this->getEsOperationLog($row_data);
                    break;
                case RouteID::PROFIT_STATEMENT:
                    [$header_format, $content, $limit] = $this->profitStatement($row_data);
                    break;
                case RouteID::USER_PROFIT:
                    [$header_format, $content, $limit] = $this->getUserProfit($row_data);
                    break;
                case RouteID::DSP_AD_LIVE_COST_REPORT:
                    [$header_format, $content, $limit, $is_accurate] = $this->liveCostReport($row_data);
                    break;
                default:
                    Helpers::getLogger('sync-export')->info('错误的route类型', ['任务详情' => $this->getTaskDetail($row_data)]);
                    $this->export_file_model->taskFinish($row_data->id);
                    return;
            }

            if (pathinfo($abs_path, PATHINFO_EXTENSION) === 'zip') {
                $file_size = '';
                if (file_exists($abs_path)) {
                    $file_size = Helpers::toSize(filesize($abs_path));
                }
                $this->export_file_model->taskFinish($row_data->id, $file_size);
                Helpers::getLogger('sync-export')->info('文件写入成功', ['任务详情' => $this->getTaskDetail($row_data)]);
            } else {
                $rows = substr_count($content, PHP_EOL);
                $rows = max($rows, 0); // 最低0
                // 写入一个bom头
                file_put_contents($abs_path, "\xEF\xBB\xBF", LOCK_EX);

                // CSV 头部
                $data = implode(',', $header_format) . PHP_EOL;
//                $header_str = mb_convert_encoding($data, 'GB18030', 'utf-8');
                $header_write_res = file_put_contents($abs_path, $data, FILE_APPEND | LOCK_EX);

                // 内容 转码
//                $content_str = mb_convert_encoding($content, 'GB18030', 'utf-8');
                $content_write_res = file_put_contents($abs_path, $content, FILE_APPEND | LOCK_EX);
                if ($header_write_res !== false && $content_write_res !== false) {
                    $file_size = Helpers::toSize(filesize($abs_path));
                    $this->export_file_model->taskFinish($row_data->id, $file_size, $limit, $rows, $is_accurate);
                    // 飞书云文档灰测 TODO
                    if (in_array($row_data->user_id, EnvConfig::PRINT_SQL_USER_ID)) {
                        (new ExportFeishuFileTaskModel())->add($row_data->user_id, $row_data->id);
                    }
                    // 灰度，多写一份文件。用于数据分析，用完会删除。并且不转码
                    if (in_array($row_data->user_id, EnvConfig::PRINT_SQL_USER_ID)) {
                        $session_filename = SRV_DIR . '/data_bot/' . uniqid() . "_" . $row_data->user_id . '.csv';
                        $header_str = implode(',', $header_format) . PHP_EOL;
                        file_put_contents($session_filename, $header_str, FILE_APPEND | LOCK_EX);
                        file_put_contents($session_filename, $content, FILE_APPEND | LOCK_EX);
                        Helpers::getLogger('feishu_data_bot')->info('文件写入成功', ['header_str' => $header_str]);
                        DataBotSession::addSessionFile(1, $row_data->user_id, $session_filename, $header_str);
                    }

                    Helpers::getLogger('sync-export')->info('文件写入成功', ['任务详情' => $this->getTaskDetail($row_data)]);
                } else {
                    $this->export_file_model->unTaskLock($row_data->id);
                    Helpers::getLogger('sync-export')->error('文件写入失败', ['任务详情' => $this->getTaskDetail($row_data)]);
                }
            }

        } catch (ExportFileException $exception) {
            // 排队异常，继续等待 解锁数据库
            Helpers::getLogger('sync-export')->info('排队等待');
            $this->export_file_model->unTaskLock($row_data->id);
        } catch (\Throwable $exception) {
            $retry = false;
            // 这里判断一下是否需要重试
            $error_message = $exception->getMessage();
            $substrings = [
                'Update failed for dictionary tanwan_datahub.dim_site_id_dict',
                'DB::Exception: Dictionary tanwan_datahub.dim_site_id_dict',
                'DB::Exception: Too many simultaneous queries',
                'DB::Exception: Memory limit',
                'Out of Memory Pool size pre cal',
                'The cluster is out of memory',
                'Query exceeded maximum time limit of',
            ];

            foreach ($substrings as $substring) {
                if (strpos($error_message, $substring) !== false) {
                    $retry = true;
                    break;
                }
            }
            if ($retry) {
                // 重试。重新进队列
                Helpers::getLogger('sync-export')->info('重新进队列，并且延迟 5 分钟');
                $this->export_file_model->unTaskLock($row_data->id, 300);
            } else {
                Helpers::getLogger('sync-export')->error('任务出错',
                    [
                        'error_message' => substr($exception->getMessage(), 0, 255),
                        '任务详情'      => $this->getTaskDetail($row_data)
                    ]);

                // 任务出错。更新一下状态
                $this->export_file_model->errorTask($row_data->id, substr($exception->getMessage(), 0, 255), $limit);
            }
            // 团队数据的专用锁
            if ($row_data->route_id == RouteID::TEAM_CONFIG_DATA) {
                $this->unTeamConfigLock();
            }
        }
    }

    /**
     * 处理投放总览（投放数据分析）
     *
     * @param $row_data
     *
     * @return array
     */
    private function overview($row_data)
    {
        $param = new OverviewListFilterParam(json_decode($row_data->param, true));
        $param->setUserPermission($row_data->user_id);
        if ($row_data->user_id == 155 || $row_data->user_id == 261 || $row_data->user_id == 2935) {
            $param->limit = 8000000;
        } else {
            $param->limit = 200000;
        }
        $param->handleTarget(1); // 处理一下指标
        // $param->database_type = 'mysql';

        $service = new OverviewLogic();

        // 去掉自动添加的标准值
        if (in_array('max_first_day_pay_money', $param->export_target)) {
            $key = array_search('max_first_day_pay_money', $param->export_target);
            array_splice($param->export_target, $key, 1);
        }

        // 表头
        if ($param->aggregation_time !== '聚合') {
            $header = array_merge($param->dimension, ['date', 'cost_money'], $param->export_target);
        } else {
            $header = array_merge($param->dimension, ['cost_money'], $param->export_target);
        }

        $header_map = ExportHelper::exportOverviewHeadMap();
        $header_format = $this->getHeaderFormat($param, $header, $header_map, $row_data->merge_dimension);

        // 自定义指标
        /* @var Collection $customized_target */
        $customized_target = $param->customized_target;
        if ($param->customized_target) {
            $header = array_merge($header, $customized_target->pluck('name')->toArray());
            $header_format = array_merge($header_format, $customized_target->pluck('name')->toArray());
        }

        $data = $service->getOverviewList($param);
        // 去掉合计的维度先
        foreach ($param->dimension as $item) {
            if (in_array($item, DataAnalysisModel::ID_TO_NAME)) {
                $name_key = DimensionTool::idToNameKey($item);
                unset($data['sum'][$name_key]);
            }
            unset($data['sum'][$item]);
        }
        $this->unshift($data['list'], $data['sum']); // 合计塞上来

        $content = '';
        foreach ($data['list'] as $index => $item) {
            $item = (array)$item;
            $tmp = [];
            foreach ($header as $column) {
                if (isset($item[$column])) {
                    $tmp[] = ExportHelper::itemFormat($column, $item, $row_data->merge_dimension);
                } else {
                    $tmp[] = ''; // 没有的补个空格
                }
            }
            $content .= implode(',', $tmp) . PHP_EOL;
        }
        return [$header_format, $content, $param->limit, $data['is_accurate']];
    }

    /**
     *
     * 处理投放总览（运营数据分析）
     *
     * @param $row_data
     *
     * @return array
     */
    private function operationOverview($row_data)
    {
        $param = new OverviewListFilterParam(json_decode($row_data->param, true));
        $param->setUserPermission($row_data->user_id);
        $param->database_type = 'mysql';
        $service = new OverviewLogic();

        // 判断指标是否有区服，有的话走另一套逻辑
        if (in_array('ori_server_id', $param->dimension) || $param->dimension_filter['ori_server_id']) {
            $param->handleServerTarget();// 处理指标
            $other = ['tdate', 'open_time'];
            // 表头
            $header = array_merge($param->dimension, $other, $param->export_target);
            $data = $service->getServerOverviewList($param);
        } else {
            $param->handleTarget(2);// 处理指标
            $data = $service->getOperationOverviewList($param);
            // 表头
            $header = array_merge($param->dimension, ['date'], $param->export_target);
        }

        $param->limit = 200000;


        $header_map = ExportHelper::exportOperationOverviewHeadMap();
        $header_format = $this->getHeaderFormat($param, $header, $header_map, $row_data->merge_dimension);
        // 自定义指标
        /* @var Collection $customized_target */
        $customized_target = $param->customized_target;
        if ($param->customized_target) {
            $header = array_merge($header, $customized_target->pluck('name')->toArray());
            $header_format = array_merge($header_format, $customized_target->pluck('name')->toArray());
        }


        // 去掉合计的维度先
        foreach ($param->dimension as $item) {
            if (in_array($item, DataAnalysisModel::ID_TO_NAME)) {
                $name_key = DimensionTool::idToNameKey($item);
                unset($data['sum'][$name_key]);
            }
            unset($data['sum'][$item]);
        }
        $this->unshift($data['list'], $data['sum']); // 合计塞上来

        $content = '';
        foreach ($data['list'] as $index => $item) {
            $item = (array)$item;
            $tmp = [];
            foreach ($header as $column) {
                if (isset($item[$column])) {
                    $tmp[] = ExportHelper::itemFormat($column, $item, $row_data->merge_dimension);
                } else {
                    $tmp[] = ''; // 没有的补个空格
                }
            }
            $content .= implode(',', $tmp) . PHP_EOL;
        }
        return [$header_format, $content, $param->limit, $data['is_accurate']];
    }


    /**
     *
     * 发行总览
     *
     * @param $row_data
     *
     * @return array
     */
    private function lyOverview($row_data)
    {
        $param = new \App\Param\LY\OverviewListFilterParam(json_decode($row_data->param, true));
        $param->setUserPermission($row_data->user_id);
        $param->limit = 200000;
        $service = new LYMarketLogic();

        // 判断指标是否有区服，有的话走另一套逻辑
        if (in_array('ori_server_id', $param->dimension) || $param->dimension_filter['ori_server_id']) {
            $param->handleServerTarget();// 处理指标

            // 去掉ori_server_id的维度
            if (in_array('ori_server_id', $param->dimension)) {
                $key = array_search('ori_server_id', $param->dimension);
                array_splice($param->dimension, $key, 1);
            }
            $data = $service->getServerOverviewList($param);

            if ($param->time_type == 1) {
                $other = ['t_date', 'open_time'];
            } else {
                $other = ['open_time'];
            }
            // 表头
            $header = array_merge($param->dimension, $other, $param->target);
        } else {
            $param->handleTarget();// 处理指标
            $data = $service->getOperationOverviewList($param);
            // 表头
            $header = array_merge($param->dimension, ['date'], $param->target);
        }


        $header_map = ExportHelper::exportLYOverviewHeadMap();
        $header_format = $this->getHeaderFormat($param, $header, $header_map, $row_data->merge_dimension);


        // 去掉合计的维度先
        foreach ($param->dimension as $item) {
            if (in_array($item, DataAnalysisModel::ID_TO_NAME)) {
                $name_key = DimensionTool::idToNameKey($item);
                unset($data['sum'][$name_key]);
            }
            unset($data['sum'][$item]);
        }
        $this->unshift($data['list'], $data['sum']); // 合计塞上来

        $content = '';
        foreach ($data['list'] as $index => $item) {
            $item = (array)$item;
            $tmp = [];
            foreach ($header as $column) {
                if (isset($item[$column])) {
                    $tmp[] = ExportHelper::itemFormat($column, $item, $row_data->merge_dimension);
                } else {
                    $tmp[] = ''; // 没有的补个空格
                }
            }
            $content .= implode(',', $tmp) . PHP_EOL;
        }
        return [$header_format, $content, $param->limit, $data['is_accurate'] ?? 1];
    }

    /**
     * @param $row_data
     *
     * @return array
     */
    private function hourOverview($row_data)
    {
        $param = new OverviewListFilterParam(json_decode($row_data->param, true));
        $param->setUserPermission($row_data->user_id);
        $param->handleHourTarget(); // 处理分时指标
        $param->limit = 1000000;
        $param->database_type = 'mysql';

        $service = new MarketLogic();

        $hour_list = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23'];
        // 表头
        $header = array_merge($param->dimension, ['date', 'cost_money', 'date_hour'], $param->target, $hour_list);
        $header_map = ExportHelper::exportHourOverviewHeadMap();
        $header_format = $this->getHeaderFormat($param, $header, $header_map, $row_data->merge_dimension);

        // 自定义指标
        /* @var Collection $customized_target */
        $customized_target = $param->customized_target;
        if ($param->customized_target) {
            $header = array_merge($header, $customized_target->pluck('name')->toArray());
            $header_format = array_merge($header_format, $customized_target->pluck('name')->toArray());
        }

        $data = $service->getOverviewWithHourList($param, 0);
        // 去掉合计的维度先
        foreach ($param->dimension as $item) {
            if (in_array($item, DataAnalysisModel::ID_TO_NAME)) {
                $name_key = DimensionTool::idToNameKey($item);
                unset($data['sum'][$name_key]);
            }
            unset($data['sum'][$item]);
        }
        $this->unshift($data['list'], $data['sum']); // 合计塞上来

        $key_prefix = $param->switchColumnKey();
        $content = '';

        foreach ($data['list'] as $index => $item) {
            $item = (array)$item;
            $tmp = [];
            foreach ($header as $column) {
                if (isset($item[$column])) {
                    $tmp[] = ExportHelper::itemFormat($column, $item, $row_data->merge_dimension);
                } else {
                    // xx时
                    $column_key = $key_prefix . $column;
                    if (isset($item[$column_key])) {
                        $tmp[] = $item[$column_key];
                    } else {
                        $tmp[] = ''; // 不存在的补个空格
                    }
                }
            }
            $content .= implode(',', $tmp) . PHP_EOL;
        }
        return [$header_format, $content, $param->limit, $data['is_accurate'] ?? 1];
    }

    private function simpleHourOverview($row_data)
    {
        $param = new OverviewListFilterParam(json_decode($row_data->param, true));
        $param->setUserPermission($row_data->user_id);
        $param->limit = 1000000;
        $param->database_type = 'mysql';

        $service = new MarketLogic();

        $hour_list = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23'];

        // 前缀
        $key_prefix = $param->switchColumnKey();

        // 表头
        $header = array_merge($param->dimension, ['date', 'day_cost_money', 'reg_cost'], $param->target, [$key_prefix . 'sum'], $hour_list);
        $header_map = ExportHelper::exportSimpleOverviewHeadMap();
        $header_format = $this->getHeaderFormat($param, $header, $header_map, $row_data->merge_dimension);

        $data = $service->getSimpleOverviewWithHourList($param, 0, false);
        // 去掉合计的维度先
        foreach ($param->dimension as $item) {
            if (in_array($item, DataAnalysisModel::ID_TO_NAME)) {
                $name_key = DimensionTool::idToNameKey($item);
                unset($data['sum']['all'][$name_key]);
            }
            unset($data['sum']['all'][$item]);
        }
        array_unshift($data['list'], $data['sum']['all']); // 合计塞上来

        $content = '';

        foreach ($data['list'] as $index => $item) {
            $item = (array)$item;
            $tmp = [];
            foreach ($header as $column) {
                if (isset($item[$column])) {
                    $tmp[] = ExportHelper::itemFormat($column, $item, $row_data->merge_dimension);
                } else {
                    // xx时
                    $column_key = $key_prefix . $column;
                    if (isset($item[$column_key])) {
                        $tmp[] = $item[$column_key];
                    } else {
                        $tmp[] = 0; // 不存在的补个0
                    }
                }
            }
            $content .= implode(',', $tmp) . PHP_EOL;
        }

        return [$header_format, $content, $param->limit, $data['is_accurate'] ?? 1];
    }

    private function lySimpleHourOverview($row_data)
    {
        $param = new \App\Param\LY\OverviewListFilterParam(json_decode($row_data->param, true));
        $param->setUserPermission($row_data->user_id);
        $param->limit = 1000000;

        $service = new LYMarketLogic();

        $hour_list = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23'];

        // 前缀
        $key_prefix = $param->switchColumnKey();

        // 表头
        $header = array_merge($param->dimension, ['date'], $param->target, [$key_prefix . 'sum'], $hour_list);
        $header_map = ExportHelper::exportSimpleOverviewHeadMap();
        $header_format = $this->getHeaderFormat($param, $header, $header_map, $row_data->merge_dimension);

        $data = $service->getSimpleOverviewWithHourList($param, 0, false);
        $sum = [];
        foreach ($data['sum'] as $item) {
            if ($item['date'] === '总合计') {
                $sum = $item;
                break;
            }
        }
        // 去掉合计的维度先
        foreach ($param->dimension as $item) {
            if (in_array($item, DataAnalysisModel::ID_TO_NAME)) {
                $name_key = DimensionTool::idToNameKey($item);
                unset($sum[$name_key]);
            }
            unset($sum[$item]);
        }
        array_unshift($data['list'], $sum); // 合计塞上来

        $content = '';

        foreach ($data['list'] as $index => $item) {
            $item = (array)$item;
            $tmp = [];
            foreach ($header as $column) {
                if (isset($item[$column])) {
                    $tmp[] = ExportHelper::itemFormat($column, $item, $row_data->merge_dimension);
                } else {
                    // xx时
                    $column_key = $key_prefix . $column;
                    if (isset($item[$column_key])) {
                        $tmp[] = $item[$column_key];
                    } else {
                        $tmp[] = 0; // 不存在的补个0
                    }
                }
            }
            $content .= implode(',', $tmp) . PHP_EOL;
        }

        return [$header_format, $content, $param->limit, $data['is_accurate'] ?? 1];
    }

    private function retain($row_data)
    {
        $row_param = json_decode($row_data->param, true);
        $param = new RetainListFilterParam($row_param);
        $param->setUserPermission($row_data->user_id);

        $service = new MarketLogic();

        if ($param->export_type == 2) {
            $data = $service->getServerRetainList($param, 0);
        } else {
            $data = $service->getRetainList($param, 0);
        }
        // 游戏留存和注册留存字段名有点不一样
        $retain_count = $param->export_type == 2 ? 'role_create_count' : 'reg_uid_count';
        $retain_date = $param->export_type == 2 ? 'open_time' : 'game_reg_date';
        // 表头
        if ($param->aggregation_time !== '聚合' && $param->export_type != 2) {
            $other[] = 'game_reg_date';
        }
        if ((in_array($param->aggregation_retain, $param->pay_retain) || in_array($param->server_type, $param->pay_retain)) && $param->pay_type_filter) {
            $other[] = 'pay_type';
        }
        $other[] = $retain_count;
        $has_platform_belong = in_array('is_old_root_game_muid', $param->dimension);  // 是否选中了平台归属
        $has_group_belong = in_array('is_old_clique_game_muid', $param->dimension);   // 是否选中了集团归属
        if ($has_group_belong) {
            $param->dimension[] = 'group_belong';
        }
        if ($has_platform_belong) {
            $param->dimension[] = 'platform_belong';
        }
        if ($has_group_belong || $has_platform_belong) {
            foreach ($param->dimension as $key => $item) {
                if (in_array($item, ['is_old_root_game_muid', 'is_old_root_pay_muid', 'is_old_clique_game_muid', 'is_old_clique_pay_muid'])) {
                    unset($param->dimension[$key]);
                }
            }
        }
        // 判断是否需要消耗
        if ($param->export_type == 1 && $param->needCostMoney() && $row_data->route_id == RouteID::MARKET_RETAIN) {
            $other[] = 'cost_money';
        }
        $header = array_merge($param->dimension, $other, $param->export_retain_list);
        $header_map = ExportHelper::exportRetainHeadMap();
        $header_format = $this->getHeaderFormat($param, $header, $header_map, $row_data->merge_dimension, '日');


        // 去掉合计的维度先
        foreach ($param->dimension as $item) {
            if (in_array($item, DataAnalysisModel::ID_TO_NAME)) {
                $name_key = DimensionTool::idToNameKey($item);
                unset($data['sum'][$name_key]);
            }
            unset($data['sum'][$item]);
        }

        $this->unshift($data['list'], $data['sum']); // 合计塞上来

        $content = '';
        foreach ($data['list'] as $index => $item) {
            $item = (array)$item;
            $tmp = [];
            $date = $param->aggregation_time === '聚合' || $index === 0 ? $param->start_time : strtotime(date("Ymd", strtotime($item[$retain_date])));
            $in_current_month = Helpers::inCurrentMonth($date);
            foreach ($header as $column) {
                if (isset($item[$column])) {
                    $tmp[] = ExportHelper::itemFormat($column, $item, $row_data->merge_dimension);
                } else {
                    // xx留
                    $num_stay_key = 'num_day_stay_' . $column;
                    $rate_stay_key = 'rate_day_stay_' . $column;
                    $effective_day_stay_key = 'effective_day_stay_' . $column;
                    $stay_cost_key = 'stay_cost_' . $column;
                    $te_stay_key = 'te_day_stay_' . $column;
                    $now = strtotime(date("Ymd"));

                    // 屏蔽当日
                    if ($param->blocked) {
                        // 判断是否是按月
                        if ($param->aggregation_time === '按月') {
                            // 判断是否包含当月
                            if ($in_current_month) {
                                $tmp[] = '';
                                continue;
                            } else {
                                // 拿到当月的最后一天
                                $last_day_of_month = strtotime(date('Y-m-t', $date));
                                $days = floor(($now - $last_day_of_month) / 86400);
                                if ($column > $days) {
                                    $tmp[] = '';
                                    continue;
                                }
                            }

                        } else {
                            // 按日、聚合、按周都是同一个逻辑
                            $days = floor(($now - $date) / 86400) + 1;
                            if ($column >= $days) {
                                $tmp[] = '';
                                continue;
                            }
                        }
                    }
                    if (isset($item[$num_stay_key])) {
                        switch ($param->is_rate) {
                            case 0:
                                $tmp[] = $item[$num_stay_key];
                                break;
                            case 1:
                                $tmp[] = $item[$rate_stay_key] . '%';
                                break;
                            case 2:
                                $tmp[] = $item[$effective_day_stay_key] . '%';
                                break;
                            case 3:
                                $tmp[] = $item[$stay_cost_key];
                                break;
                            case 4:
                                $tmp[] = $item[$te_stay_key] . '%';
                                break;
                        }
                    } else {
                        $days = floor(($now - $date) / 86400);
                        if ($column > $days + 1) {
                            $tmp[] = '';
                        } else {
                            $tmp[] = in_array($param->is_rate, [1, 2, 4]) ? '0%' : '0';
                        }
                    }
                }
            }
            $content .= implode(',', $tmp) . PHP_EOL;
        }

        return [$header_format, $content, $param->limit, $data['is_accurate'] ?? 1];
    }

    private function actionRetain($row_data)
    {
        $row_param = json_decode($row_data->param, true);
        $param = new ActionRetainListFilterParam($row_param);
        $param->setUserPermission($row_data->user_id);

        $service = new RetainLogic();

        $data = $service->getActionRetainList($param, 0);
        // 游戏留存和注册留存字段名有点不一样
        $retain_date = 'action_date';
        // 表头
        if ($param->aggregation_time !== '聚合') {
            $other[] = 'action_date';
        }
        if ((in_array($param->aggregation_retain, $param->pay_retain)) && $param->pay_type_filter) {
            $other[] = 'pay_type';
        }
        $other[] = 'action_count';
        $has_platform_belong = in_array('is_old_root_game_muid', $param->dimension);  // 是否选中了平台归属
        $has_group_belong = in_array('is_old_clique_game_muid', $param->dimension);   // 是否选中了集团归属
        if ($has_group_belong) {
            $param->dimension[] = 'group_belong';
        }
        if ($has_platform_belong) {
            $param->dimension[] = 'platform_belong';
        }
        if ($has_group_belong || $has_platform_belong) {
            foreach ($param->dimension as $key => $item) {
                if (in_array($item, ['is_old_root_game_muid', 'is_old_root_pay_muid', 'is_old_clique_game_muid', 'is_old_clique_pay_muid'])) {
                    unset($param->dimension[$key]);
                }
            }
        }
        // 判断是否需要消耗
        if ($param->needCostMoney()) {
            $param->dimension[] = 'cost_money';
        }
        $header = array_merge($param->dimension, $other, $param->export_retain_list);
        $header_map = ExportHelper::exportRetainHeadMap();
        $header_format = $this->getHeaderFormat($param, $header, $header_map, $row_data->merge_dimension, '日');

        // 去掉合计的维度先
        foreach ($param->dimension as $item) {
            if (in_array($item, DataAnalysisModel::ID_TO_NAME)) {
                $name_key = DimensionTool::idToNameKey($item);
                unset($data['sum'][$name_key]);
            }
            unset($data['sum'][$item]);
        }

        $this->unshift($data['list'], $data['sum']); // 合计塞上来

        $content = '';
        foreach ($data['list'] as $index => $item) {
            $item = (array)$item;
            $tmp = [];
            foreach ($header as $column) {
                if (isset($item[$column])) {
                    $tmp[] = ExportHelper::itemFormat($column, $item, $row_data->merge_dimension);
                } else {
                    // xx留
                    $num_stay_key = 'num_day_stay_' . $column;
                    $rate_stay_key = 'rate_day_stay_' . $column;
                    $effective_day_stay_key = 'effective_day_stay_' . $column;
                    $stay_cost_key = 'stay_cost_' . $column;
                    $te_stay_key = 'te_day_stay_' . $column;
                    $now = strtotime(date("Ymd"));

                    $date = $param->aggregation_time !== '按日' || $index === 0 ? $param->start_time : strtotime(date("Ymd", strtotime($item[$retain_date])));
                    // 屏蔽当日
                    if ($param->blocked) {
                        if ($now == (intval($column) - 1) * 86400 + $date) {
                            $tmp[] = '';
                            continue;
                        }
                    }
                    if (isset($item[$num_stay_key])) {
                        switch ($param->is_rate) {
                            case 0:
                                $tmp[] = $item[$num_stay_key];
                                break;
                            case 1:
                                $tmp[] = $item[$rate_stay_key] . '%';
                                break;
                            case 2:
                                $tmp[] = $item[$effective_day_stay_key] . '%';
                                break;
                            case 3:
                                $tmp[] = $item[$stay_cost_key];
                                break;
                            case 4:
                                $tmp[] = $item[$te_stay_key] . '%';
                                break;
                        }
                    } else {
                        $days = floor(($now - $date) / 86400);
                        if ($column > $days + 1) {
                            $tmp[] = '';
                        } else {
                            $tmp[] = in_array($param->is_rate, [1, 2, 4]) ? '0%' : '0';
                        }
                    }
                }
            }
            $content .= implode(',', $tmp) . PHP_EOL;
        }

        return [$header_format, $content, $param->limit, $data['is_accurate'] ?? 1];
    }

    private function lyRetain($row_data)
    {
        $param = new \App\Param\LY\RetainListFilterParam(json_decode($row_data->param, true));
        $param->setUserPermission($row_data->user_id);
        $service = new LYMarketLogic();

        // 游戏留存和注册留存字段名有点不一样
        $retain_date = $param->export_type == 1 ? 'game_reg_date' : 'create_date';
        $retain_count = $param->export_type == 1 ? 'reg_uid_count' : 'role_create_count';

        // 表头
        if ($param->aggregation_time !== '聚合') {
            $other[] = $retain_date;
        }
        if ((in_array($param->aggregation_retain, $param->pay_retain) || in_array($param->server_type, $param->pay_retain)) && $param->pay_type_filter) {
            $other[] = 'pay_type';
        }
        $other[] = $retain_count;
        $header = array_merge($param->dimension, $other, $param->retain_list);
        $header_map = ExportHelper::exportRetainHeadMap();
        $header_format = $this->getHeaderFormat($param, $header, $header_map, $row_data->merge_dimension, '日');


        if ($param->export_type == 1) {
            $data = $service->getRetainList($param, 0);
        } else {
            $data = $service->getServerRetainList($param, 0);
        }
        $this->unshift($data['list'], $data['sum']); // 合计塞上来

        $content = '';
        foreach ($data['list'] as $index => $item) {
            $item = (array)$item;
            $tmp = [];
            foreach ($header as $column) {
                if (isset($item[$column])) {
                    $tmp[] = ExportHelper::itemFormat($column, $item, $row_data->merge_dimension);
                } else {
                    // xx留
                    $num_stay_key = 'num_day_stay_' . $column;
                    $rate_stay_key = 'rate_day_stay_' . $column;
                    $now = strtotime(date("Ymd"));
                    $date = $param->aggregation_time !== '按日' || $index === 0 ? $param->start_time : strtotime($item[$retain_date]);
                    if (isset($item[$num_stay_key])) {
                        // 屏蔽当日
                        if ($param->blocked) {
                            if ($now == (intval($column) - 1) * 86400 + $date) {
                                $tmp[] = '';
                                continue;
                            }
                        }
                        $tmp[] = $param->is_rate ? $item[$rate_stay_key] . '%' : $item[$num_stay_key];
                    } else {
                        // 屏蔽当日
                        if ($param->blocked) {
                            if ($now == (intval($column) - 1) * 86400 + $date) {
                                $tmp[] = '';
                                continue;
                            }
                        }
                        $days = floor(($now - $date) / 86400);
                        if ($column > $days + 1) {
                            $tmp[] = '';
                        } else {
                            $tmp[] = $param->is_rate ? '0%' : '0';
                        }
                    }
                }
            }
            $content .= implode(',', $tmp) . PHP_EOL;
        }

        return [$header_format, $content, $param->limit, $data['is_accurate'] ?? 1];
    }

    /**
     * 处理付费情况（包括投放付费情况和运营付费情况）
     *
     * @param $row_data
     *
     * @return array
     */
    private function payment($row_data)
    {
        $row_param = json_decode($row_data->param, true);
        $param = new PaymentListFilterParam($row_param);
        $raw_dimension = $row_param['dimension'];
        $param->setUserPermission($row_data->user_id);
        $payment_date = $param->export_type == 2 ? 'open_time' : 'game_reg_date';


        if ($param->export_type == 2) {
            $logic = new MarketLogic();
            $data = $logic->getServerPaymentList($param, 0);
            $data['sum']['open_days'] = '合计';
        } else {
            $logic = new PaymentLogic();
            // 判断是否存在系统版本和手机型号、平台归属、集团归属 有的话走运营数据获取
            $dimensions = ['system_version', 'device_model', 'device_price_section', 'is_old_root_game_muid', 'is_old_clique_game_muid'];
            // 自然量分摊预先查出 game_id,提升查询性能
            if ($param->official_apportion == 1) {
                DimensionTool::checkApportionDimension($param->dimension, $param->dimension_filter_raw);
                $param->setApportionGame();
            }
            if (array_intersect($dimensions, $param->dimension)) {
                $data = $logic->getOperationPaymentList($param, 0);
            } else {
                $data = $logic->getPaymentList($param, 0);
            }
        }

        $this->unshift($data['list'], $data['sum']); // 合计塞上来

        // 格式化一下原始维度
        if (in_array('game_reg_date', $raw_dimension)) {
            $key = array_search('game_reg_date', $raw_dimension);
            array_splice($raw_dimension, $key, 1);
        }
        // 格式化一下原始维度
        if (in_array('create_date', $raw_dimension)) {
            $key = array_search('create_date', $raw_dimension);
            array_splice($raw_dimension, $key, 1);
        }

        if (DimensionTool::needPushPlatform($raw_dimension)) {
            array_unshift($raw_dimension, 'platform');
        }
        // 去重
        $raw_dimension = array_unique($raw_dimension);

        // 表头
        $header = array_merge($raw_dimension, $param->fixedHeader($param->export_type, $row_data->route_id), $param->header_day_list);
        $header_map = ExportHelper::exportPaymentHeadMap();
        $header_format = $this->getHeaderFormat($param, $header, $header_map, $row_data->merge_dimension, '日');
        $key_prefix = $param->switchColumnKey();
        $content = '';
        $now = strtotime(date("Ymd"));
        foreach ($data['list'] as $index => $item) {
            $item = (array)$item;
            $tmp = [];
            // xx日
            $date = $param->aggregation_time === '聚合' || $index === 0 ? $param->start_time : strtotime(date("Ymd", strtotime($item[$payment_date])));
            $in_current_month = Helpers::inCurrentMonth($date);
            foreach ($header as $column) {
                // 普通表头
                if (isset($item[$column])) {
                    $tmp[] = ExportHelper::itemFormat($column, $item, $row_data->merge_dimension);
                } else {
                    // xx日表头
                    $column_key = $key_prefix . $column;
                    // 屏蔽当日
                    if ($param->blocked) {
                        // 判断是否是按月
                        if ($param->aggregation_time === '按月') {
                            // 判断是否包含当月
                            if ($in_current_month) {
                                $tmp[] = '';
                                continue;
                            } else {
                                // 拿到当月的最后一天
                                $last_day_of_month = strtotime(date('Y-m-t', $date));
                                $days = floor(($now - $last_day_of_month) / 86400);
                                if ($column > $days) {
                                    $tmp[] = '';
                                    continue;
                                }
                            }

                        } else {
                            // 按日、聚合、按周都是同一个逻辑
                            $days = floor(($now - $date) / 86400) + 1;
                            if ($column >= $days) {
                                $tmp[] = '';
                                continue;
                            }
                        }
                        if (isset($item[$column_key])) {
                            $tmp[] = $this->formatPercentage($item[$column_key]);
                        } else {
                            $tmp[] = '0';
                        }
                    } else {
                        $days = floor(($now - $date) / 86400) + 1;
                        if ($column > $days) {
                            $tmp[] = '';
                        } else {
                            if (isset($item[$column_key])) {
                                $tmp[] = $this->formatPercentage($item[$column_key]);
                            } else {
                                $tmp[] = '0';
                            }
                        }
                    }

                }
            }
            $content .= implode(',', $tmp) . PHP_EOL;
        }

        return [$header_format, $content, $param->limit, $data['is_accurate'] ?? 1];

    }

    /**
     * 处理付费情况按月
     *
     * @param $row_data
     *
     * @return array
     */
    private function paymentMonth($row_data)
    {
        $row_param = json_decode($row_data->param, true);
        $param = new PaymentMonthListParam($row_param);
        $raw_dimension = $row_param['dimension'];
        $param->setUserPermission($row_data->user_id);

        $service = new MarketLogic();

        $data = $service->getPaymentMonthList($param, 0);

        $this->unshift($data['list'], $data['sum']); // 合计塞上来

        // 格式化一下原始维度
        if (in_array('game_reg_date', $raw_dimension)) {
            $key = array_search('game_reg_date', $raw_dimension);
            array_splice($raw_dimension, $key, 1);
        }


        if (DimensionTool::needPushPlatform($raw_dimension)) {
            array_unshift($raw_dimension, 'platform');
        }
        // 去重
        $raw_dimension = array_unique($raw_dimension);

        // 表头
        $header = array_merge($raw_dimension, $param->fixedHeader(), $param->target);
        $header_map = ExportHelper::exportPaymentHeadMap();
        $header_format = $this->getHeaderFormat($param, $header, $header_map, $row_data->merge_dimension, '月');
        $key_prefix = $param->switchColumnKey();
        $content = '';
        foreach ($data['list'] as $index => $item) {
            $item = (array)$item;
            $tmp = [];
            foreach ($header as $column) {
                if (isset($item[$column])) {
                    $tmp[] = ExportHelper::itemFormat($column, $item, $row_data->merge_dimension);
                } else {
                    $column_key = $key_prefix . $column;
                    if (isset($item[$column_key])) {
                        $tmp[] = $item[$column_key];
                    } else {
                        $tmp[] = '0';
                    }
                }
            }
            $content .= implode(',', $tmp) . PHP_EOL;
        }

        return [$header_format, $content, $param->limit, $data['is_accurate'] ?? 1];

    }

    /**
     * 处理LY付费情况
     *
     * @param $row_data
     *
     * @return array
     */
    private function lyPayment($row_data)
    {
        $param = new \App\Param\LY\PaymentListFilterParam(json_decode($row_data->param, true));
        $param->setUserPermission($row_data->user_id);

        $service = new LYMarketLogic();

        $payment_date = $param->export_type == 1 ? 'game_reg_date' : 'create_date';

        // 表头
        $header = array_merge($param->dimension, $param->fixedHeader($param->export_type), $param->header_day_list);
        $header_map = ExportHelper::exportPaymentHeadMap();
        $header_format = $this->getHeaderFormat($param, $header, $header_map, $row_data->merge_dimension, '日');

        if ($param->export_type == 1) {
            $data = $service->getOperationPaymentList($param, 0);
        } else {
            $data = $service->getServerPaymentList($param, 0);
        }
        $this->unshift($data['list'], $data['sum']); // 合计塞上来

        $key_prefix = $param->switchColumnKey();
        $content = '';
        foreach ($data['list'] as $index => $item) {
            $item = (array)$item;
            $tmp = [];
            foreach ($header as $column) {
                if (isset($item[$column])) {
                    $tmp[] = ExportHelper::itemFormat($column, $item, $row_data->merge_dimension);
                } else {
                    // xx日
                    $date = $param->aggregation_time !== '按日' || $index === 0 ? $param->start_time : strtotime($item[$payment_date]);
                    $column_key = $key_prefix . $column;
                    $now = strtotime(date("Ymd"));
                    // 屏蔽当日
                    if ($param->blocked) {
                        if ($now == (intval($column) - 1) * 86400 + $date) {
                            $tmp[] = '';
                            continue;
                        }
                    }
                    $days = floor(($now - $date) / 86400);
                    if ($column > $days + 1) {
                        $tmp[] = '';
                    } else {
                        if (isset($item[$column_key])) {
                            $tmp[] = $item[$column_key];
                        } else {
                            $tmp[] = '0';
                        }
                    }
                }
            }
            $content .= implode(',', $tmp) . PHP_EOL;
        }

        return [$header_format, $content, $param->limit, $data['is_accurate'] ?? 1];
    }


    /**
     * 处理流失情况列表（或者是流失详情）
     *
     * @param $row_data
     *
     * @return array
     */
    private function wastage($row_data)
    {
        $param = new WastageListFilterParam(json_decode($row_data->param, true));
        $param->setUserPermission($row_data->user_id);
        if ($param->export_type === 'detail') {
            return $this->wastageDetail($param, $row_data);
        } else if ($param->export_type === 'all_detail') {
            return $this->allWastageADetail($param, $row_data);
        } else {
            return $this->wastageList($param, $row_data);
        }
    }


    /**
     * 处理发行的流失情况列表（或者是流失详情）
     *
     * @param $row_data
     *
     * @return array
     */
    private function lyWastage($row_data)
    {
        $param = new \App\Param\LY\WastageListFilterParam(json_decode($row_data->param, true));
        $param->setUserPermission($row_data->user_id);
        if ($param->export_type === 'detail') {
            return $this->lyWastageDetail($param, $row_data);
        } else {
            return $this->lyWastageList($param, $row_data);
        }
    }

    /**
     * 流失列表
     *
     * @param WastageListFilterParam $param
     *
     * @return array
     */
    private function wastageList(WastageListFilterParam $param, $row_data)
    {
        $service = new MarketLogic();

        // 表头
        $header = array_merge($param->dimension, ['ymd'], $param->target);
        $header_map = ExportHelper::exportWastageListHeadMap();
        $header_format = $this->getHeaderFormat($param, $header, $header_map, $row_data->merge_dimension, '天');

        $data = $service->getWastageList($param, 0);
        $data['sum']['date'] = '合计';
        $this->unshift($data['list'], $data['sum']); // 合计塞上来

        $content = '';
        switch ($param->is_rate) {
            case 1:
                $key_prefix = 'loss_uid_count_';
                break;
            case 2:
                $key_prefix = 'uid_count_';
                break;
            case 3:
                $key_prefix = 'new_uid_count_';
                break;
            case 0:
                $key_prefix = 'loss_rate_';
                break;
            default:
                $key_prefix = 'loss_uid_count_';
        }
        foreach ($data['list'] as $index => $item) {
            $item = (array)$item;
            $tmp = [];
            foreach ($header as $column) {
                if (isset($item[$column])) {
                    $tmp[] = ExportHelper::itemFormat($column, $item, $row_data->merge_dimension);
                } else {
                    // xx天
                    $column_key = $key_prefix . $column;
                    if (isset($item[$column_key])) {
                        $tmp[] = $item[$column_key];
                    } else {
                        $tmp[] = ''; // 没有的补个空格
                    }
                }
            }
            $content .= implode(',', $tmp) . PHP_EOL;
        }
        return [$header_format, $content, 0, $data['is_accurate'] ?? 1];
    }

    /**
     * 流失详情
     *
     * @param WastageListFilterParam $param
     *
     * @return array
     */
    public function wastageDetail(WastageListFilterParam $param, $row_data)
    {
        $service = new MarketLogic();

        // 表头
        $header = array_merge($param->dimension,
            [
                'uid', 'user_name', 'platform', 'root_game_id', 'root_game_reg_time', 'root_game_last_login_time',
                'agent_last_login_time', 'root_game_total_pay_money', 'agent_total_pay_money',
                'city', 'ip', 'pay_diff',
            ]);
        $header_map = ExportHelper::exportWastageDetailHeadMap();
        $header_format = $this->getHeaderFormat($param, $header, $header_map, $row_data->merge_dimension);

        $data = $service->getWastageDetail($param);

        $content = '';
        foreach ($data['list'] as $index => $item) {
            $item = (array)$item;
            $tmp = [];
            foreach ($header as $column) {
                if (isset($item[$column])) {
                    $tmp[] = ExportHelper::itemFormat($column, $item, $row_data->merge_dimension);
                } else {
                    $tmp[] = ''; // 没有的补个空格
                }
            }
            $content .= implode(',', $tmp) . PHP_EOL;
        }
        return [$header_format, $content, 0, $data['is_accurate'] ?? 1];
    }

    /**
     * 流失详情
     *
     * @param WastageListFilterParam $param
     *
     * @return array
     */
    public function allWastageADetail(WastageListFilterParam $param, $row_data)
    {
        $service = new MarketLogic();

        // 表头
        $header = array_merge($param->dimension,
            [
                'uid', 'user_name', 'platform', 'root_game_id', 'root_game_reg_time', 'root_game_last_login_time',
                'agent_last_login_time', 'root_game_total_pay_money', 'agent_total_pay_money',
                'city', 'ip', 'pay_diff', 'diff'
            ]);
        $header_map = ExportHelper::exportAllWastageDetailHeadMap();
        $header_format = $this->getHeaderFormat($param, $header, $header_map, $row_data->merge_dimension);


        $data = $service->getAllWastageDetail($param);

        $content = '';
        foreach ($data['list'] as $index => $item) {
            $item = (array)$item;
            $tmp = [];
            foreach ($header as $column) {
                if (isset($item[$column])) {
                    $tmp[] = ExportHelper::itemFormat($column, $item, $row_data->merge_dimension);
                } else {
                    $tmp[] = ''; // 没有的补个空格
                }
            }
            $content .= implode(',', $tmp) . PHP_EOL;
        }
        return [$header_format, $content, 0, $data['is_accurate'] ?? 1];
    }

    /**
     * 流失列表
     *
     * @param \App\Param\LY\WastageListFilterParam $param
     *
     * @return array
     */
    private function lyWastageList(\App\Param\LY\WastageListFilterParam $param, $row_data)
    {
        $service = new LYMarketLogic();

        // 表头
        $header = array_merge($param->dimension, ['ymd'], $param->target);
        $header_map = ExportHelper::exportWastageListHeadMap();
        $header_format = $this->getHeaderFormat($param, $header, $header_map, $row_data->merge_dimension, '天');


        $data = $service->getWastageList($param, 0);
        $data['sum']['date'] = '合计';
        $this->unshift($data['list'], $data['sum']); // 合计塞上来

        $content = '';
        switch ($param->is_rate) {
            case 1:
                $key_prefix = 'loss_uid_count_';
                break;
            case 2:
                $key_prefix = 'uid_count_';
                break;
            case 3:
                $key_prefix = 'new_uid_count_';
                break;
            case 0:
                $key_prefix = 'loss_rate_';
                break;
            default:
                $key_prefix = 'loss_uid_count_';
        }
        foreach ($data['list'] as $index => $item) {
            $item = (array)$item;
            $tmp = [];
            foreach ($header as $column) {
                if (isset($item[$column])) {
                    $tmp[] = ExportHelper::itemFormat($column, $item, $row_data->merge_dimension);
                } else {
                    // xx天
                    $column_key = $key_prefix . $column;
                    if (isset($item[$column_key])) {
                        $tmp[] = $item[$column_key];
                    } else {
                        $tmp[] = ''; // 没有的补个空格
                    }
                }
            }
            $content .= implode(',', $tmp) . PHP_EOL;
        }
        return [$header_format, $content, 0, $data['is_accurate'] ?? 1];
    }

    /**
     * 流失详情
     *
     * @param \App\Param\LY\WastageListFilterParam $param
     *
     * @return array
     */
    private function lyWastageDetail(\App\Param\LY\WastageListFilterParam $param, $row_data)
    {
        $service = new LYMarketLogic();

        // 表头
        $header = array_merge($param->dimension,
            ['uid', 'user_name', 'platform', 'game_reg_time', 'last_login_time',
             'total_pay_money', 'city', 'ip', 'pay_diff']);
        $header_map = ExportHelper::exportWastageDetailHeadMap();
        $header_format = $this->getHeaderFormat($param, $header, $header_map, $row_data->merge_dimension);

        $data = $service->getWastageDetail($param);

        $content = '';
        foreach ($data['list'] as $index => $item) {
            $item = (array)$item;
            $tmp = [];
            foreach ($header as $column) {
                if (isset($item[$column])) {
                    $tmp[] = ExportHelper::itemFormat($column, $item, $row_data->merge_dimension);
                } else {
                    $tmp[] = ''; // 没有的补个空格
                }
            }
            $content .= implode(',', $tmp) . PHP_EOL;
        }
        return [$header_format, $content, 0, $data['is_accurate'] ?? 1];
    }

    private function accountBind($row_data)
    {
        $param = new \App\Param\DMS\AccountBindListFilterParam(json_decode($row_data->param, true));
        $param->setUserPermission($row_data->user_id);
        $service = new MarketLogic();

        $field = ['tdate', 'uid_count', 'true_uid_count', 'phone_uid_count', 'email_uid_count', 'true_uid_percent', 'phone_uid_percent', 'email_uid_percent'];
        switch ($param->export_type) {
            case 'reg':
                $data = $service->getAccountBindList($param);
                break;
            case 'active':
                $field[] = 'money_type';
                $data = $service->getAccountActiveList($param);
                break;
            case 'pay':
                $data = $service->getAccountPayUserList($param);
                break;
            default:
                Helpers::getLogger('sync-export')->error('文件写入失败，类型有误', ['任务详情' => $this->getTaskDetail($row_data)]);
                break;
        }

        // 表头
        $header = array_merge($param->dimension, $field);
        $header_map = ExportHelper::exportAccountBindHeadMap();
        $header_format = $this->getHeaderFormat($param, $header, $header_map, $row_data->merge_dimension);

        $data['sum']['tdate'] = '合计';
        $this->unshift($data['list'], $data['sum']); // 合计塞上来
        $content = '';
        foreach ($data['list'] as $index => $item) {
            $item = (array)$item;
            $tmp = [];
            foreach ($header as $column) {
                if (isset($item[$column])) {
                    $tmp[] = ExportHelper::itemFormat($column, $item, $row_data->merge_dimension);
                } else {
                    $tmp[] = ''; // 没有的补个空格
                }
            }
            $content .= implode(',', $tmp) . PHP_EOL;
        }
        return [$header_format, $content, $param->limit, $data['is_accurate'] ?? 1];
    }

    private function lyAccountBind($row_data)
    {
        $param = new AccountBindListFilterParam(json_decode($row_data->param, true));
        $param->setUserPermission($row_data->user_id);
        $service = new LYMarketLogic();

        $field = ['tdate', 'uid_count', 'true_uid_count', 'phone_uid_count', 'email_uid_count', 'true_uid_percent', 'phone_uid_percent', 'email_uid_percent'];
        switch ($param->export_type) {
            case 'reg':
                $data = $service->getAccountBindList($param);
                break;
            case 'active':
                $field[] = 'money_type';
                $data = $service->getAccountActiveList($param);
                break;
            default:
                Helpers::getLogger('sync-export')->error('文件写入失败，类型有误', ['任务详情' => $this->getTaskDetail($row_data)]);
                break;
        }

        // 表头
        $header = array_merge($param->dimension, $field);
        $header_map = ExportHelper::exportAccountBindHeadMap();
        $header_format = $this->getHeaderFormat($param, $header, $header_map, $row_data->merge_dimension);

        $data['sum']['tdate'] = '合计';
        $this->unshift($data['list'], $data['sum']); // 合计塞上来
        $content = '';
        foreach ($data['list'] as $index => $item) {
            $item = (array)$item;
            $tmp = [];
            foreach ($header as $column) {
                if (isset($item[$column])) {
                    $tmp[] = ExportHelper::itemFormat($column, $item, $row_data->merge_dimension);
                } else {
                    $tmp[] = ''; // 没有的补个空格
                }
            }
            $content .= implode(',', $tmp) . PHP_EOL;
        }
        return [$header_format, $content, $param->limit, $data['is_accurate'] ?? 1];
    }

    private function alertPayData($row_data)
    {
        $param = json_decode($row_data->param, true);
        $model = new AlertPayDataModel();

        $data_permission = (new PermissionLogic())->getDataPermissionByUserId($row_data->user_id);
        $list = $model->getList($data_permission['agent_permission'], $data_permission['game_permission'], $param['game_reg_date']);
        // 表头
        $header = [
            'game_reg_date', 'agent_leader', 'platform', 'plat_id',
            'root_game_id', 'main_game_id', 'game_id', 'agent_group_id',
            'agent_id', 'day_reg_uid_count', 'day_cost_money', 'first_day_roi',
            'first_standard_value', 'diff'
        ];
        $header_map = ExportHelper::exportAlertPayDataHeadMap();
        $header_format = $this->getHeaderFormat($param, $header, $header_map, $row_data->merge_dimension);

        $content = $this->genCommonData($list, $header, $row_data);
        return [$header_format, $content, 0, $data['is_accurate'] ?? 1];
    }

    /**
     * 获取格式化的表头
     *
     * @param $param
     * @param $header
     * @param $header_map
     * @param int $merge_dimension
     * @param string $not_isset
     * @return array
     */
    public function getHeaderFormat($param, &$header, $header_map, int $merge_dimension = 1, string $not_isset = '')
    {
        $header_format = [];

        // 插入名称到表头列，这样插入的目的是保持顺序
        if (!$merge_dimension) {
            // 最终的表头数组
            $final_headers = [];
            // 遍历现有表头数组
            foreach ($header as $header_item) {
                // 添加当前表头字段
                $final_headers[] = $header_item;

                // 检查当前字段是否在维度名称数组中
                if (isset(ExportHelper::ID_TO_NAME_MAP[$header_item])) {
                    $name_key = DimensionTool::idToNameKey($header_item);
                    // 插入对应的维度名称列
                    $final_headers[] = $name_key;
                }

                // 检查当前字段是否有周几
                if (in_array($header_item, self::DATE_COLUMN)) {
                    if (isset($param->show_week) && $param->show_week == 1) $final_headers[] = 'week_ch';
                }

            }
            $header = $final_headers;
            $header_map = array_merge($header_map, ExportHelper::dimensionNameHeadMap());
        }

        foreach ($header as $item) {
            if (isset($header_map[$item])) {
                $header_format[] = $header_map[$item];
            } else {
                if ($not_isset) {
                    $header_format[] = $item . $not_isset;
                }
            }
        }
        return $header_format;
    }

    /**
     * 常规导出的内容 没有什么特出操作
     *
     * @param $list
     * @param $header
     * @param $row_data
     * @return string
     */
    private function genCommonData($list, $header, $row_data)
    {
        $content = '';
        foreach ($list as $index => $item) {

            $item = (array)$item;
            $tmp = [];
            foreach ($header as $column) {
                if (isset($item[$column])) {
                    $tmp[] = ExportHelper::itemFormat($column, $item, $row_data->merge_dimension);
                } else {
                    $tmp[] = ''; // 没有的补个空格
                }
            }
            $content .= implode(',', $tmp) . PHP_EOL;
        }

        return $content;
    }

    /**
     * 获取执行锁，用来判断当前任务是否正在执行。
     *
     * @param $task_id
     *
     * @return bool  获锁成功返回true 否则返回false
     */
    private function lock($task_id)
    {
        $key = 'running_export_task_' . $task_id;
        if (RedisCache::getInstance()->set($key, '1', ['NX', 'EX' => 1200])) {
            Helpers::getLogger('sync-export')->info('成功获取执行锁');
            return true;
        } else {
            Helpers::getLogger('sync-export')->info('获取执行锁失败');
            return false;
        }
    }

    /**
     * 解锁
     *
     * @param $task_id
     */
    private function unlock($task_id)
    {
        $key = 'running_export_task_' . $task_id;
        $result = RedisCache::getInstance()->del($key);
        Helpers::getLogger('sync-export')->info('unlock', ['result' => $result]);
    }

    private function getTaskDetail($row_data)
    {
        return [
            'id'          => $row_data->id,
            'user_id'     => $row_data->user_id,
            'route_id'    => $row_data->route_id,
            'create_time' => date("Y-m-d H:i:s", $row_data->create_time),
        ];
    }

    private function operationRechargeRank($row_data)
    {
        $param = new RechargeRankParam(json_decode($row_data->param, true));
        $param->setUserPermission($row_data->user_id);

        $service = new MarketLogic();

        $header_column = [];
        $header_format = [];
        if ($param->show_type === MarketLogic::TYPE_RECHARGE_ACCOUNT) {
            $header_format = ['序号', '平台', '用户ID', '账户名', '渠道ID', '渠道名', '广告ID', '根游戏', '主游戏', '游戏', '充值金额', '累计充值金额', '按游戏注册时间', '最近登录时间', '最近充值时间', '未登录天数', '未充值天数', '手机绑定情况', '首日付费金额', '手机型号', '系统语言', '报警'];
            $header_column = ['platform', 'uid', 'user_name', 'agent_id', 'agent_name', 'site_id', 'root_game_name', 'main_game_name', 'game_name', 'money', 'total_pay_money', 'game_reg_time', 'last_login_date', 'last_pay_time', 'days_not_logged_in', 'uncharged_days', 'phone_bind_info', 'first_day_pay_money', 'device_name', 'system_language'];
        } else if ($param->show_type === MarketLogic::TYPE_RECHARGE_ROLE) {
            $header_format = ['序号', '平台', '用户ID', '根游戏', '主游戏', '游戏名', '区服ID', '角色ID', '当前角色名', '曾用角色名', '充值金额', '累计充值金额', '创角时间', '最近登录时间', '最近充值时间', '未登录天数', '未充值天数', '手机绑定情况', '首日付费金额', '手机型号', '系统语言', '报警'];
            $header_column = ['platform', 'uid', 'root_game_name', 'main_game_name', 'game_name', 'server_id', 'role_id', 'role_name', 'last_role_name', 'money', 'total_pay_money', 'create_date', 'last_login_date', 'last_pay_time', 'days_not_logged_in', 'uncharged_days', 'phone_bind_info', 'first_day_pay_money', 'device_name', 'system_language'];
        } else if ($param->show_type === MarketLogic::TYPE_RECHARGE_ROOT_GAME) {
            $header_format = ['序号', '平台', '用户ID', '账户名', '根游戏名', '主游戏', '游戏名', '充值金额', '累计充值金额', '根游戏注册时间', '根游戏最后支付时间', '根游戏最后登录时间', '备注', '未登录天数', '未充值天数', '手机绑定情况', '首日付费金额', '手机型号', '系统语言', '报警'];
            $header_column = ['platform', 'uid', 'user_name', 'root_game_name', 'main_game_name', 'game_name', 'money', 'root_game_total_pay_money', 'root_game_reg_time', 'root_game_last_pay_time', 'root_game_last_login_date', 'information', 'days_not_logged_in', 'uncharged_days', 'phone_bind_info', 'first_day_pay_money', 'device_name', 'system_language'];
        }

        $param->page = 1;
        $param->rows = 50000; // 限制2000条
        $data = $service->getMarketRechargeRankList($param);

        $content = '';
        foreach ($data['list'] as $index => $item) {
            $item = (array)$item;
            $tmp = [];
            $tmp[] = $index + 1;
            foreach ($header_column as $column) {
                if (isset($item[$column])) {
                    $tmp[] = ExportHelper::itemFormat($column, $item, $row_data->merge_dimension);
                } else {
                    $tmp[] = ''; // 没有的补个空格
                }
            }
            $tmp[] = $this->formatRechargeRankWarning($item['days_not_logged_in'], $item['uncharged_days']);
            $content .= implode(',', $tmp) . PHP_EOL;
        }
        return [$header_format, $content, $param->rows, $data['is_accurate'] ?? 1];
    }

    private function marketRechargeRank($row_data)
    {
        $param = new RechargeRankParam(json_decode($row_data->param, true));
        $param->setUserPermission($row_data->user_id);

        $service = new MarketLogic();

        $header_column = [];
        $header_format = [];
        if ($param->show_type === MarketLogic::TYPE_RECHARGE_ACCOUNT) {
            $header_format = ['序号', '平台', '用户ID', '账户名', '渠道ID', '渠道名', '广告ID', '根游戏', '主游戏', '游戏', '充值金额', '累计充值金额', '按游戏注册时间', '最近登录时间', '最近充值时间', '未登录天数', '未充值天数', '手机绑定情况', '首日付费金额', '手机型号', '系统语言', '报警'];
            $header_column = ['platform', 'uid', 'user_name', 'agent_id', 'agent_name', 'site_id', 'root_game_name', 'main_game_name', 'game_name', 'money', 'total_pay_money', 'game_reg_time', 'last_login_date', 'last_pay_time', 'days_not_logged_in', 'uncharged_days', 'phone_bind_info', 'first_day_pay_money', 'device_name', 'system_language'];
        } else if ($param->show_type === MarketLogic::TYPE_RECHARGE_ROLE) {
            $header_format = ['序号', '平台', '用户ID', '根游戏', '主游戏', '游戏名', '区服ID', '渠道ID', '渠道名', '广告ID', '角色ID', '当前角色名', '曾用角色名', '充值金额', '累计充值金额', '创角时间', '最近登录时间', '最近充值时间', '未登录天数', '未充值天数', '手机绑定情况', '首日付费金额', '手机型号', '系统语言', '报警'];
            $header_column = ['platform', 'uid', 'root_game_name', 'main_game_name', 'game_name', 'server_id', 'agent_id', 'agent_name', 'site_id', 'role_id', 'role_name', 'last_role_name', 'money', 'total_pay_money', 'create_date', 'last_login_date', 'last_pay_time', 'days_not_logged_in', 'uncharged_days', 'phone_bind_info', 'first_day_pay_money', 'device_name', 'system_language'];
        } else if ($param->show_type === MarketLogic::TYPE_RECHARGE_ROOT_GAME) {
            $header_format = ['序号', '平台', '用户ID', '账户名', '根游戏名', '主游戏', '游戏名', '渠道ID', '渠道名', '广告ID', '充值金额', '累计充值金额', '根游戏注册时间', '根游戏最后支付时间', '根游戏最后登录时间', '备注', '未登录天数', '未充值天数', '手机绑定情况', '首日付费金额', '手机型号', '系统语言', '报警'];
            $header_column = ['platform', 'uid', 'user_name', 'root_game_name', 'main_game_name', 'game_name', 'agent_id', 'agent_name', 'site_id', 'money', 'root_game_total_pay_money', 'root_game_reg_time', 'root_game_last_pay_time', 'root_game_last_login_date', 'information', 'days_not_logged_in', 'uncharged_days', 'phone_bind_info', 'first_day_pay_money', 'device_name', 'system_language'];
        }

        $param->page = 1;
        $param->rows = 50000; // 限制2000条
        $data = $service->getMarketRechargeRankList($param);

        $content = '';
        foreach ($data['list'] as $index => $item) {
            $item = (array)$item;
            $tmp = [];
            $tmp[] = $index + 1;
            foreach ($header_column as $column) {
                if (isset($item[$column])) {
                    $tmp[] = ExportHelper::itemFormat($column, $item, $row_data->merge_dimension);
                } else {
                    $tmp[] = ''; // 没有的补个空格
                }
            }
            $tmp[] = $this->formatRechargeRankWarning($item['days_not_logged_in'], $item['uncharged_days']);
            $content .= implode(',', $tmp) . PHP_EOL;
        }
        return [$header_format, $content, $param->rows, $data['is_accurate'] ?? 1];
    }

    /**
     * 日志报表
     *
     * @param $row_data
     *
     * @return array
     */
    private function getLogPercentDetail($row_data)
    {
        $param = new LogListParam(json_decode($row_data->param, true));
        $service = new LogLogic();

        // dimension: platform, department, user p_router router

        $dimension = [];
        $header_format = [];
        foreach ($param->dimension as $item) {
            switch ($item) {
                case 'platform':
                    $dimension[] = 'platform_name';
                    $header_format[] = '平台';
                    break;
                case 'department':
                    $dimension[] = 'department_name';
                    $header_format[] = '部门';
                    break;
                case 'user':
                    $dimension[] = 'username';
                    $header_format[] = '负责人';
                    break;
                case 'p_router':
                    $dimension[] = 'p_router';
                    $header_format[] = '一级路由';
                    break;
                case 'router':
                    $dimension[] = 'router';
                    $header_format[] = '二级级路由';
                    break;
            }
        }
        $fixed_header_format = ['查询次数', '查询占比', '导出次数', '导出占比', '新增次数', '新增占比', '编辑次数',
                                '编辑占比', '删除次数', '删除占比'];
        $fixed_header = ['get_num', 'get_percent', 'export_num', 'export_percent', 'add_num', 'add_percent', 'edit_num',
                         'edit_percent', 'delete_num', 'delete_percent'];
        if (!array_intersect(['p_router', 'router'], $param->dimension)) {
            array_unshift($fixed_header_format, '登录占比');
            array_unshift($fixed_header_format, '登录次数');
            array_unshift($fixed_header, 'login_percent');
            array_unshift($fixed_header, 'login_num');
        }
        $header_format = array_merge($header_format, $fixed_header_format);
        // 表头
        $header = array_merge($dimension, $fixed_header);

        $list = $service->getLogPercentDetail($param);

        $content = '';
        $route_uri_name_map = PermissionService::routeUriNameMap();
        foreach ($list as $index => $item) {
            $item = (array)$item;
            $tmp = [];
            foreach ($header as $column) {
                if (isset($item[$column])) {
                    if ($column === 'p_router' || $column === 'router') {
                        $tmp[] = $route_uri_name_map[$item[$column]];
                    } else {
                        $tmp[] = $item[$column];
                    }
                } else {
                    $tmp[] = ''; // 没有的补个空格
                }
            }
            $content .= implode(',', $tmp) . PHP_EOL;
        }
        return [$header_format, $content, 0, $data['is_accurate'] ?? 1];
    }

    /**
     * 运营利润列表
     *
     * @param $row_data
     *
     * @return array
     */
    private function operationProfit($row_data)
    {
        $param = new DimProfitListParam(json_decode($row_data->param, true));
        $logic = new OperationProfitLogic();
        $param->setUserPermission($row_data->user_id);

        // 表头
        $header = array_merge($param->dimension, ['game_date'],
            [
                'game_pay_money',
                'channel_pay_money',
                'apple_pay_money',
                'yyb_pay_money',
                'applet_pay_money',
                'pay_way_pay_money',
                'dehan_pay_money',
                'dehan_divide_money',
                'ios_divide',
                'yyb_divide',
                'applet_divide',
                'applet_divide_rate',
                'authorization_money',
                'endorsement_money',
                'server_cost',
            ]
        );
        if ($param->true_cost) {
            $header[] = 'true_cost';
        } else {
            $header[] = 'cost_money';
        }
        $header = array_merge($header, ['finance_money', 'profit', 'operation_profit', 'profit_rate']);
        $header_map = ExportHelper::exportProfitListHeadMap();
        $header_format = $this->getHeaderFormat($param, $header, $header_map, $row_data->merge_dimension);

        $data = $logic->getDimList($param, 2);
        $data['sum']['game_date'] = '合计';
        $this->unshift($data['list'], $data['sum']); // 合计塞上来

        $content = '';
        foreach ($data['list'] as $index => $item) {
            $item = (array)$item;
            $tmp = [];
            foreach ($header as $column) {
                if (isset($item[$column])) {
                    $tmp[] = ExportHelper::itemFormat($column, $item, $row_data->merge_dimension);
                } else {
                    $tmp[] = ''; // 没有的补个空格
                }
            }
            $content .= implode(',', $tmp) . PHP_EOL;
        }
        return [$header_format, $content, 0, $data['is_accurate'] ?? 1];
    }

    private function lyRechargeRank($row_data)
    {
        $param = new LYRechargeRankParam(json_decode($row_data->param, true));
        $param->setUserPermission($row_data->user_id);

        $service = new LYMarketLogic();

        if ($param->show_type === LYMarketLogic::TYPE_RECHARGE_ACCOUNT) {
            $header_format = ['序号', '平台', '用户ID', '账户名', '渠道ID', '渠道名', '广告ID', '游戏', '充值金额', '累计充值金额', '按游戏注册时间', '最近登录时间', '最近充值时间', 'open_id', '报警'];
            $header_column = ['platform', 'uid', 'user_name', 'agent_id', 'agent_name', 'site_id', 'game_name', 'money', 'total_pay_money', 'game_reg_time', 'last_login_date', 'last_pay_time', 'open_id'];
        } else if ($param->show_type === LYMarketLogic::TYPE_RECHARGE_ROLE) {
            $header_format = ['序号', '平台', '用户ID', '主游戏', '渠道ID', '渠道名', '游戏名', '区服ID', '角色ID', '角色名', '充值金额', '累计充值金额', '创角时间', '最近登录时间', '最近充值时间', 'open_id', '报警'];
            $header_column = ['platform', 'uid', 'main_game_name', 'agent_id', 'agent_name', 'game_name', 'server_id', 'role_id', 'role_name', 'money', 'total_pay_money', 'create_date', 'last_login_date', 'last_pay_time', 'open_id'];
        }
        // else if ($param->show_type === LYMarketLogic::TYPE_RECHARGE_ROOT_GAME) {
        //     $header_format = ['序号', '平台', '用户ID', '账户名', '根游戏名', '充值金额', '累计充值金额', '根游戏注册时间', '根游戏最后支付时间', '根游戏最后登录时间', '报警'];
        //     $header_column = ['platform', 'uid', 'user_name', 'root_game_name', 'money', 'root_game_total_pay_money', 'root_game_reg_time', 'root_game_last_pay_time', 'root_game_last_login_date'];
        // }

        $param->page = 1;
        $param->rows = 50000; // 限制2000条
        $data = $service->getRechargeRankList($param);

        $content = '';
        foreach ($data['list'] as $index => $item) {
            $item = (array)$item;
            $tmp = [];
            $tmp[] = $index + 1;
            foreach ($header_column as $column) {
                if (isset($item[$column])) {
                    $tmp[] = ExportHelper::itemFormat($column, $item, $row_data->merge_dimension);
                } else {
                    $tmp[] = ''; // 没有的补个空格
                }
            }
            $tmp[] = $this->formatRechargeRankWarning($item['days_not_logged_in'], $item['uncharged_days']);
            $content .= implode(',', $tmp) . PHP_EOL;
        }
        return [$header_format, $content, $param->rows, $data['is_accurate'] ?? 1];
    }

    private function formatRechargeRankWarning($not_login_day, $uncharged_day)
    {
        $msg = '';
        if ($not_login_day >= 3) {
            $msg .= "{$not_login_day}天未登录";
        }

        if ($not_login_day >= 3 && $uncharged_day >= 3) {
            $msg .= '，';
        }

        if ($uncharged_day >= 3) {
            $msg .= "{$uncharged_day}天未充值";
        }

        return $msg;
    }


    /**
     * 处理付费情况（包括投放付费情况和运营付费情况）
     *
     * @param $row_data
     *
     * @return array
     * @throws \ReflectionException
     */
    private function payEstimate($row_data)
    {

        $param = new PayEstimateParam(json_decode($row_data->param, true));
        $param->setUserPermission($row_data->user_id);

        $service = new PayEstimateLogic();

        // 表头
        $header = array_merge($param->dimension, $param->fixedHeader(), $param->header_day_list);
        // 用付费情况的导出映射就可以了
        $header_map = ExportHelper::exportPaymentHeadMap();
        $header_format = $this->getHeaderFormat($param, $header, $header_map, $row_data->merge_dimension, '日');

        $data = $service->getPayEstimateList($param, 0);
        $list = $data['list'];
        array_unshift($list, $data['sum']); // 合计塞上来

        $key_prefix = $param->switchColumnKey();
        $content = '';
        foreach ($list as $index => $item) {
            $item = (array)$item;
            $tmp = [];
            foreach ($header as $column) {
                if (isset($item[$column])) {
                    $tmp[] = ExportHelper::itemFormat($column, $item, $row_data->merge_dimension);
                } else {
                    // xx日
                    $column_key = $key_prefix . $column . '_raw';
                    if (isset($item[$column_key])) {
                        $tmp[] = $item[$column_key];
                    } else {
                        // 没有的补0
                        $tmp[] = '0';
                    }
                }
            }
            $content .= implode(',', $tmp) . PHP_EOL;
        }

        return [$header_format, $content, $param->limit, $data['is_accurate'] ?? 1];

    }

    /**
     * 处理区服数据概览
     *
     * @param $row_data
     *
     * @return array
     */
    private function serverDataOverview($row_data)
    {
        $param = new ServerDataOverviewListFilterParam(json_decode($row_data->param, true));
        $param->setUserPermission($row_data->user_id);

        $service = new MarketLogic();

        // 表头
        $header = array_merge($param->dimension, ['open_day_type'], $param->date_list);
        $header_map = ExportHelper::exportServerDataOverviewHeadMap();
        $header_format = $this->getHeaderFormat($param, $header, $header_map, $row_data->merge_dimension, '_');
        $data = $service->getServerDataOverviewList($param);
        $this->unshift($data['list'], $data['sum']); // 合计塞上来

        $key_prefix = $param->switchColumnKey();
        $content = '';
        foreach ($data['list'] as $index => $item) {
            $item = (array)$item;
            $tmp = [];
            foreach ($header as $column) {
                if (isset($item[$column])) {
                    $tmp[] = ExportHelper::itemFormat($column, $item, $row_data->merge_dimension);
                } else {
                    // 日期
                    $column_key = $key_prefix . $column;
                    if (isset($item[$column_key])) {
                        $tmp[] = $item[$column_key];
                    } else {
                        if ($index === 0) {
                            $tmp[] = '';
                            continue; // 合计列
                        }
                        if ($key_prefix === 'prate_') {
                            $tmp[] = '0%';
                        } else {
                            $tmp[] = '0';
                        }
                    }
                }
            }
            $content .= implode(',', $tmp) . PHP_EOL;
        }

        return [$header_format, $content, $param->limit, $data['is_accurate'] ?? 1];
    }

    /**
     * 消耗录入导出
     *
     * @param $row_data
     *
     * @return array
     */
    private function costInput($row_data)
    {
        $row_param = json_decode($row_data->param, true);
        // 达人消耗
        if (isset($row_param['master_cost']) && $row_param['master_cost'] == 1) {
            return $this->masterCost($row_param, $row_data->user_id, $row_data);
        }

        $param = new DMSCostInputLogParam($row_param);
        $param->setUserPermission($row_data->user_id);
        $param->page = 1;
        $param->rows = 1000000;// 支持100w导出
        $data = (new V2DWDDayCostLogModel())->getList($param);

        $list = $data['list'];
        $logic = new CostInputLogic();
        /* @var $list Collection */
        foreach ($list as $item) {
            $item->input_type = $logic->inputTypeMapAlias($item);
            $item->money_type = $item->money_type == 1 ? '消耗' : '返货';
            $item->aweme_name = '';
        }

        // 表头
        $header = [
            'platform', 'game_id', 'agent_group_id', 'agent_id', 'site_id', 'tdate', 'money',
            'ori_money', 'operator', 'insert_time', 'update_time', 'input_type', 'money_type',
            'aweme_account', 'interface_person', 'remark'
        ];
        $header_map = ExportHelper::exportCostInputHeadMap();
        $header_format = $this->getHeaderFormat($param, $header, $header_map, $row_data->merge_dimension);

        $content = $this->genCommonData($list, $header, $row_data);
        return [$header_format, $content, $param->rows, $data['is_accurate'] ?? 1];
    }

    private function masterCost($row_param, $user_id, $row_data)
    {
        $param = new MasterCostInputLogParam($row_param);
        $param->setUserPermission($user_id);
        $param->page = 1;
        $param->rows = 1000000;// 支持100w导出
        $data = (new CostInputLogic())->getMasterCostList($param);

        $list = $data['list'];

        // 表头
        $header = [
            'platform', 'apportion_agent_id', 'clique_id', 'agent_group_id', 'money',
            'start_date', 'end_date', 'operator',
        ];
        $header_map = ExportHelper::exportCostInputHeadMap();
        $header_format = $this->getHeaderFormat($param, $header, $header_map, $row_data->merge_dimension);

        $content = $this->genCommonData($list, $header, $row_data);
        return [$header_format, $content, $param->rows, $data['is_accurate'] ?? 1];
    }

    private function lyChannelProfit($row_data)
    {
        $param = new LYProfitListParam(json_decode($row_data->param, true));
        $param->setUserPermission($row_data->user_id);
        $logic = new DataHubLogic();
        $data = $logic->getDimList($param);
        $other = [
            'advertising', 'authorization_money', 'beta_test_cost', 'beta_test_fee', 'channel_name', 'coupon_money',
            'daily_cost', 'endorsement_money', 'game_pay_money', 'net_profit', 'onsale_cost', 'operating_cost',
            'operating_profit', 'package_cost', 'publicity_cost', 'self_recharge_fee', 'self_recharge_money',
            'server_cost', 'softwarecopyright_money', 'versionnum_money'
        ];
        // 表头
        if ($param->aggregation_type !== '聚合') {
            array_unshift($other, 'game_date');
        }

        $header = array_merge($param->dimension, $other);
        $header_map = ExportHelper::exportLYChannelProfitHeadMap();

        $header_format = $this->getHeaderFormat($param, $header, $header_map, $row_data->merge_dimension);
        $this->unshift($data['list'], $data['sum']); // 合计塞上来

        $content = '';
        foreach ($data['list'] as $index => $item) {
            $item = (array)$item;
            $tmp = [];
            foreach ($header as $column) {
                if (isset($item[$column])) {
                    $tmp[] = ExportHelper::itemFormat($column, $item, $row_data->merge_dimension);
                } else {
                    $tmp[] = ''; // 没有的补个空格
                }
            }
            $content .= implode(',', $tmp) . PHP_EOL;
        }
        return [$header_format, $content, 0, $data['is_accurate'] ?? 1];
    }


    /**
     *
     * 对外投放数据分析（推广数据分析）
     *
     * @param $row_data
     *
     * @return array
     */
    private function outOverview($row_data)
    {
        $param = new OuterOverViewListFilterParam(json_decode($row_data->param, true));
        $param->setUserPermission($row_data->user_id);
        $service = new OuterLogic();

        $param->handleTarget();// 处理指标
        $param->limit = 200000;
        $data = $service->getOverviewList($param);
        if (in_array('agent_id', $param->dimension)) {
            $key = array_search('agent_id', $param->dimension);
            array_splice($param->dimension, $key, 1, ['agent_id', 'agency']);
        }
        // 表头
        $header = array_merge($param->dimension, ['date', 'cost_money'], $param->target);

        $header_map = ExportHelper::exportOutOverViewHeadMap();
        $header_format = $this->getHeaderFormat($param, $header, $header_map, $row_data->merge_dimension);


        // 去掉合计的维度先
        foreach ($param->dimension as $item) {
            if (in_array($item, DataAnalysisModel::ID_TO_NAME)) {
                $name_key = DimensionTool::idToNameKey($item);
                unset($data['sum'][$name_key]);
            }
            unset($data['sum'][$item]);
        }
        $this->unshift($data['list'], $data['sum']); // 合计塞上来

        $content = '';
        foreach ($data['list'] as $index => $item) {
            $item = (array)$item;
            $tmp = [];
            foreach ($header as $column) {
                if (isset($item[$column])) {
                    $tmp[] = ExportHelper::itemFormat($column, $item, $row_data->merge_dimension);
                } else {
                    $tmp[] = ''; // 没有的补个空格
                }
            }
            $content .= implode(',', $tmp) . PHP_EOL;
        }
        return [$header_format, $content, $param->limit, $data['is_accurate'] ?? 1];
    }


    /**
     * 流水预估
     *
     * @param $row_data
     *
     * @return array
     * @throws \Exception
     */
    private function flowEstimateList($row_data)
    {
        $param = new FlowEstimateParam(json_decode($row_data->param, true));
        $param->setUserPermission($row_data->user_id);
        $param->setDayList();
        $param->setCostDate();
        $logic = new MarketLogic();


        $data = $logic->getFlowEstimateList($param);
        $target_date_list = [];
        $sum = [
            'game_reg_date' => '合计',
            'cost'          => 0,
            'reg_uid_count' => 0,
        ];
        foreach ($param->target_date_list as $item) {
            $target_date_list[] = 'tpay_' . $item;
            $sum['tpay_' . $item] = 0;
        }
        // 表头
        $header = array_merge($param->dimension, ['game_reg_date', 'cost', 'reg_uid_count'], $target_date_list);
        $header_map = ExportHelper::exportFlowEstimateHeadMap();
        $header_format = [];
        foreach ($header as $item) {
            if (isset($header_map[$item])) {
                $header_format[] = $header_map[$item];
            } else {
                $header_format[] = '`' . str_replace('tpay_', '', $item); // 日期指标 加个`防止Excel乱搞日期
            }
        }

        // 计算合计
        foreach ($data as $item) {
            $item = (array)$item;
            $sum['cost'] += $item['cost'];
            $sum['reg_uid_count'] += $item['reg_uid_count'];
            foreach ($param->target_date_list as $day) {
                $sum['tpay_' . $day] += $item['tpay_' . $day];
            }
        }
        $this->unshift($data, $sum);

        $content = $this->genCommonData($data, $header, $row_data);
        return [$header_format, $content, 0, $data['is_accurate'] ?? 1];
    }

    private function sdkStepList($row_data)
    {
        $param = new SdkStepListParam(json_decode($row_data->param, true));
        $param->setUserPermission($row_data->user_id);
        $param->setSdkActionId();

        $data = (new SdkStepLogic())->getSdkStepList($param);
        // 表头
        $target_list = [];
        foreach ($param->target as $item) {
            $target_list[] = 'sdk_action_id_' . $item['sdk_action_id'];
        }
        $header = array_merge($param->dimension, $target_list);
        $header_map = ExportHelper::exportSDKStepMap($param->target);
        $header_format = $this->getHeaderFormat($param, $header, $header_map, $row_data->merge_dimension);

        $content = $this->genCommonData($data['list'], $header, $row_data);
        return [$header_format, $content, $param->limit, $data['is_accurate'] ?? 1];
    }


    private function lySDKStepList($row_data)
    {
        $param = new \App\Param\LY\SdkStepListParam(json_decode($row_data->param, true));
        $param->setUserPermission($row_data->user_id);
        $param->setSdkActionId();

        $data = (new \App\Logic\LY\SdkStepLogic())->getSdkStepList($param);
        // 表头
        $target_list = [];
        foreach ($param->target as $item) {
            $target_list[] = 'sdk_action_id_' . $item['sdk_action_id'];
        }
        $header = array_merge($param->dimension, $target_list);
        $header_map = ExportHelper::exportSDKStepMap($param->target);
        $header_format = $this->getHeaderFormat($param, $header, $header_map, $row_data->merge_dimension);

        $content = $this->genCommonData($data['list'], $header, $row_data);
        return [$header_format, $content, $param->limit, $data['is_accurate'] ?? 1];
    }


    /**
     * 资金流水记录导出
     *
     * @param $row_data
     *
     * @return array
     */
    private function financeFund($row_data)
    {
        $param = new FinanceListParam(json_decode($row_data->param, true));
        $param->page = 1;
        $param->rows = 1000000;// 支持100w导出
        $param->setUserPermission($row_data->user_id);
        $data = (new FinanceLogic())->getFundListWithPermission($param);
        $list = $data['list'];

        $header = $param->target;
        $header_map = ExportHelper::exportFinanceFundHeadMap();
        $header_format = $this->getHeaderFormat($param, $header, $header_map, $row_data->merge_dimension);

        $content = $this->genFinanceFundCommonData($list, $header, $row_data);

        return [$header_format, $content, $param->rows, $data['is_accurate'] ?? 1];
    }

    private function genFinanceFundCommonData($list, $header, $row_data)
    {
        $content = '';
        foreach ($list as $index => $item) {
            $item = (array)$item;
            $tmp = [];
            foreach ($header as $column) {
                if (isset($item[$column])) {
                    if (in_array($column, ['account_id', 'remitter', 'payee', 'transaction_seq', 'majordomo_account_id', 'wallet_id'])) {
                        $tmp[] = '="' . trim($item[$column]) . '"';
                    } else {
                        $tmp[] = ExportHelper::itemFormat($column, $item, $row_data->merge_dimension);
                    }
                } else {
                    $tmp[] = ''; // 没有的补个空格
                }
            }
            $content .= implode(',', $tmp) . PHP_EOL;
        }

        return $content;
    }

    /**
     * 财务对账报表导出
     *
     * @param $row_data
     *
     * @return array
     */
    private function financeReport($row_data)
    {
        $param = new FundBalanceLogParam(json_decode($row_data->param, true));
        $param->page = 1;
        $param->rows = 1000000;// 支持100w导出
        $param->setUserPermission($row_data->user_id);
        $data = (new FinanceLogic())->getReport($param);
        $list = $data['list'];

        $header = $param->target;
        $header_map = ExportHelper::exportFinanceReportHeadMap();
        $header_format = $this->getHeaderFormat($param, $header, $header_map, $row_data->merge_dimension);
        $content = $this->genFinanceReportCommonData($list, $header, $row_data);

        return [$header_format, $content, $param->rows, $data['is_accurate'] ?? 1];
    }

    private function genFinanceReportCommonData($list, $header, $row_data)
    {
        $content = '';
        foreach ($list as $index => $item) {
            $item = (array)$item;
            $tmp = [];
            foreach ($header as $column) {
                if (isset($item[$column])) {
                    if (in_array($column, ['account_id', 'remitter', 'payee'])) {
                        $tmp[] = '="' . trim($item[$column]) . '"';
                    } else if ($column === 'media_type') {
                        $tmp[] = MediaType::MEDIA_TYPE_MAP[$item[$column]];
                    } else {
                        $tmp[] = ExportHelper::itemFormat($column, $item, $row_data->merge_dimension);
                    }
                } else {
                    $tmp[] = ''; // 没有的补个空格
                }
            }
            $content .= implode(',', $tmp) . PHP_EOL;
        }

        return $content;
    }

    /**
     *  邮件结算导出
     *
     * @param $row_data
     *
     * @return array
     */
    private function financeMail($row_data)
    {
        $param = new FinanceMailListParam(json_decode($row_data->param, true));
        $param->page = 1;
        $param->rows = 1000000;// 支持100w导出
        $data = (new FinanceLogic())->getMailList($param);
        $list = $data['list'];

        $header = $param->target;
        $header_map = ExportHelper::exportFinanceMailHeadMap();
        $header_format = $this->getHeaderFormat($param, $header, $header_map, $row_data->merge_dimension);

        $content = $this->genFinanceFundCommonData($list, $header, $row_data);

        return [$header_format, $content, $param->rows, $data['is_accurate'] ?? 1];
    }

    /**
     *  台账列表导出
     *
     * @param $row_data
     *
     * @return array
     */
    private function financeAccountCenter($row_data)
    {
        $param = new FinanceStandingBookListParam(json_decode($row_data->param, true));
        $param->page = 1;
        $param->rows = 1000000;// 支持100w导出
        $user_data = (new UserModel())->getData($row_data->user_id);
        $param->user = $user_data->name;
        $data = (new FinanceLogic())->getStandingBookList($param);
        $this->unshift($data['list'], $data['sum']); // 合计塞上来
        $list = $data['list'];

        $header = $param->target;
        $header_map = ExportHelper::exportFinanceAccountCenterHeadMap();
        $header_format = $this->getHeaderFormat($param, $header, $header_map, $row_data->merge_dimension);

        $content = $this->genFinanceAccountCenterData($list, $header);

        return [$header_format, $content, $param->rows, $data['is_accurate'] ?? 1];
    }

    private function genFinanceAccountCenterData($list, $header)
    {
        $content = '';
        foreach ($list as $index => $item) {
            $item = (array)$item;
            $tmp = [];
            foreach ($header as $column) {
                if (isset($item[$column])) {
                    if (in_array($column, ['account_id', 'agent_id', 'account_name'])) {
                        $tmp[] = '="' . trim($item[$column]) . '"';
                    } else if ($column === 'background_data') {
                        $tmp[] = $item[$column] ? '是' : '否';
                    } else {
                        $tmp[] = trim($item[$column]);
                    }
                } else {
                    $tmp[] = ''; // 没有的补个空格
                }
            }
            $content .= implode(',', $tmp) . PHP_EOL;
        }

        return $content;
    }

    /**
     * 截图任务文件导出
     *
     * @param $row_data
     *
     * @return array
     */
    private function financeScreenshotMission($row_data)
    {
        $param = new FundScreenshotMissionAccountListParam(json_decode($row_data->param, true));
        $param->source_filename = $row_data->source_filename;
        $param->setUserPermission($row_data->user_id);
        return (new FinanceLogic())->exportScreenshotWithPermission($param);
    }


    /**
     * 财务台账
     *
     * @param $row_data
     *
     * @return array
     * @throws \Exception
     */
    private function financeAccountList($row_data)
    {
        $param = new FinanceAccountParam(json_decode($row_data->param, true));
        $param->setUserPermission($row_data->user_id);
        $logic = new FinanceAccountLogic();

        $data = $logic->getFinanceAccountList($param);

        // 表头
        $header = array_merge($param->dimension, ['fa_cost_date', 'fa_cost']);
        $header_map = ExportHelper::exportFinanceAccountHeadMap();
        $header_format = $this->getHeaderFormat($param, $header, $header_map, $row_data->merge_dimension);

        $this->unshift($data['list'], $data['sum']); // 合计塞上来

        $content = $this->genCommonData($data['list'], $header, $row_data);
        return [$header_format, $content, $param->limit, $data['is_accurate'] ?? 1];
    }

    public function unshift(&$list, $sum)
    {
        if (is_array($list)) {
            array_unshift($list, $sum);
        } else if ($list instanceof Collection) {
            $list->prepend($sum);
        }
    }

    /**
     * 审计总览
     *
     * @param $row_data
     *
     * @return array
     * @throws \Exception
     */
    private function auditOverview($row_data)
    {
        $param = new AuditOverviewParam(json_decode($row_data->param, true));
        $param->rows = ********;
        $param->setUserPermission($row_data->user_id);
        $logic = new MarketLogic();
        $data = $logic->getAuditOverviewList($param);
        $list = $data['list']->toArray();
        array_unshift($list, $data['sum']);

        // 表头
        $header = array_merge($param->source_dimension, ['day_cost_money', 'day_ori_cost_money', 'day_pay_money']);
        $header_map = ExportHelper::exportAuditOverviewMap();
        $header_format = $this->getHeaderFormat($param, $header, $header_map, $row_data->merge_dimension);

        $content = $this->genAuditCommonData($list, $header, $row_data);
        return [$header_format, $content, $param->rows, $data['is_accurate'] ?? 1];
    }

    private function genAuditCommonData($list, $header, $row_data)
    {
        $content = '';
        foreach ($list as $index => $item) {
            $item = (array)$item;
            $tmp = [];
            foreach ($header as $column) {
                if (isset($item[$column])) {
                    if ($column === 'account_id' || $column === 'account_name') {
                        $v = $item[$column];
                        $tmp[] = str_replace(',', '_', $v);
                    } else {
                        $tmp[] = ExportHelper::itemFormat($column, $item, $row_data->merge_dimension);
                    }
                } else {
                    $tmp[] = ''; // 没有的补个空格
                }
            }
            $content .= implode(',', $tmp) . PHP_EOL;
        }

        return $content;
    }

    private function basicSalary($row_data)
    {
        $param = json_decode($row_data->param, true);
        $param['page'] = 1;
        $param['rows'] = 10000;
        // 表头
        $header_map = ExportHelper::exportBasicSalaryMap();

        $data = (new SalaryLogic())->getList($param);
        $content = '';
        foreach ($data['list'] as $index => $item) {
            $item = (array)$item;
            $tmp = [];
            foreach ($header_map as $column => $column_name) {
                if (isset($item[$column])) {
                    $tmp[] = ExportHelper::itemFormat($column, $item, $row_data->merge_dimension);
                } else {
                    $tmp[] = ''; // 没有的补个空格
                }
            }
            $content .= implode(',', $tmp) . PHP_EOL;
        }
        return [$header_map, $content, $param['rows'], $data['is_accurate'] ?? 1];
    }

    private function basicCost($row_data)
    {
        $param = json_decode($row_data->param, true);
        $param['page'] = 1;
        $param['rows'] = 10000;
        // 表头
        $header_map = ExportHelper::exportBasicCostMap();

        $data = (new CostLogic())->getList($param);
        $content = '';
        foreach ($data['list'] as $index => $item) {
            $item = (array)$item;
            $tmp = [];
            foreach ($header_map as $column => $column_name) {
                if (isset($item[$column])) {
                    $tmp[] = ExportHelper::itemFormat($column, $item, $row_data->merge_dimension);
                } else {
                    $tmp[] = ''; // 没有的补个空格
                }
            }
            $content .= implode(',', $tmp) . PHP_EOL;
        }
        return [$header_map, $content, $param['rows'], $data['is_accurate'] ?? 1];
    }

    private function shareSalary($row_data)
    {
        $param = json_decode($row_data->param, true);
        // 表头
        $header_map = ExportHelper::exportShareSalaryMap();

        $data = (new SalaryLogic())->exportShareDetailList($param);
        $content = '';
        foreach ($data as $index => $item) {
            $item = (array)$item;
            $tmp = [];
            foreach ($header_map as $column => $column_name) {
                if (isset($item[$column])) {
                    $tmp[] = ExportHelper::itemFormat($column, $item, $row_data->merge_dimension);
                } else {
                    $tmp[] = ''; // 没有的补个空格
                }
            }
            $content .= implode(',', $tmp) . PHP_EOL;
        }
        return [$header_map, $content, 1000, $data['is_accurate'] ?? 1];
    }

    private function shareCost($row_data)
    {
        $param = json_decode($row_data->param, true);
        // 表头
        $header_map = ExportHelper::exportShareCostMap();

        $data = (new CostLogic())->exportShareDetailList($param);
        $content = '';
        foreach ($data as $index => $item) {
            $item = (array)$item;
            $tmp = [];
            foreach ($header_map as $column => $column_name) {
                if (isset($item[$column])) {
                    $tmp[] = ExportHelper::itemFormat($column, $item, $row_data->merge_dimension);
                } else {
                    $tmp[] = ''; // 没有的补个空格
                }
            }
            $content .= implode(',', $tmp) . PHP_EOL;
        }
        return [$header_map, $content, 1000, $data['is_accurate'] ?? 1];
    }

    private function visualizationProfit($row_data)
    {
        $param = json_decode($row_data->param, true);
        $param['dimension'] = 'department';
        // 表头
        $header_map = ExportHelper::exportVisualizationProfitMap();

        $data = (new VisualizationLogic())->getList($param);
        $content = '';
        foreach ($data as $index => $item) {
            $item = (array)$item;
            $tmp = [];
            foreach ($header_map as $column => $column_name) {
                if (isset($item[$column])) {
                    $tmp[] = ExportHelper::itemFormat($column, $item, $row_data->merge_dimension);
                } else {
                    $tmp[] = ''; // 没有的补个空格
                }
            }
            $content .= implode(',', $tmp) . PHP_EOL;
        }
        return [$header_map, $content, 0];
    }

    private function attendanceRecord($row_data)
    {
        $param = json_decode($row_data->param, true);
        $param['page'] = 1;
        $param['rows'] = 10000;

        $info = (new AttendancePersonalRecordParam())->getLevelAndRankIdByUserId($row_data->user_id);

        // 表头
        $header_map = ExportHelper::exportAttendanceRecord();

        $data = (new AttendanceRecordLogic())->exportRecord($param, $info['level'], $info['rank_id'], $info['staff_number']);
        $content = '';
        foreach ($data['list'] as $index => $item) {
            $item = (array)$item;
            $tmp = [];
            foreach ($header_map as $column => $column_name) {
                if (isset($item[$column])) {
                    $tmp[] = ExportHelper::itemFormat($column, $item, $row_data->merge_dimension);
                } else {
                    $tmp[] = ''; // 没有的补个空格
                }
            }
            $content .= implode(',', $tmp) . PHP_EOL;
        }
        return [$header_map, $content, $param['rows']];
    }

    private function attendanceRecordDetail($row_data)
    {
        $param = json_decode($row_data->param, true);
        $param['page'] = 1;
        $param['rows'] = 10000;

        $info = (new AttendancePersonalRecordParam())->getLevelAndRankIdByUserId($row_data->user_id);

        $fix_header_map = ['个人信息', '日期'];

        $date_header_map = [];
        for ($i = 1; $i <= 31; $i++) {
            $date_header_map['day_' . $i] = $i;
        }

        // 表头
        $header_map = array_merge($fix_header_map, $date_header_map);
        array_push($header_map, '合计');

        $new_cols = [
            'clock_in_time'         => '考勤记录',
            'clock_out_time'        => '考勤记录',
            'forget_to_clock_times' => '忘记打卡',
            'late_times'            => '迟到次数',
            'late_min'              => '迟到分钟',
            'remark'                => '考勤情况',
            'shift_name'            => '班期',
        ];

        $data = (new AttendanceRecordLogic())->exportDetail($param, $info['level'], $info['rank_id'], $info['staff_number']);
        $content = '';
        foreach ($data['list'] as $index => $item) {
            $item = (array)$item;

            //每行都加表头
            $tmp = [];
            foreach ($header_map as $header_item) {
                $tmp[] = $header_item; //第二列
            }
            $content .= implode(',', $tmp) . PHP_EOL;

            //考勤记录 忘记打卡 迟到次数 迟到分钟 考勤情况 班期
            foreach ($new_cols as $tmp_field => $tmp_val) {
                $tmp = [];
                //第一列
                if ($tmp_field == 'clock_in_time') {
                    $tmp[] = $item['sbu_name'];
                } elseif ($tmp_field == 'clock_out_time') {
                    $tmp[] = $item['department_name'];
                } elseif ($tmp_field == 'forget_to_clock_times') {
                    $tmp[] = $item['name'];
                } elseif ($tmp_field == 'late_times') {
                    $tmp[] = date('Y年m月', strtotime($item['work_month']));
                } elseif ($tmp_field == 'late_min') {
                    $tmp[] = $item['name_remark'];
                } else {
                    $tmp[] = '';
                }
                //第二列
                $tmp[] = $tmp_val;

                //其他列
                foreach ($date_header_map as $column => $column_name) {
                    if (isset($item['date_list'][$column])) {
                        if (in_array($tmp_field, ['forget_to_clock_times', 'late_times', 'late_min']) && $item['date_list'][$column][$tmp_field] == 0) {
                            $tmp[] = ''; // 没有的补个空格
                        } else {
                            $tmp[] = $item['date_list'][$column][$tmp_field];
                        }
                    } else {
                        $tmp[] = ''; // 没有的补个空格
                    }
                }

                //最后合计列
                if (in_array($tmp_field, ['forget_to_clock_times', 'late_times', 'late_min']) && $item[$tmp_field] > 0) {
                    $tmp[] = $item[$tmp_field];
                } else {
                    $tmp[] = ''; // 没有的补个空格
                }

                $content .= implode(',', $tmp) . PHP_EOL;
            }


            $content .= PHP_EOL;
            $content .= PHP_EOL;
        }
        return [[], $content, $param['rows']];
    }

    public function layeredPayment($row_data)
    {
        $raw_param = json_decode($row_data->param, true);
        $param = new RoleCreateLogParam($raw_param);
        $param->is_export = true;
        $param->setUserPermission($row_data->user_id);

        $data = (new SdkStepLogic())->getLayeredPayment($param);
        // 表头
        $header_map = ExportHelper::exportLayeredPayment();
        $header_map = array_merge($header_map, ExportHelper::exportHeadMap());
        // 表头排序 以dimension排序
        $choose_header = [];
        foreach ($param->dimension as $dimension) {
            $choose_header[$dimension] = $header_map[$dimension] ?? '';
        }

        $list_one = $data['list']->first();
        foreach ($list_one as $index => $item) {
            if (isset($header_map[$index])) {
                $choose_header[$index] = $header_map[$index];
            } else if (strpos($index, "pay_money_") !== false) {
                $choose_header[$index] = ltrim($index, 'pay_money_');
            } else if (strpos($index, "pay_count_") !== false) {
                $choose_header[$index] = ltrim($index, 'pay_count_');
            }
        }
        $content = '';
        foreach ($data['list'] as $index => $item) {
            $item = (array)$item;
            $tmp = [];
            foreach ($choose_header as $column => $column_name) {
                if (isset($item[$column])) {
                    if ($column == 'ori_server_id') {
                        $tmp[] = $item['ori_server_id'] . '-' . $item['ori_server_name'];
                    } else {
                        $tmp[] = ExportHelper::itemFormat($column, $item, $row_data->merge_dimension);
                    }
                } else {
                    $tmp[] = '';
                }
            }
            $content .= implode(',', $tmp) . PHP_EOL;
        }
        return [$choose_header, $content, 0, $data['is_accurate'] ?? 1];
    }

    /**
     * 官网充值
     *
     * @param $row_data
     *
     * @return array
     */
    private function websiteRecharge($row_data)
    {
        $param = new WebsiteRechargeFilterParam(json_decode($row_data->param, true));
        $param->setUserPermission($row_data->user_id);

        $data = (new MarketLogic())->getWebsiteRecharge($param);
        // 表头
        if ($param->aggregation_type !== '聚合') {
            $header = array_merge($param->dimension, ['pay_date', 'num', 'total_pay_money']);
        } else {
            $header = array_merge($param->dimension, ['num', 'total_pay_money']);
        }
        $header_map = ExportHelper::exportWebsiteRecharge();
        $header_format = $this->getHeaderFormat($param, $header, $header_map, $row_data->merge_dimension);

        $content = $this->genCommonData($data['list'], $header, $row_data);
        return [$header_format, $content, $param->limit, $data['is_accurate'] ?? 1];
    }


    /**
     * 团队数据
     *
     * @param $row_data
     *
     * @return array
     */
    private function teamConfigDataList($row_data)
    {

        $param = json_decode($row_data->param, true);
        if ($param['type'] == 'growth_factor') {
            return $this->teamGrowthFactorList($param, $row_data->user_id, $row_data);
        }

        // 任务开始之前加锁，同个时间段只允许一个任务运行
        if (!$this->teamConfigLock()) {
            throw new ExportFileException('其他任务还在运行，需要排队');
        }

        $logic = new TeamConfigLogic();
        $param = new TeamConfigDataParam($param);
        $param->setUserPermission($row_data->user_id);

        // 超管的话把user_id置为0
        $user_id = UserService::isSuperManagerByUserId($row_data->user_id) ? 0 : $row_data->user_id;
        $data = $logic->getDataList($param, $user_id);


        $header = array_merge(['team_config_name'], $param->dimension, $param->target);
        // 表头
        if ($param->aggregation_time !== '聚合') {
            $header = array_merge($header, ['date']);
        }
        $header_map = ExportHelper::exportTeamConfigDataMap();
        $header_format = $this->getHeaderFormat($param, $header, $header_map, $row_data->merge_dimension);

        $content = $this->genCommonData($data['list'], $header, $row_data);

        // 解锁redis
        $this->unTeamConfigLock();
        return [$header_format, $content, 0, $data['is_accurate'] ?? 1];
    }

    private function teamGrowthFactorList($param, $user_id, $row_data)
    {
        $user_permission = (new PermissionLogic())->getDataPermissionByUserId($user_id);
        $data = (new TeamDataGrowthFactorLogic())->getConfigList($param, $user_permission);

        // 表头
        $header = ['team', 'platform', 'root_game_id', 'root_game_name', 'os', 'tdate', 'ios_percent', 'channel_rate', 'multiple',];
        $header_map = ExportHelper::exprotTeamGrowthFactorHeadMap();
        $header_format = $this->getHeaderFormat($param, $header, $header_map, $row_data->merge_dimension);

        $content = '';
        foreach ($data['list'] as $index => $item) {
            $item = (array)$item;
            $tmp = [];
            foreach ($header as $column) {
                if (isset($item[$column])) {
                    $tmp[] = $item[$column];
                } else {
                    $tmp[] = ''; // 没有的补个空格
                }
            }
            $content .= implode(',', $tmp) . PHP_EOL;
        }
        return [$header_format, $content, 0, $data['is_accurate'] ?? 1];
    }


    private function orderList($row_data)
    {
        $param = json_decode($row_data->param, true);
        if ($param['export_type'] && (int)$param['export_type'] === 2) {
            return $this->orderCountList($row_data);
        } else {
            return $this->orderConversionList($row_data);
        }
    }

    /**
     * 订单转化
     *
     * @param $row_data
     *
     * @return array
     */
    private function orderConversionList($row_data)
    {
        $param = new OrderConversionListFilterParam(json_decode($row_data->param, true));
        $param->setUserPermission($row_data->user_id);

        $data = (new MarketLogic())->getOrderConversionList($param);
        $header = $param->dimension;
        // 表头
        if ($param->aggregation_time !== '聚合') {
            $header = array_merge($param->dimension, ['pay_date']);
        }
        $list = $data['list'];
        // 这里要聚合一下
        $list = DimensionTool::groupByDimension($list, $header);
        $res_list = [];
        foreach ($list as $key => $item) {
            $tmp = $item[0];
            $tmp->order_status_id_x_1 = $tmp->order_status_id_x_2 = $tmp->order_status_id_x_3 = $tmp->order_status_id_x_4 =
            $tmp->order_status_id_x_5 = $tmp->order_status_id_x_6 = 0;
            foreach ($item as $value) {
                $tmp->order_status_id_x_1 += $value->order_status_id_1;
                $tmp->order_status_id_x_2 += $value->order_status_id_2;
                $tmp->order_status_id_x_3 += $value->order_status_id_3;
                $tmp->order_status_id_x_4 += $value->order_status_id_4;
                $tmp->order_status_id_x_5 += $value->order_status_id_5;
                $tmp->order_status_id_x_6 += $value->order_status_id_6;
            }
            $res_list[$key] = $tmp;
        }


        $header = array_merge($header, ['order_status_id_x_1', 'order_status_id_x_2', 'order_status_id_x_3', 'order_status_id_x_4', 'order_status_id_x_5', 'order_status_id_x_6']);
        $header_map = ExportHelper::exportOrderConversion();
        $header_format = $this->getHeaderFormat($param, $header, $header_map, $row_data->merge_dimension);

        $content = $this->genCommonData($res_list, $header, $row_data);
        return [$header_format, $content, $param->limit, $data['is_accurate'] ?? 1];
    }

    /**
     * 订单统计
     *
     * @param $row_data
     *
     * @return array
     */
    private function orderCountList($row_data)
    {
        $param = new OrderCountListFilterParam(json_decode($row_data->param, true));
        $param->setUserPermission($row_data->user_id);

        $data = (new MarketLogic())->getOrderCountList($param);
        $header = $param->dimension;
        // 表头
        if ($param->aggregation_time !== '聚合') {
            $header = array_merge($param->dimension, ['pay_date']);
        }
        $list = $data['list'];
        $header = array_merge($header, ['order_count', 'order_pay_count', 'all_uid', 'pay_count', 'total_pay_money', 'order_success_rates', 'uid_success_rates']);
        $header_map = ExportHelper::exportOrderCount();
        $header_format = $this->getHeaderFormat($param, $header, $header_map, $row_data->merge_dimension);

        $content = '';
        foreach ($list as $index => $item) {
            $item = (array)$item;
            $tmp = [];
            foreach ($header as $column) {
                if (isset($item[$column])) {
                    if ($column === 'pay_way_id') {
                        $tmp[] = $item[$column] . '-' . $item['pay_way_name'];
                    } else {
                        $tmp[] = ExportHelper::itemFormat($column, $item, $row_data->merge_dimension);
                    }
                } else {
                    $tmp[] = ''; // 没有的补个空格
                }
            }
            $content .= implode(',', $tmp) . PHP_EOL;
        }
        return [$header_format, $content, $param->limit, $data['is_accurate'] ?? 1];
    }

    /**
     * 转账校验
     *
     * @param $row_data
     *
     * @return array
     */
    private function transactionCheck($row_data)
    {
        $param = new FundBalanceLogParam(json_decode($row_data->param, true));
        $param->is_export = true;
        $list = (new FinanceLogic())->transactionCheck($param);
        $header = ['type', 'create_time_in', 'create_time_out', 'cash_in', 'cash_out', 'grant_in', 'grant_out', 'return_goods_in', 'return_goods_out', 'account_id_in', 'account_id_out', 'company_out', 'company_in', 'platform_out', 'platform_in', 'agency_full_name_in', 'agency_full_name_out'];
        $header_map = ExportHelper::exportTransactionCheck();
        $header_format = $this->getHeaderFormat($param, $header, $header_map, $row_data->merge_dimension);
        // $content = $this->genCommonData($list, $header, $row_data);
        $content = '';
        foreach ($list as $index => $item) {
            $item = (array)$item;
            $tmp = [];
            foreach ($header as $column) {
                if (isset($item[$column])) {
                    if ($column === 'account_id_in' || $column === 'account_id_out') {
                        $tmp[] = '="' . trim($item[$column]) . '"';
                    } else {
                        $tmp[] = ExportHelper::itemFormat($column, $item, $row_data->merge_dimension);
                    }
                } else {
                    $tmp[] = ''; // 没有的补个空格
                }
            }
            $content .= implode(',', $tmp) . PHP_EOL;
        }

        return [$header_format, $content, 50000, $data['is_accurate'] ?? 1];
    }

    /**
     * 导出返货
     *
     * @param $row_data
     *
     * @return array
     */
    private function returnGoodsRecord($row_data)
    {
        $param = new FundBalanceLogParam(json_decode($row_data->param, true));
        $param->is_export = true;
        $param->setUserPermission($row_data->user_id);
        $data = (new FinanceLogic())->getReturnGoodsWithCompanyAgency($param);
        $list = $data['list'];
        $header = ['platform', 'account_id', 'company', 'agency_full_name', 'media_type', 'date'];
        switch ($param->media_type) {
            case MediaType::TOUTIAO:
                $header = array_merge($header, ['collection_amount', 'activity_name', 'due_date']);
                break;
            case MediaType::TENCENT:
                $header = array_merge($header, ['external_bill_no', 'description', 'amount']);
                break;
        }
        $header_map = ExportHelper::exportReturnGoods();
        $header_format = $this->getHeaderFormat($param, $header, $header_map, $row_data->merge_dimension);
        $content = '';
        foreach ($list as $index => $item) {
            $item = (array)$item;
            $tmp = [];
            foreach ($header as $column) {
                if (isset($item[$column])) {
                    if ($column === 'account_id') {
                        $tmp[] = '="' . trim($item[$column]) . '"';
                    } else if ($column === 'media_type') {
                        $tmp[] = MediaType::MEDIA_TYPE_MAP[$item[$column]];
                    } else {
                        $tmp[] = ExportHelper::itemFormat($column, $item, $row_data->merge_dimension);
                    }
                } else {
                    $tmp[] = ''; // 没有的补个空格
                }
            }
            $content .= implode(',', $tmp) . PHP_EOL;
        }

        return [$header_format, $content, $param->rows, $data['is_accurate'] ?? 1];
    }

    // 工时确认
    private function workflowWorkHour($row_data)
    {
        $param = json_decode($row_data->param, true);
        $param['page'] = 1;
        $param['rows'] = 10000;

        $header_map = ExportHelper::exportWorkflowWorkHour();

        $data = (new FAWorkflowWorkHourModel())->getList($param['page'], $param['rows'], $param);
        (new WorkflowLogic())->getRootGameNameAndCliqueName($data['list']);

        $content = '';
        foreach ($data['list'] as $index => $item) {
            $item = (array)$item;
            $tmp = [];
            foreach ($header_map as $column => $column_name) {
                if (isset($item[$column])) {
                    $val = $item[$column];
                    if ($column === 'content') {
                        $val = str_replace(["\n", "\r", "\r\n"], [" ", " ", " "], $val);
                    } elseif ($column === 'idcard') {
                        $val = "'" . $val;
                    } elseif ($column === 'work_month') {
                        $val = date("Y年m月", strtotime($val));
                    }
                    $tmp[] = $val;
                } else {
                    $tmp[] = ''; // 没有的补个空格
                }
            }
            $content .= implode(',', $tmp) . PHP_EOL;
        }
        return [$header_map, $content, $param['rows']];
    }

    // 出入库
    private function erpStock($row_data)
    {
        $param = json_decode($row_data->param, true);
        if ($param['type'] == 1) {
            $param = new StorageListParam($param);
            $param->rows = 200000;
            $param->setUserPermission($row_data->user_id);
            $data = (new \App\Logic\ERP\FinanceLogic())->getStorageList($param);
            $list = $data['list'];
            $header_map = ExportHelper::exportErpStockIn();
        } else {
            $param = new WarehouseListParam($param);
            $param->rows = 200000;
            $param->setUserPermission($row_data->user_id);
            $data = (new \App\Logic\ERP\FinanceLogic())->getWarehouseList($param);
            $list = $data['list'];
            $header_map = ExportHelper::exportErpStockOut();
        }

        $content = '';
        foreach ($list as $index => $item) {
            $item = (array)$item;
            $tmp = [];
            foreach ($header_map as $column => $column_name) {
                if (isset($item[$column])) {
                    $val = $item[$column];
                    if ($column === 'id') {
                        $val .= ' ';
                    } elseif ($column === 'is_frozen') {
                        $val = (int)$val === 1 ? '冻结' : '未冻结';
                    } elseif ($column === 'invoice_state') {
                        $val = (int)$val === -1 ? '已开票' : '未开票';
                    } elseif ($column === 'status') {
                        $val = (int)$val === -1 ? '部分收款' : ((int)$val === -2 ? '全部收款' : '未收款');
                    } elseif ($column === 'collection_status') {
                        $val = (int)$val === -1 ? '部分收款' : ((int)$val === -2 ? '全部收款' : '未收款');
                    }
                    $tmp[] = $val;
                } else {
                    $tmp[] = ''; // 没有的补个空格
                }
            }
            $content .= implode(',', $tmp) . PHP_EOL;
        }
        return [$header_map, $content, $param->rows];
    }

    // 产品
    private function erpProduct($row_data)
    {
        $param = new ProductListParam(json_decode($row_data->param, true));
        $param->setUserPermission($row_data->user_id);
        $param->rows = 200000;
        $data = (new \App\Logic\ERP\FinanceLogic())->getProductList($param);
        $list = $data['list'];
        $header_map = ExportHelper::exportErpProduct();

        $content = '';
        foreach ($list as $index => $item) {
            $item = (array)$item;
            $tmp = [];
            foreach ($header_map as $column => $column_name) {
                if (isset($item[$column])) {
                    $val = $item[$column];
                    if ($column === 'product_id') {
                        $val .= ' ';
                    }
                    $tmp[] = $val;
                } else {
                    $tmp[] = ''; // 没有的补个空格
                }
            }
            $content .= implode(',', $tmp) . PHP_EOL;
        }
        return [$header_map, $content, $param->rows];
    }

    // 仓库
    private function erpStockAddress($row_data)
    {
        $param = new StorehouseListParam(json_decode($row_data->param, true));
        $param->setUserPermission($row_data->user_id);
        $param->rows = 200000;
        $data = (new \App\Logic\ERP\FinanceLogic())->getStorehouseList($param);
        $list = $data['list'];
        $header_map = ExportHelper::exportErpStockAddress();

        $content = '';
        foreach ($list as $index => $item) {
            $item = (array)$item;
            $tmp = [];
            foreach ($header_map as $column => $column_name) {
                if (isset($item[$column])) {
                    $val = $item[$column];
                    if ($column === 'storehouse_id') {
                        $val .= ' ';
                    }
                    $tmp[] = $val;
                } else {
                    $tmp[] = ''; // 没有的补个空格
                }
            }
            $content .= implode(',', $tmp) . PHP_EOL;
        }
        return [$header_map, $content, $param->rows];
    }

    // 客户
    private function erpCustomer($row_data)
    {
        $param = new CustomerListParam(json_decode($row_data->param, true));
        $param->setUserPermission($row_data->user_id);
        $param->rows = 200000;
        $data = (new \App\Logic\ERP\FinanceLogic())->getCustomerList($param);
        $list = $data['list'];
        $header_map = ExportHelper::exportErpCustomer();

        $content = '';
        foreach ($list as $index => $item) {
            $item = (array)$item;
            $tmp = [];
            foreach ($header_map as $column => $column_name) {
                if (isset($item[$column])) {
                    $val = $item[$column];
                    if ($column === 'customer_id') {
                        $val .= ' ';
                    }
                    $tmp[] = $val;
                } else {
                    $tmp[] = ''; // 没有的补个空格
                }
            }
            $content .= implode(',', $tmp) . PHP_EOL;
        }
        return [$header_map, $content, $param->rows];
    }

    // 供应商
    private function erpSupplier($row_data)
    {
        $param = new VendorListParam(json_decode($row_data->param, true));
        $param->setUserPermission($row_data->user_id);
        $param->rows = 200000;
        $data = (new \App\Logic\ERP\FinanceLogic())->getVendorList($param);
        $list = $data['list'];
        $header_map = ExportHelper::exportErpSupplier();

        $content = '';
        foreach ($list as $index => $item) {
            $item = (array)$item;
            $tmp = [];
            foreach ($header_map as $column => $column_name) {
                if (isset($item[$column])) {
                    $val = $item[$column];
                    if ($column === 'vendor_id') {
                        $val .= ' ';
                    }
                    $tmp[] = $val;
                } else {
                    $tmp[] = ''; // 没有的补个空格
                }
            }
            $content .= implode(',', $tmp) . PHP_EOL;
        }
        return [$header_map, $content, $param->rows];
    }

    // 组合
    private function erpCombination($row_data)
    {
        $param = new CombinationListParam(json_decode($row_data->param, true));
        $param->setUserPermission($row_data->user_id);
        $param->rows = 200000;
        $data = (new \App\Logic\ERP\FinanceLogic())->getCombinationList($param);
        $list = $data['list'];
        $header_map = ExportHelper::exportErpCombination();

        $content = '';
        foreach ($list as $index => $item) {
            $item = (array)$item;
            $tmp = [];
            foreach ($header_map as $column => $column_name) {
                if (isset($item[$column])) {
                    $val = $item[$column];
                    if ($column === 'combination_id') {
                        $val .= ' ';
                    }
                    $tmp[] = $val;
                } else {
                    $tmp[] = ''; // 没有的补个空格
                }
            }
            $content .= implode(',', $tmp) . PHP_EOL;
        }
        return [$header_map, $content, $param->rows];
    }

    // 耗材
    private function erpConsumables($row_data)
    {
        $param = new ConsumablesListParam(json_decode($row_data->param, true));
        $param->setUserPermission($row_data->user_id);
        $param->rows = 200000;
        $data = (new \App\Logic\ERP\FinanceLogic())->getConsumablesList($param);
        $list = $data['list'];
        $header_map = ExportHelper::exportErpConsumables();

        $content = '';
        foreach ($list as $index => $item) {
            $item = (array)$item;
            $tmp = [];
            foreach ($header_map as $column => $column_name) {
                if (isset($item[$column])) {
                    $val = $item[$column];
                    $tmp[] = $val;
                } else {
                    $tmp[] = ''; // 没有的补个空格
                }
            }
            $content .= implode(',', $tmp) . PHP_EOL;
        }
        return [$header_map, $content, $param->rows];
    }

    /**
     * 处理付费预估（自然月）
     *
     * @param $row_data
     *
     * @return array
     * @throws \ReflectionException
     */
    private function payEstimateMonth($row_data)
    {

        $param = new PayEstimateParam(json_decode($row_data->param, true));
        $param->setUserPermission($row_data->user_id);

        $service = new PayEstimateLogic();

        // 表头
        $header = array_merge($param->dimension, $param->fixedHeader(), $param->header_day_list);
        // 用付费情况的导出映射就可以了
        $header_map = ExportHelper::exportPaymentHeadMap();
        $header_format = $this->getHeaderFormat($param, $header, $header_map, $row_data->merge_dimension, '月');

        $data = $service->getPayEstimateMonthList($param, 0);
        $list = $data['list'];
        array_unshift($list, $data['sum']); // 合计塞上来

        $key_prefix = $param->switchColumnKey();
        $content = '';
        foreach ($list as $index => $item) {
            $item = (array)$item;
            $tmp = [];
            foreach ($header as $column) {
                if (isset($item[$column])) {
                    $tmp[] = ExportHelper::itemFormat($column, $item, $row_data->merge_dimension);
                } else {
                    // xx日
                    $column_key = $key_prefix . $column;
                    if (isset($item[$column_key])) {
                        $tmp[] = $item[$column_key];
                    } else {
                        // 没有的补0
                        $tmp[] = '0';
                    }
                }
            }
            $content .= implode(',', $tmp) . PHP_EOL;
        }

        return [$header_format, $content, $param->limit, $data['is_accurate'] ?? 1];

    }


    /**
     * 内部账号资源管理
     *
     * @param $row_data
     *
     * @return array
     * @throws \Exception
     */
    private function internalUser($row_data)
    {
        $param = new InternalUserListParam(json_decode($row_data->param, true));
        $param->rows = ********;
        $param->setUserPermission($row_data->user_id);
        $logic = new MarketLogic();
        $data = $logic->getInternalUserList($param);
        $list = $data['list']->toArray();

        $header_map = ExportHelper::exportInternalUser();

        $content = '';
        foreach ($list as $index => $item) {
            $item = (array)$item;
            $tmp = [];
            foreach ($header_map as $column => $column_name) {
                if (isset($item[$column])) {
                    $val = $item[$column];
                    if ($column === 'status') {
                        $val = (int)$val === 1 ? '正常' : '封禁';
                    }
                    $tmp[] = $val;
                } else {
                    $tmp[] = ''; // 没有的补个空格
                }
            }
            $content .= implode(',', $tmp) . PHP_EOL;
        }
        return [$header_map, $content, $param->rows];
    }

    // 项目确认
    private function workflowProject($row_data)
    {
        $param = json_decode($row_data->param, true);
        $param['page'] = 1;
        $param['rows'] = 10000;

        $header_map = ExportHelper::exportWorkflowProject();

        $data = (new FAWorkflowProjectModel())->getList($param['page'], $param['rows'], $param);
        (new WorkflowLogic())->getRootGameNameAndCliqueName($data['list']);

        $content = '';
        foreach ($data['list'] as $index => $item) {
            $item = (array)$item;
            $tmp = [];
            foreach ($header_map as $column => $column_name) {
                if (isset($item[$column])) {
                    if (in_array($column, ['start_month', 'end_month'])) {
                        $tmp[] = date("Y年m月", strtotime($item[$column]));
                    } elseif ($column === 'node_id') {
                        $tmp[] = $item[$column] === 1101 ? '创建' : '已确认';
                    } else {
                        $tmp[] = $item[$column];
                    }

                } else {
                    $tmp[] = ''; // 没有的补个空格
                }
            }
            $content .= implode(',', $tmp) . PHP_EOL;
        }
        return [$header_map, $content, $param['rows']];
    }

    // 年假配置
    private function attendanceAnnualLeave($row_data)
    {
        $param = json_decode($row_data->param, true);
        $param['page'] = 1;
        $param['rows'] = 10000;

        $header_map = [
            'sbu_id'           => '事业部',
            'department_name'  => '部门',
            'staff_name'       => '姓名',
            'staff_number'     => '员工编号',
            'year'             => '年份',
            'days'             => '年假天数',
            'left_days'        => '剩余年假天数',
            'start_date'       => '起始日期',
            'end_date'         => '结束日期',
            'leave_date'       => '休假日期',
            'error_leave_date' => '异常休假日期',
        ];

        $data = (new FAAttendanceAnnualLeaveModel())->getList($param['page'], $param['rows'], $param);
        $data['list']->map(function ($item) {
            $leave_date = $error_leave_date = [];
            try {
                $leave_date = \GuzzleHttp\json_decode($item->leave_date, true);
                $error_leave_date = \GuzzleHttp\json_decode($item->error_leave_date, true);
            } catch (\Throwable $exception) {
            }

            $string = [];
            foreach ($leave_date as $tmp_item) {
                $string[] = $tmp_item[0] . '【' . $tmp_item[1] . '】';
            }
            $item->leave_date = implode('; ', $string);

            $string = [];
            foreach ($error_leave_date as $tmp_item) {
                $string[] = $tmp_item[0] . '【' . $tmp_item[1] . '】';
            }
            $item->error_leave_date = implode('; ', $string);

            return $item;
        });


        $content = '';
        foreach ($data['list'] as $index => $item) {
            $item = (array)$item;
            $tmp = [];
            foreach ($header_map as $column => $column_name) {
                if (isset($item[$column])) {
                    $tmp[] = $item[$column];
                } else {
                    $tmp[] = ''; // 没有的补个空格
                }
            }
            $content .= implode(',', $tmp) . PHP_EOL;
        }
        return [$header_map, $content, $param['rows']];
    }


    /**
     * 获取执行锁，用来判断当前任务是否正在执行。
     * 团队数据专用锁
     *
     *
     * @return bool  获锁成功返回true 否则返回false
     */
    private function teamConfigLock()
    {
        // 默认的过期时间为1天
        $key = 'team_config_running_export_task';
        if (RedisCache::getInstance()->set($key, '1', ['NX', 'EX' => 86400])) {
            Helpers::getLogger('sync-export')->info('成功获取团队数据执行锁');
            return true;
        } else {
            Helpers::getLogger('sync-export')->info('获取团队数据执行锁失败');
            return false;
        }
    }

    /**
     * 解锁
     * 团队数据专用
     *
     */
    private function unTeamConfigLock()
    {
        $key = 'team_config_running_export_task';
        $result = RedisCache::getInstance()->del($key);
        Helpers::getLogger('sync-export')->info('unlock', ['result' => $result]);
    }

    /**
     * 消耗分摊导出
     *
     * @param $row_data
     *
     * @return array
     */
    private function costShare($row_data)
    {
        $param = json_decode($row_data->param, true);
        if ($param['type'] == 'share_rate') {
            $user_permission = (new PermissionLogic())->getDataPermissionByUserId($row_data->user_id);
            $param = new CostShareParam($param);
            return $this->shareRate($param, $param->keyword, $user_permission, $param->start_date, $param->end_date, $row_data);
        }
        $param = new CostShareParam($param);
        $param->setUserPermission($row_data->user_id);
        $data = (new CostShareLogic())->getCostShareList($param);

        $list = $data['list'];
        // 表头
        if ($param->aggregation_type !== '聚合') {
            $header = array_merge($param->dimension, ['game_reg_date']);
        } else {
            $header = $param->dimension;
        }
        // 指标
        $header = array_merge($header, ['per_cost_money', 'per_ori_money']);

        $header_map = ExportHelper::exportCostShareHeadMap();
        $header_format = $this->getHeaderFormat($param, $header, $header_map, $row_data->merge_dimension);

        $content = $this->genCommonData($list, $header, $row_data);
        return [$header_format, $content];
    }

    private function shareRate($param, $keyword, $permission, $start_date, $end_date, $row_data)
    {
        $list = (new CostShareLogic())->getShareRateList($keyword, $permission, $start_date, $end_date);

        // 表头
        $header = ['company', 'platform', 'game_id', 'tdate', 'money', 'percent', 'operator'];
        $header_map = ExportHelper::exportShareRateHeadMap();
        $header_format = $this->getHeaderFormat($param, $header, $header_map, $row_data->merge_dimension);

        $content = $this->genCommonData($list, $header, $row_data);
        return [$header_format, $content];
    }

    private function agentRebate($row_data)
    {
        $param = json_decode($row_data->param, true);
        $param = new AgentRebateListParam($param);
        ['agent_permission' => $agent_permission] = (new PermissionLogic())->getDataPermissionByUserId($row_data->user_id);
        $param->agent_permission = $agent_permission;
        $logic = new AgentRebateLogic();
        $header = [];
        $result = [];
        switch ($param->type) {
            case 1:
                $result = $logic->getRebateList($param, -1)['list'];
                break;
            case 2:
                $result = $logic->getAgentGroupRebateList($param, -1)['list'];
                break;
            case 3:
                $result = $logic->getTrueRebateList($param, -1)['list'];
                $header = ['platform', 'agent_id', 'agent_group_id', 'true_rebate', 'rebate_type', 'rebate_start_time', 'rebate_end_time', 'update_time', 'creator', 'operator'];
                break;
            case 4:
                $result = $logic->getAgentGroupTrueRebateList($param, -1)['list'];
                break;
            case 5:
                $result = $logic->getAccountRebateList($param, -1)['list'];
                break;
        }

        $header_format = $this->getHeaderFormat($param, $header, ExportHelper::exprotAgentRebateHeadMap(), $row_data->merge_dimension);
        $content = $this->genCommonData($result, $header, $row_data);
        return [$header_format, $content];
    }

    private function adInspireCost($row_data)
    {
        $param = json_decode($row_data->param, true);
        $param = new AdInspireCostParam($param);
        $param->setUserPermission($row_data->user_id);
        $param->handleTarget();

        $result = (new AdInspireCostLogic())->getList($param);

        $header_map = ExportHelper::exportAdInspireCostHeadMap();

        // 表头
        if ($param->aggregation_type !== '聚合') {
            $header = array_merge($param->dimension, ['tdate_1'], $param->target);
            $header_map = array_merge($header_map, ['tdate_1' => '日期']);
        } else {
            $header = array_merge($param->dimension, $param->target);
        }

        $header_format = $this->getHeaderFormat($param, $header, $header_map, $row_data->merge_dimension);
        $content = $this->genCommonData($result['list'], $header, $row_data);

        return [$header_format, $content];
    }

    private function getSettlementList($row_data)
    {
        $param = json_decode($row_data->param, true);
        $param = new SiteSettlementListParam($param);
        $param->setUserPermission($row_data->user_id);


        $logic = new OuterLogic();
        $result = $logic->getSettlementList($param);

        $result['sum']->log_date = '合计';

        $header_map = ExportHelper::exportSettlementHeadMap();

        // 表头
        $header = ['log_date', 'our_company', 'platform', 'agent_id', 'agent_group_name', 'site_id', 'game_id', 'is_channel', 'os', 'agent_leader', 'agent_leader_group_name',
                   'settlement_type', 'tax_rate', 'channel_fee_rate', 'bank_holder', 'settlement_base_value', 'deduction_value', 'log_true_value', 'log_value', 'settlement_money'];

        $this->unshift($result['list'], $result['sum']); // 合计塞上来
        $header_format = $this->getHeaderFormat($param, $header, $header_map, $row_data->merge_dimension);
        $content = $this->genCommonData($result['list'], $header, $row_data);

        return [$header_format, $content];
    }

    private function getUserProfit($row_data)
    {
        $param = json_decode($row_data->param, true);
        $param = new UserProfileListFilterParam($param);
        $param->setUserPermission($row_data->user_id);


        $logic = new UserProfileLogic();
        $result = $logic->getUserProfileList($param);


        $header_map = ExportHelper::exportUserProfitHeadMap();


        // 表头
        $header = ['total_cnt', 'id_card_cnt', 'male_cnt', 'female_cnt', 'bind_rate', 'male_fate', 'female_rate', 'unbind_number', 'unbind_rate'];

        $this->unshift($result['list'], $result['sum']); // 合计塞上来
        $header_format = $this->getHeaderFormat($param, $header, $header_map, $row_data->merge_dimension);
        $content = $this->genCommonData($result['list'], $header, $row_data);

        return [$header_format, $content];
    }

    /**
     * 导出汇总的结算单
     *
     * @param $row_data
     * @return array
     */
    public function getSettlementSummarizeList($row_data)
    {
        $param = json_decode($row_data->param, true);
        $settle_ids = explode(',', $param['settle_ids']);
        $settlement_model = new SiteSettlementModel();
        $list = $settlement_model->getListByIds($settle_ids, SiteSettlementModel::SETTLED, true);
        if (count($settle_ids) !== $list->count()) {
            throw new AppException('传入的id错误，其中有未结算id');
        }

        // 判断一下参数
        if (isset($param['export_type']) && $param['export_type'] == 2) {
            return $this->getSettlementListByNoAggregation($list, $row_data, $param);
        }

        $agent_model = new AgentModel();

        // 汇总导出的site列表的各种参数
        $summarize_data_list = [];
        $summarize_min_time = time();
        $summarize_max_time = 0;

        // 按平台+渠道分
        $file_dimension_list = $list->groupBy(function ($item) {
            return $item->platform . '|' . $item->agent_id;
        });
        $map = (new OuterLogic())->getProjectTeamMap();
        foreach ($file_dimension_list as $platform_agent_key => $item_list) {
            // 获取渠道信息
            $platform_agent = explode('|', $platform_agent_key);
            $agent_info = $agent_model->getDataByPlatformAgentId($platform_agent[0], $platform_agent[1]);
            if (!$agent_info) {
                throw new AppException('渠道信息不存在，数据异常');
            }
            /**
             * @var $item_list Collection
             */
            $result = $item_list
                ->sort(function ($a, $b) {
                    // 先按site_id排序
                    if ($a->site_id > $b->site_id) return -1;
                    if ($a->site_id < $b->site_id) return 1;

                    // 如果site_id相同，再按log_date排序
                    return strcmp($a->log_date, $b->log_date);
                })
                ->values() // 重置键名确保顺序正确
                ->reduce(function ($carry, $item) use ($agent_info, &$summarize_max_time, &$summarize_min_time, $map) {
                    // 总的时间周期
                    $time = strtotime($item->log_date);
                    $summarize_max_time = max($summarize_max_time, $time);
                    $summarize_min_time = min($summarize_min_time, $time);

                    // 特殊处理一下our_company
                    if ($item->agent_group_name == '短信') {
                        $our_company = BillTemplate::PLATFORM_MAP['DX']['invoice'];
                    } else if (in_array($agent_info->agent_id, BillTemplate::GZTW_AGENT_ID_LIST) && $agent_info->platform === 'TW') {
                        $our_company = BillTemplate::PLATFORM_MAP['GZTW']['invoice'] ?? '';
                    } else {
                        $our_company = $item->our_company;
                    }

                    // 首次迭代初始化第一个分组
                    if (is_null($carry['current'])) {
                        $carry['current'] = [
                            'platform'                  => $item->platform,
                            'site_id'                   => $item->site_id,
                            'settlement_base_value'     => $item->settlement_base_value,
                            'channel_fee_rate'          => $item->channel_fee_rate,
                            'tax_rate'                  => $item->tax_rate,
                            'start_date'                => $item->log_date,
                            'end_date'                  => $item->log_date,
                            'site_name'                 => $item->site_name,
                            'agent_id'                  => $item->agent_id,
                            'agent_leader'              => $item->agent_leader,
                            'agent_name'                => $item->agent_name,
                            'agent_group_name'          => $item->agent_group_name,
                            'operator'                  => $item->operator,
                            'project_team_name'         => $map[$item->project_team_id]->project_team ?? '',
                            'agent_leader_group_name'   => $item->agent_leader_group_name,
                            'game_id'                   => $item->game_id,
                            'game_name'                 => $item->game_name,
                            'is_channel'                => $item->is_channel,
                            'os'                        => $item->os,
                            'log_value'                 => $item->log_value,
                            'settlement_money'          => $item->settlement_money,
                            'live_order_ids'            => $item->live_order_ids,
                            'exclude_channel_fee_money' => $item->exclude_channel_fee_money,
                            'site_id_name'              => $item->site_id . '-' . $item->site_name,
                            'channel_fee'               => $item->log_value * $item->channel_fee_rate,
                            'statistical_type'          => $agent_info->statistic_caliber,
                            'other_company'             => $item->bank_holder,
                            'our_company'               => $our_company,
                            'time'                      => date('n.j', strtotime($item->log_date)) . ' - ' . date('n.j', strtotime($item->log_date)),

                        ];

                        return $carry;
                    }

                    // 检查分组条件
                    $isSameGroup =
                        $carry['current']['site_id'] === $item->site_id &&
                        $carry['current']['settlement_base_value'] === $item->settlement_base_value &&
                        $carry['current']['channel_fee_rate'] === $item->channel_fee_rate &&
                        $carry['current']['tax_rate'] === $item->tax_rate;

//                    // 检查日期连续性（用 Carbon 处理跨月/年）
//                    $isConsecutive = Carbon::parse($carry['current']['end_date'])
//                        ->addDay()
//                        ->isSameDay($item->log_date);

                    // 满足条件时扩展当前分组
                    if ($isSameGroup) {
                        $carry['current']['end_date'] = $item->log_date;

                        // 聚合字段计算
                        $channel_fee_tmp = $item->log_value * $item->channel_fee_rate;
                        $carry['current']['channel_fee'] += $channel_fee_tmp;
                        $carry['current']['log_value'] += $item->log_value;
                        $carry['current']['settlement_money'] += $item->settlement_money;
                        $carry['current']['exclude_channel_fee_money'] += $item->exclude_channel_fee_money;
                        $carry['current']['time'] = date('n.j', strtotime($carry['current']['start_date'])) . ' - ' . date('n.j', strtotime($carry['current']['end_date']));
                        $carry['current']['live_order_ids'] = $carry['current']['live_order_ids'] . ',' . $item->live_order_ids;
                    } else {
                        // 保存当前分组并创建新分组
                        $carry['result'][] = $carry['current'];
                        $carry['current'] = [
                            'platform'                  => $item->platform,
                            'site_id'                   => $item->site_id,
                            'settlement_base_value'     => $item->settlement_base_value,
                            'channel_fee_rate'          => $item->channel_fee_rate,
                            'tax_rate'                  => $item->tax_rate,
                            'start_date'                => $item->log_date,
                            'end_date'                  => $item->log_date,
                            'site_name'                 => $item->site_name,
                            'agent_leader'              => $item->agent_leader,
                            'agent_id'                  => $item->agent_id,
                            'agent_name'                => $item->agent_name,
                            'agent_group_name'          => $item->agent_group_name,
                            'operator'                  => $item->operator,
                            'project_team_name'         => $map[$item->project_team_id]->project_team ?? '',
                            'agent_leader_group_name'   => $item->agent_leader_group_name,
                            'game_id'                   => $item->game_id,
                            'game_name'                 => $item->game_name,
                            'is_channel'                => $item->is_channel,
                            'os'                        => $item->os,
                            'log_value'                 => $item->log_value,
                            'settlement_money'          => $item->settlement_money,
                            'live_order_ids'            => $item->live_order_ids,
                            'exclude_channel_fee_money' => $item->exclude_channel_fee_money,
                            'site_id_name'              => $item->site_id . '-' . $item->site_name,
                            'channel_fee'               => $item->log_value * $item->channel_fee_rate,
                            'statistical_type'          => $agent_info->statistic_caliber,
                            'other_company'             => $item->bank_holder,
                            'our_company'               => $our_company,
                            'time'                      => date('n.j', strtotime($item->log_date)) . ' - ' . date('n.j', strtotime($item->log_date)),
                        ];
                    }

                    return $carry;
                }, ['result' => [], 'current' => null]);

            // 处理最后一个分组
            if (!is_null($result['current'])) {
                $result['result'][] = $result['current'];
            }

            $summarize_data_list = array_merge($summarize_data_list, $result['result']);
        }
        $list = (new OuterLogic())->summarizeDataRows(date("Y-m-d", $summarize_min_time), date("Y-m-d", $summarize_max_time), $summarize_data_list);

        $header_map = ExportHelper::exportSumSettlementHeadMap();


        // 表头
        $header = [
            'our_company',
            'other_company',
            'agent_id',
            'agent_group_name',
            'time',
            'site_id_name',
            'game_id_name',
            'is_channel',
            'os',
            'agent_leader',
            'agent_leader_group_name',
            'statistical_type',
            'channel_fee_rate',
            'channel_fee',
            'tax_rate',
            'settlement_base_value',
            'exclude_channel_fee_money',
            'settlement_money',
            'cost_money',
            'log_value',
            'total_pay_money',
            'diff',
            'reg_uid_count',
            'live_order_ids',
            'operator',
            'project_team_name'
        ];

        $header_format = $this->getHeaderFormat($param, $header, $header_map, $row_data->merge_dimension);
        $content = $this->genCommonData($list, $header, $row_data);

        return [$header_format, $content];
    }

    private function getSettlementListByNoAggregation($list, $row_data, $param)
    {
        $map = (new OuterLogic())->getProjectTeamMap();
        $sum = [
            'our_company'               => '合计',
            'log_value'                 => 0,
            'settlement_money'          => 0,
            'exclude_channel_fee_money' => 0,
            'log_true_value'            => 0,
        ];
        foreach ($list as $item) {
            // 合计
            $sum['log_value'] += $item->log_value;
            $sum['exclude_channel_fee_money'] += $item->exclude_channel_fee_money;
            $sum['log_true_value'] += $item->log_true_value;
            $sum['settlement_money'] += $item->settlement_money;


            // 格式化显示
            $item->settlement_money = Math::decimal($item->settlement_money, 2);
            $item->exclude_channel_fee_money = Math::decimal($item->exclude_channel_fee_money, 2);
            $item->is_channel = $item->is_channel == 1 ? '发行' : ($item->is_channel == 0 ? '买量' : '未知');
            $item->project_team_name = $map[$item->project_team_id]->project_team ?? '';
        }


        $sum['settlement_money'] = Math::decimal($sum['settlement_money'], 2);
        $sum['exclude_channel_fee_money'] = Math::decimal($sum['exclude_channel_fee_money'], 2);
        $sum['log_value'] = Math::decimal($sum['log_value'], 2);
        $sum['log_true_value'] = Math::decimal($sum['log_true_value'], 2);


        $header_map = ExportHelper::exportSettlementHeadMap();

        // 表头
        $header = [
            'our_company', 'log_date', 'platform', 'agent_id', 'agent_group_name', 'site_id', 'contract_game_name', 'game_id', 'is_channel', 'os', 'agent_leader', 'agent_leader_group_name',
            'settlement_type', 'tax_rate', 'channel_fee_rate', 'bank_holder', 'settlement_base_value', 'deduction_value', 'log_true_value', 'log_value',
            'exclude_channel_fee_money', 'settlement_money', 'live_order_ids', 'operator', 'project_team_name'
        ];

        $this->unshift($list, $sum); // 合计塞上来
        $header_format = $this->getHeaderFormat($param, $header, $header_map, $row_data->merge_dimension);
        $content = $this->genCommonData($list, $header, $row_data);

        return [$header_format, $content];
    }

    private function getEsOperationLog($row_data)
    {
        $param = json_decode($row_data->param, true);
        $param['page'] = 1;
        $param['rows'] = 10000;// ES封顶1w
        if ($row_data->user_id == 151) {
            $param['rows'] = 100000;
        }

        $header_map = ExportHelper::exportEsOperationLog();

        $log_param = new LogSearchParam($param);

        $data = (new LogEsLogic())->getList($log_param, true);

        $content = '';
        foreach ($data as $index => $item) {
            $item = (array)$item;
            $tmp = [];
            foreach ($header_map as $column => $column_name) {
                if (isset($item[$column])) {
                    if ($column == 'request_message') {
                        $tmp[] = '"' . $item[$column] . '"';
                    } else {
                        $tmp[] = $item[$column];
                    }
                } else {
                    $tmp[] = ''; // 没有的补个空格
                }
            }
            $content .= implode(',', $tmp) . PHP_EOL;
        }
        return [$header_map, $content, $param['rows']];
    }

    /**
     * 数据管理-产品利润
     * @param $row_data
     *
     * @return array
     */
    private function profitStatement($row_data)
    {
        $param = new ProfitStatementListParam(json_decode($row_data->param, true));
        $param->setUserPermission($row_data->user_id);
        $logic = new ProfitStatementLogic();

        // 表头
        $header = array_merge($param->dimension, ['tdate', 'run_date'], $param->target_raw);
        $header_map = ExportHelper::exportProfitStatementHeadMap();
        $header_format = $this->getHeaderFormat($param, $header, $header_map, $row_data->merge_dimension);

        $data = $logic->profitStatementList($param);
        $this->unshift($data['list'], $data['sum']); // 合计塞上来

        $content = '';
        foreach ($data['list'] as $index => $item) {
            $item = (array)$item;
            $tmp = [];
            foreach ($header as $column) {
                if (isset($item[$column])) {
                    $tmp[] = ExportHelper::itemFormat($column, $item, $row_data->merge_dimension);
                } else {
                    $tmp[] = ''; // 没有的补个空格
                }
            }
            $content .= implode(',', $tmp) . PHP_EOL;
        }

        return [$header_format, $content, $param->limit, $data['is_accurate'] ?? 1];

    }

    /**
     * 汇总导出的文件
     *
     * @param string $dir
     * @param $start_time
     * @param $end_time
     * @param $summarize_data_list
     * @return string
     */
    private function summarizeDataRows(string $dir, $start_time, $end_time, $summarize_data_list)
    {
        $logic = new OuterLogic();
        $out_data_list = Collection::make();

        // 按统计口径分 site_id
        $site_list = [];
        foreach ($summarize_data_list as $item) {
            $site_list[$item['statistical_type']][] = $item;
        }

        // 按统计口径去查流水数据
        foreach ($site_list as $statistical_type => $data_list) {
            // 组装查询条件
            $values = [];// site_id的筛选条件
            foreach ($data_list as $item) {
                $values[] = EnvConfig::PLATFORM_MAP[$item['platform']] . '-' . $item['site_id'];
            }
            $dimension_filter = [
                'site' => [
                    'column'    => 'platform-site_id',
                    'comma'     => false,
                    'condition' => "in",
                    'confirm'   => true,
                    'name'      => "广告位ID",
                    'show'      => true,
                    'type'      => "int",
                    'value'     => $values,
                ]
            ];
            $condition = [
                'aggregation_time'  => '按日',
                'deduction'         => 1,
                'dimension'         => ['platform', 'site_id'],
                'start_time'        => $start_time,
                'end_time'          => $end_time,
                'statistic_caliber' => $statistical_type,
                'target'            => ['reg_uid_count', 'total_pay_money'],
                'dimension_filter'  => $dimension_filter,
            ];
            $param = new OuterOverViewListFilterParam($condition);
            // 处理指标
            $param->handleTarget();
            $res = $logic->getOverviewList($param);
            $out_data_list = $out_data_list->merge($res['list']);
        }

        $out_data_list = $out_data_list->groupBy(function ($item) {
            return $item->platform . '|' . $item->site_id;
        });
        // 汇总数据的文件名
        $sum_file_name = $dir . '/CPS结算确认函汇总_' . Container::getSession()->user_id . date("YmdHis") . '.csv';
        $file_handle = fopen($sum_file_name, "w");
        // 写入表头
        fputcsv($file_handle, ['结算主体', '代理', '渠道', '渠道ID', '计费周期', '位置', "广告位ID", "子游戏id", "游戏名",
                               "负责人", "统计口径", "渠道费率", "渠道费", "税费", "分成比例",
                               "结算金额", '总流水金额', "总付费流水（扣量后）", "扣量总付费-结算单流水"]);

        // 写入数据
        foreach ($summarize_data_list as $item) {
            $key = $item['platform'] . "|" . $item['site_id'];
            // 合计数值
            $total_pay_money = $reg_uid_count = 0;
            $site_data_list = $out_data_list[$key] ?? Collection::make();
            /**@var$site_data_list  Collection */
            $site_data_list = $site_data_list->whereBetween('date', [$item['start_date'], $item['end_date']]);

            foreach ($site_data_list as $data) {
                $total_pay_money += $data->total_pay_money;
                $reg_uid_count += $data->reg_uid_count;
            }

            fputcsv($file_handle, [
                $item['our_company'],
                $item['other_company'],
                $item['agent_name'],
                $item['agent_id'],
                $item['time'],
                $item['site_id_name'],
                $item['site_id'],
                $item['game_id'],
                $item['game_name'],
                $item['agent_leader'],
                $item['statistical_type'] == 1 ? "平台" : ($item['statistical_type'] == 2 ? "按子" : "按根"),
                $item['channel_fee_rate'],
                $item['channel_fee'],
                $item['tax_rate'],
                $item['settlement_base_value'],
                $item['settlement_money'],
                $item['log_value'],
                $total_pay_money,
                $total_pay_money - $item['log_value']
            ]);
        }

        fclose($file_handle);

        return $sum_file_name;
    }

    /**
     * 直播成本导出
     *
     * @param $row_data
     *
     * @return array
     */
    private function liveCostReport($row_data)
    {
        $param = new ADLiveCostReportFilterParam(json_decode($row_data->param, true));
        $data = (new ADLiveCostReportLogic())->getReport($param, 1000000);
        $list = $data['list'];

        $header = array_merge($param->dimension, $param->target);
        $header_map = ExportHelper::exportLiveCostReportHeadMap();
        $header_format = $this->getHeaderFormat($param, $header, $header_map, $row_data->merge_dimension);

        $content = $this->genLiveCostReportCommonData($list, $header, $row_data);

        return [$header_format, $content, 1000000, $data['is_accurate'] ?? 1];
    }

    private function genLiveCostReportCommonData($list, $header, $row_data)
    {
        $to_string_target = [
            'live_account_id', 'agency_id', 'live_order_id', 'live_demand_id',
            'live_author_id', 'alipay_order_no', 'trade_no', 'payee_bank_card_no',
        ];
        $content = '';
        foreach ($list as $index => $item) {
            $item = (array)$item;
            $tmp = [];
            foreach ($header as $column) {
                if (isset($item[$column])) {
                    if (in_array($column, $to_string_target)) {
                        $tmp[] = '="' . trim($item[$column]) . '"';
                    } else if ($column === 'media_type') {
                        $tmp[] = MediaType::MEDIA_TYPE_MAP[$item[$column]];
                    } else if ($column === 'live_time') {
                        $tmp[] = (new ADLiveCostReportLogic())->formatLiveTime($item[$column]);
                    } else {
                        $tmp[] = ExportHelper::itemFormat($column, $item, $row_data->merge_dimension);
                    }
                } else {
                    $tmp[] = ''; // 没有的补个空格
                }
            }
            $content .= implode(',', $tmp) . PHP_EOL;
        }

        return $content;
    }

    private function formatPercentage($input)
    {
        if (strpos($input, '%') !== false) {
            // 去掉百分号并转换为浮点数
            $number = floatval(str_replace('%', '', $input));
            // 保留两位小数并拼接百分号
            return number_format($number, 2) . '%';
        }
        return $input;
    }
}
