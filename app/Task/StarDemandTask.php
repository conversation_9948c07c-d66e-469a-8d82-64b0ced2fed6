<?php


namespace App\Task;

use App\Constant\ADFieldsENToCNMap;
use App\Exception\AppException;
use App\Logic\API\StarDemandLogic;
use App\Model\HttpModel\Bpm\WorkProjectInst\WorkProjectInstModel;
use App\Model\HttpModel\TrinoTask\ToutiaoTaskModel;
use App\Model\RedisModel\StarDemandReceiveModel;
use App\Model\RedisModel\StarDemandToBpmModel;
use App\Model\SqlModel\DataMedia\OdsStarDemandOrderListLogModel;
use App\Model\SqlModel\DataMedia\OdsStarDemandQrcodeReceiveLogModel;
use App\Model\SqlModel\DataMedia\OdsStarSpecialFundLog;
use App\Model\SqlModel\Tanwan\V2DimGameIdModel;
use App\Model\SqlModel\Zeda\UserModel;
use App\Param\ADLiveCostReportFilterParam;
use App\Struct\Input;
use App\Utils\Helpers;
use App\Utils\Math;
use Common\EnvConfig;

class StarDemandTask
{
    const TICK_LIST = [
        [
            'env' => ['production'],
            'desc' => '支付宝返款订单匹配与订单自动过期',
            'interval_ms' => 60000, // 每1分钟运行一次
            /** @uses StarDemandTask::matchTradeLog() */
            'method' => 'matchTradeLog',
        ],
        [
            'env' => ['production'],
            'desc' => '同步游戏APP_NAME到BPM',
            'interval_ms' => 60000 * 30, // 每30分钟运行一次
            /** @uses StarDemandTask::syncGameAppNameToBpm() */
            'method' => 'syncGameAppNameToBpm',
        ],
        [
            'env' => ['production'],
            'desc' => '同步星图订单到BPM',
            'interval_ms' => 60000 * 5, // 每5分钟运行一次
            /** @uses StarDemandTask::updateLiveOrderToBpm() */
            'method' => 'updateLiveOrderToBpm',
        ],
        [
            'env' => ['production'],
            'desc' => '同步订单结算到BPM',
            'interval_ms' => 60000 * 5, // 每5分钟运行一次
            /** @uses StarDemandTask::updateSettlementToBpm() */
            'method' => 'updateSettlementToBpm',
        ],
        [
            'env' => ['production'],
            'desc' => '同步主播费用到BPM',
            'interval_ms' => 60000 * 5, // 每5分钟运行一次
            /** @uses StarDemandTask::updateSpecialFundToBpm() */
            'method' => 'updateSpecialFundToBpm',
        ],
        [
            'env' => ['production'],
            'desc' => '同步根游戏到BPM',
            'interval_ms' => 60000 * 30, // 每30分钟运行一次
            /** @uses StarDemandTask::syncRootGameToBpm() */
            'method' => 'syncRootGameToBpm',
        ],
    ];

    public function matchTradeLog()
    {
        $logger = Helpers::getLogger('star_demand');
        $logger->info("开始执行");

        $receive_redis_model = new StarDemandReceiveModel();
        if (!$receive_redis_model->lock()) {
            $logger->info("星图返款订单匹配正在进行中");
            return false;
        }

        $receive_model = new OdsStarDemandQrcodeReceiveLogModel();
        $list = $receive_model->getMatchTradeNoList();

        $match_trade_no_list = [];
        $match_alipay_order_no_list = [];
        $all_order_ids = [];
        foreach ($list as $item) {
            // 先通过trade_no与支付宝流水的备注匹配
            if ($item->trade_no == $item->trans_memo) {
                if ($item->amount != $item->trans_amount) {
                    $logger->error('返款订单金额不相符', [
                        'data' => (array)$item
                    ]);
                    continue;
                }

                $update_data = [
                    'alipay_order_no' => $item->alipay_order_no,
                    'trans_memo' => $item->trans_memo,
                    'alipay_order_type' => $item->alipay_order_type,
                    'alipay_trans_dt' => $item->alipay_trans_dt,
                    'status' => $receive_model::STATUS_SUCCESS,
                    'update_time' => date('Y-m-d H:i:s')
                ];

                $receive_model->updateByTradeNo($item->trade_no, $update_data);
                $match_trade_no_list[] = $item->trade_no;
                $match_alipay_order_no_list[] = $item->alipay_order_no;

                $order_ids = explode(",", $item->order_list);
                $all_order_ids = array_merge($all_order_ids, $order_ids);
            }
        }

        $match_amount_list = $list
            ->whereNotIn('trade_no', $match_trade_no_list)
            ->whereNotIn('alipay_order_no', $match_alipay_order_no_list);

        $trade_no_group = $match_amount_list->groupBy(['trade_no'])->map(function ($item) {
            return count($item);
        })->toArray();

        $alipay_order_no_group = $match_amount_list->groupBy(['alipay_order_no'])->map(function ($item) {
            return count($item);
        })->toArray();

        // 没匹配上的通过扫描时间和金额匹配
        foreach ($match_amount_list as $item) {
            if ($item->amount != $item->trans_amount) {
                $logger->error('返款订单金额不相符', [
                    'data' => (array)$item
                ]);
                continue;
            }

            if ($trade_no_group[$item->trade_no] > 1) {
                $logger->error('返款订单对应多笔流水，需要手动匹配', [
                    'data' => (array)$item
                ]);
                continue;
            }

            // 一笔支付宝订单匹配到多笔结算
            if ($alipay_order_no_group[$item->alipay_order_no] > 1) {
                $logger->error('单笔流水对应多笔返款订单，需要手动匹配', [
                    'data' => (array)$item
                ]);
                continue;
            }

            $update_data = [
                'alipay_order_no' => $item->alipay_order_no,
                'trans_memo' => $item->trans_memo,
                'alipay_order_type' => $item->alipay_order_type,
                'alipay_trans_dt' => $item->alipay_trans_dt,
                'status' => $receive_model::STATUS_SUCCESS,
                'update_time' => date('Y-m-d H:i:s')
            ];

            $receive_model->updateByTradeNo($item->trade_no, $update_data);

            // 匹配成功的删除redis 方便后续扫码获取最新状态
            $receive_redis_model->delete($item->trade_no);

            $order_ids = explode(",", $item->order_list);
            $all_order_ids = array_merge($all_order_ids, $order_ids);
        }

        // 自动让二维码过期失效
        $expire_list = $receive_model->getExpireList();
        $expire_trade_nos = $expire_list->pluck('trade_no')->unique()->toArray();
        // 不删除redis等他自动过期 内部用户要查看数据
        foreach ($expire_trade_nos as $item) {
            $receive_redis_model->delete($item);
        }

        if ($expire_trade_nos) {
            $receive_model->updateByTradeNos($expire_trade_nos, [
                'status' => $receive_model::STATUS_EXPIRE,
                'update_time' => date('Y-m-d H:i:s')
            ]);
        }

        $expire_order_ids = $expire_list->pluck("order_id")->toArray();
        $all_order_ids = array_values(array_unique(array_merge($all_order_ids, $expire_order_ids)));
        if (count($all_order_ids) > 0) {
            (new ToutiaoTaskModel())->refreshLiveOrderQrCodeCalc($all_order_ids);
        }

        $receive_redis_model->unLock();
        $logger->info("结束执行");

        return true;
    }

    /**
     * 同步游戏APP_NAME到BPM
     * @return true
     */
    public function syncGameAppNameToBpm()
    {
        $logger = Helpers::getLogger('star_demand');
        $logger->info("同步游戏APP_NAME到BPM-开始执行");
        $redis_model = new StarDemandToBpmModel();
        if (!$redis_model->lock("APP_NAME")) {
            $logger->info("同步游戏APP_NAME到BPM-上一轮任务还在进行中");
            return true;
        }

        // 找出2025-01-01以来有消耗的游戏
        $app_name_list = (new V2DimGameIdModel())->getToutiaoCostGameListByCostDate('TW', '2025-01-01');

        $app_name_list = $app_name_list->map(function ($item) {
            $item->platform = EnvConfig::PLATFORM_MAP[$item->platform];
            return $item;
        })->toArray();

        // 同步给BPM
        try {
            $result = (new WorkProjectInstModel())->syncLiveDemandAppNameList([
                "space_id" => 26,
                "work_project_id" => 197,
                "staff_number" => "TW3104",
                "field_values" => $app_name_list
            ]);
        } catch (AppException $e) {
            $result = ['err_msg' => $e->getMessage()];
        }

        $logger->info("同步游戏APP_NAME到BPM-返回结果", $result);
        $logger->info("同步游戏APP_NAME到BPM-结束执行");
        $redis_model->unLock("APP_NAME");

        return true;
    }

    /**
     * 同步星图订单到BPM
     * @return true
     */
    public function updateLiveOrderToBpm()
    {
        $logger = Helpers::getLogger('star_demand');
        $logger->info("同步星图订单到BPM-开始执行");

        $redis_model = new StarDemandToBpmModel();
        if (!$redis_model->lock("LIVE_ORDER")) {
            $logger->info("同步星图订单到BPM-上一轮任务还在进行中");
            return true;
        }

        // 以订单创建时间偏移5分钟
        $now = time();
        $start_time = date("Y-m-d H:i:s", strtotime("-10 minutes", $now));
        $end_time = date("Y-m-d H:i:s", $now);

        $result = (new StarDemandLogic())->updateAnchorCooperateLiveOrder($start_time, $end_time, []);

        $logger->info("同步星图订单到BPM-返回结果", $result);
        $logger->info("同步星图订单到BPM-结束执行");
        $redis_model->unLock("LIVE_ORDER");

        return true;
    }

    /**
     * 同步订单结算到BPM
     * @return bool
     */
    public function updateSettlementToBpm()
    {
        $logger = Helpers::getLogger('star_demand');
        $logger->info("同步订单结算到BPM-开始执行");

        $redis_model = new StarDemandToBpmModel();
        if (!$redis_model->lock("SETTLEMENT")) {
            $logger->info("同步订单结算到BPM-上一轮任务还在进行中");
            return true;
        }

        // 以订单创建时间偏移5分钟
        $now = time();
        $start_time = date("Y-m-d H:i:s", strtotime("-10 minutes", $now));
        $end_time = date("Y-m-d H:i:s", $now);

        $condition = [
            'update_time' => [$start_time, $end_time],
            'page' => 1,
            'rows' => 100000
        ];
        $result = (new StarDemandLogic())->updateAnchorCooperateSettlement($condition);

        $logger->info("同步订单结算到BPM-返回结果", $result);
        $logger->info("同步订单结算到BPM-结束执行");

        $redis_model->unLock("SETTLEMENT");

        return true;
    }

    /**
     * 同步主播费用到BPM
     * @return bool
     */
    public function updateSpecialFundToBpm()
    {
        $logger = Helpers::getLogger('star_demand');
        $logger->info("同步主播费用到BPM-开始执行");

        $redis_model = new StarDemandToBpmModel();
        if (!$redis_model->lock("SPECIAL_FUND")) {
            $logger->info("同步主播费用到BPM-上一轮任务还在进行中");
            return true;
        }

        // 以订单创建时间偏移5分钟
        $now = time();
        $start_time = date("Y-m-d H:i:s", strtotime("-10 minutes", $now));
        $end_time = date("Y-m-d H:i:s", $now);

        $result = (new StarDemandLogic())->updateAnchorCooperateSpecialFund([
            'update_time' => [$start_time, $end_time]
        ]);

        $logger->info("同步主播费用到BPM-返回结果", $result);
        $logger->info("同步主播费用到BPM-结束执行");

        $redis_model->unLock("SPECIAL_FUND");

        return true;
    }

    /**
     * 同步根游戏到BPM
     * @return true
     */
    public function syncRootGameToBpm()
    {
        $logger = Helpers::getLogger('star_demand');
        $logger->info("同步根游戏到BPM-开始执行");
        $redis_model = new StarDemandToBpmModel();
        if (!$redis_model->lock("ROOT_GAME", 30 * 60)) {
            $logger->info("同步根游戏到BPM-上一轮任务还在进行中");
            return true;
        }

        $root_game_list = (new V2DimGameIdModel())->getAllRootGameByPlatform('TW');

        $root_game_list = $root_game_list->where('root_game_name', '!=', '')->map(function ($item) {
            $item->platform = EnvConfig::PLATFORM_MAP[$item->platform];
            return (array)$item;
        })->toArray();

        // 同步给BPM
        try {
            $result = (new WorkProjectInstModel())->syncRootGameList([
                "staff_number" => "TW3104",
                "field_values" => $root_game_list
            ]);
        } catch (AppException $e) {
            $result = ['err_msg' => $e->getMessage()];
        }

        $logger->info("同步根游戏到BPM-返回结果", $result);
        $logger->info("同步根游戏到BPM-结束执行");
        $redis_model->unLock("ROOT_GAME");

        return true;
    }
}
