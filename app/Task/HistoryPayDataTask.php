<?php

/**
 * 同步历史游戏支付数据
 */

namespace App\Task;


use App\Logic\DMS\OperationProfitLogic;
use App\Model\SqlModel\Tanwan\V2DimGameIdModel;
use App\Model\SqlModel\Tanwan\V2DWDDayPayLogModel;
use App\Model\SqlModel\Zeda\AppletDivideModel;
use App\Model\SqlModel\Zeda\GamePayMoneyModel;
use App\Model\SqlModel\Zeda\GameProfitModel;
use App\Model\SqlModel\Zeda\MainGameProfitDetailModel;
use App\Model\SqlModel\Zeda\OperationCostFieldModel;
use App\MysqlConnection;
use App\Param\MainGameProfitParam;
use App\Struct\RedisCache;
use App\Utils\Helpers;
use App\Utils\Math;
use Exception;

class HistoryPayDataTask
{


    /**
     * 同步所有game_id维度的支付数据
     *
     * @return bool
     */
    public function syncAllHistoryGameData($logger)
    {
        $config_model = new MainGameProfitDetailModel();
        $game_pay_model = new GamePayMoneyModel();
        $service = new OperationProfitLogic();
        $day_pay_log_model = new \App\Model\SqlModel\DatahubLY\V2DWDDayPayLogModel();
        $game_id_model = new V2DimGameIdModel();
        $operation_cost_field_model = new OperationCostFieldModel();
        $logger->info('同步脚本开始');

//        // TODO 测试买量 sql
//        $day_pay_log_model = new V2DWDDayPayLogModel();
//        $start_date = 1393344000;                               // 2014-02-26 00:00:00
//        $to_date = strtotime(date("Y-m-d", time()));     // 今天凌晨
//        $per_time = 86400 * 30; // 三十天一次
//        $total_list = $day_pay_log_model->getGameTotalPayMoney($start_date, $start_date + $per_time);
//        exit;

        try {
            MysqlConnection::getConnection('default')->beginTransaction();
        } catch (Exception $e) {
            $logger->error('事务开启异常，结束任务');
            return false;
        }
        // 先清空表数据
        $game_pay_model->deleteByDate();


        # -----------------------------  开始同步买量游戏支付总额数据  -----------------------------#
        $day_pay_log_model = new V2DWDDayPayLogModel();
        $start_date = 1393344000;                               // 2014-02-26 00:00:00
        $to_date = strtotime(date("Y-m-d", time()));     // 今天凌晨
        $per_time = 86400 * 30; // 三十天一次
        while (1) {
            // 终止条件
            if ($start_date > $to_date) break;
            $logger->info('开始同步买量游戏历史数据，时间区间： ' . date("Y-m-d", $start_date) . '至' . date("Y-m-d", $start_date + $per_time));
            try {
                $insert_data = [];
                $total_list = $day_pay_log_model->getGameTotalPayMoney($start_date, $start_date + $per_time);
                foreach ($total_list as $item) {
                    $insert_data[] = [
                        'platform'          => $item->platform ?? '',
                        'clique_id'         => $item->clique_id ?? 0,
                        'clique_name'       => $item->clique_name ?? '',
                        'root_game_id'      => $item->root_game_id ?? 0,
                        'root_game_name'    => $item->root_game_name ?? '',
                        'main_game_id'      => $item->main_game_id ?? 0,
                        'main_game_name'    => $item->main_game_name ?? '',
                        'game_id'           => $item->game_id ?? 0,
                        'game_name'         => $item->game_name ?? '',
                        'game_pay_money'    => $item->game_pay_money ?? 0,
                        'apple_pay_money'   => $item->apple_pay_money ?? 0,
                        'inspire_money'     => $item->inspire_money ?? 0,
                        'yyb_pay_money'     => $item->yyb_pay_money ?? 0,
                        'applet_pay_money'  => $item->applet_pay_money ?? 0,
                        'pay_way_pay_money' => $item->pay_way_pay_money ?? 0,
                        'dehan_pay_money'   => $item->dehan_pay_money ?? 0,
                        'dehan_divide_money'   => $item->dehan_divide_money ?? 0,
                        'is_channel'        => 0,
                        'cost_money'        => Math::decimal($item->cost_money, 2),
                        'plat_id'           => $item->plat_id ?? 0,
                        'plat_name'         => $item->plat_name ?? '',
                        'os'                => $item->os ?? '',
                        'pay_date'          => $item->pay_date,
                        'true_cost'         => Math::decimal($item->true_cost, 2),
                        'finance_money'     => Math::decimal($item->finance_money, 2),
                        'proxy_type'        => $item->proxy_type ?? 1,
                        'channel_id'        => $item->channel_id ?? 0,
                        'channel_name'      => $item->channel_name ?? '',
                    ];
                }

                // 入库
                $game_pay_model->addMultiple($insert_data);
                $logger->info('买量游戏数据同步成功。时间区间： ' . date("Y-m-d", $start_date) . '至' . date("Y-m-d", $start_date + $per_time));
            } catch (\Exception $e) {
                $logger->error('买量数据同步失败，回滚数据,时间区间:' . date("Y-m-d", $start_date) . '至' . date("Y-m-d", $start_date + $per_time));
                $logger->error('买量数据同步失败，回滚数据, 异常信息：' . substr($e->getMessage(), 0, 1000));
                try {
                    MysqlConnection::getConnection('default')->rollBack();
                } catch (\Exception $e) {
                    $logger->error('事务回滚异常！');
                }
                return false;
            }

            $add_time = $per_time + 86400;
            $start_date += $add_time;
        }
        $logger->info('买量游戏数据同步完成');
        MysqlConnection::getConnection('default')->commit();

        # -----------------------------  开始同步历史共享的cost_filed  -----------------------------#
        $logger->info('开始跑历史共享的cost_filed');
        /* 先跑共享的cost_filed */
        // 获取需要更新的root_game_id
        $need_update_root_game = $operation_cost_field_model->getShareRootList();
        $insert_data = [];
        foreach ($need_update_root_game as $root_game_item) {
            $start_date = date("Y-m-01", strtotime($root_game_item->cost_month));
            $end_date = date('Y-m-d', strtotime("$start_date +1 month -1 day"));
            // 共享的需要同步到根游戏下的所有主游戏
            $main_game_list = $game_id_model->getAllMainGameByRootGameId($root_game_item->root_game_id, $root_game_item->platform);
            // 获取时间范围内根游戏总流水
            $total_pay_money = $game_pay_model->getRootGameTotalPayMoney($root_game_item->root_game_id, $root_game_item->platform, [$start_date, $end_date], $root_game_item->proxy_type);
            $game_count = $game_id_model->getGameCountByRootGame($root_game_item->platform, $root_game_item->root_game_id, $root_game_item->proxy_type);
            $per_data = $service->perCostMoney($start_date,
                $end_date,
                $total_pay_money,
                $root_game_item->authorization_money,
                $root_game_item->endorsement_money,
                $root_game_item->server_cost,
                $game_count);
            // 共享情况下 mark通过根游戏流水判断
            $mark = $total_pay_money > 0 ? 1 : 0;
            foreach ($main_game_list as $main_game_item) {
                $service->makeUpGamePayMoney($root_game_item->platform, $root_game_item->root_game_id, $main_game_item->main_game_id, $root_game_item->proxy_type, $start_date, $end_date);
                $insert_data[] = [
                    'platform'                    => $root_game_item->platform,
                    'root_game_id'                => $root_game_item->root_game_id,
                    'proxy_type'                  => $root_game_item->proxy_type,
                    'main_game_id'                => $main_game_item->main_game_id,
                    'per_authorization_money'     => $per_data['per_authorization_money'],
                    'per_endorsement_money'       => $per_data['per_endorsement_money'],
                    'per_server_cost'             => $per_data['per_server_cost'],
                    'per_day_authorization_money' => $per_data['per_day_authorization_money'],
                    'per_day_endorsement_money'   => $per_data['per_day_endorsement_money'],
                    'per_day_server_cost'         => $per_data['per_day_server_cost'],
                    'cost_month'                  => $root_game_item->cost_month,
                    'authorization_money'         => $root_game_item->authorization_money,
                    'endorsement_money'           => $root_game_item->endorsement_money,
                    'server_cost'                 => $root_game_item->server_cost,
                    'mark'                        => $mark,
                    'module'                      => 'dms'
                ];
            }
            $logger->info('单月根游戏同步完成', [$root_game_item->cost_month, $root_game_item->platform, $root_game_item->root_game_id, $root_game_item->proxy_type]);
        }
        // 入库
        $operation_cost_field_model->addMultiple($insert_data);
        $logger->info('同步共享的cost_filed完成');

        $logger->info('开始同步非共享的cost_filed');
        // 获取非共享的主游戏cost_filed
        $not_share_main_game = $operation_cost_field_model->getAllNotShareMainGame();
        $insert_data = [];
        foreach ($not_share_main_game as $main_game_item) {
            $start_date = date("Y-m-01", strtotime($main_game_item->cost_month));
            $end_date = date('Y-m-d', strtotime("$start_date +1 month -1 day"));
            [$mark, $total_pay_money] = $service->makeUpGamePayMoney($main_game_item->platform, $main_game_item->root_game_id, $main_game_item->main_game_id, $main_game_item->proxy_type, $start_date, $end_date);
            $game_count = $game_id_model->getGameCountByMainGame($main_game_item->platform, $main_game_item->main_game_id, $main_game_item->proxy_type);
            $per_data = $service->perCostMoney($start_date,
                $end_date,
                $total_pay_money,
                $main_game_item->authorization_money,
                $main_game_item->endorsement_money,
                $main_game_item->server_cost,
                $game_count);
            // 入库数据
            $insert_data[] = [
                'platform'                    => $main_game_item->platform,
                'root_game_id'                => $main_game_item->root_game_id,
                'main_game_id'                => $main_game_item->main_game_id,
                'proxy_type'                  => $main_game_item->proxy_type,
                'per_authorization_money'     => $per_data['per_authorization_money'],
                'per_endorsement_money'       => $per_data['per_endorsement_money'],
                'per_server_cost'             => $per_data['per_server_cost'],
                'per_day_authorization_money' => $per_data['per_day_authorization_money'],
                'per_day_endorsement_money'   => $per_data['per_day_endorsement_money'],
                'per_day_server_cost'         => $per_data['per_day_server_cost'],
                'cost_month'                  => $main_game_item->cost_month,
                'authorization_money'         => $main_game_item->authorization_money,
                'endorsement_money'           => $main_game_item->endorsement_money,
                'server_cost'                 => $main_game_item->server_cost,
                'mark'                        => $mark,
                'module'                      => 'dms'
            ];
            $logger->info('单月主游游戏同步完成', [$main_game_item->cost_month, $main_game_item->platform, $main_game_item->root_game_id, $main_game_item->main_game_id]);
        }
        // 入库
        $operation_cost_field_model->addMultiple($insert_data);
        $logger->info('同步非共享的cost_filed完成');
        # -----------------------------  同步三个cost字段的数据结束  -----------------------------#


        # -----------------------------  计算研发利润  -----------------------------#
        $config_list = $config_model->getAll();
        foreach ($config_list as $config) {
            $logger->info('开始计算。platform: ' . $config->platform . ', main_game_id: ' . $config->main_game_id);
            $data = [
                'platform'      => $config->platform,
                'root_game_id'  => $config->root_game_id,
                'main_game_id'  => $config->main_game_id,
                'formula'       => json_decode($config->formula, true),
                'ios_divide'    => $config->ios_divide,
                'yyb_divide'    => $config->yyb_divide,
                'applet_divide' => $config->applet_divide,
                'diy_param'     => $config->diy_param,
                'proxy_type'    => $config->proxy_type,
            ];
            $param = new MainGameProfitParam($data);
            try {
                $service->computeProfit($param);
            } catch (Exception $e) {
                $logger->info('出现异常，计算结束。platform: ' . $config->platform . ', main_game_id: ' . $config->main_game_id, [$e->getMessage()]);
                continue;
            }
            $update_date = [
                'profit' => $param->profit,
            ];
            $config_model->updateOne($update_date, $config->id);
            $logger->info('结束计算。platform: ' . $config->platform . ', main_game_id: ' . $config->main_game_id);
        }

        $logger->info('研发利润更新完成');

        # -----------------------------  同步研发分成到adb  -----------------------------#
        $start_date = 1393344000;                               // 2014-02-26 00:00:00
        $to_date = strtotime(date("Y-m-d", time()));     // 今天凌晨
        $logic = new OperationProfitLogic();
        $logic->syncDividePercentToADB($start_date, $to_date);
        $logger->info('研发分成同步完成');
        # -----------------------------  同步研发分成到adb  -----------------------------#


        # -----------------------------  小程序分成比例  -----------------------------#
        $logger->info('小程序分成比例开始处理');
        $this->syncApplet();
        $logger->info('小程序分成比例处理完成');

        $logger->info('脚本任务结束');
        return true;
    }


    public function syncApplet()
    {
        $res = (new V2DimGameIdModel())->getListLikeClique('小', [], -1, null, 1, 1000);
        $clique_id_list = $res->pluck('clique_id')->toArray();
        $list = (new GameProfitModel())->getAppletPayMoney($clique_id_list);
        $insert_data = [];
        foreach ($list as $item) {
            $insert_data[] = [
                'platform'       => $item->platform,
                'root_game_id'   => $item->root_game_id,
                'game_id'        => $item->game_id,
                'proxy_type'     => $item->proxy_type,
                'pay_date'       => $item->pay_date,
                'applet_divide'  => $item->applet_divide ?? 0,
                'game_pay_money' => $item->game_pay_money,
            ];
        }
        $applet_model = new AppletDivideModel();
        // 先清空，再添加
        $applet_model->truncate();
        $applet_model->addMultiple($insert_data);

    }
}
