<?php

namespace App\Param\DMS;

use App\Logic\DMS\PermissionLogic;
use App\Param\AbstractParam;
use App\Service\PermissionService;

class OtherCostDetailListExportParam extends AbstractParam
{
    public $platform = '';

    public $root_game_id;

    public $money_type;

    public $cost_month;

    public $game_permission = [];                   // 游戏权限

    public $project_team_ids = [];

    public $remark = '';

    public $agency = '';

    public function paramHook()
    {
        if (!empty($this->project_team_ids)) {
            $this->project_team_ids = explode(',', $this->project_team_ids);
        }

        if (!empty($this->money_type)) {
            $this->money_type = explode(',', $this->money_type);
        }

        // 前端选了日期又叉掉的情况下，会传个null过来，处理下
        if ($this->cost_month == 'null') {
            $this->cost_month = '';
        }

        if (!empty($this->cost_month)) {
            $this->cost_month = explode(',', $this->cost_month);
        }
    }

    /**
     * 设置登录用户的game_permission和agent_permission
     *
     */
    public function setLoginUserPermission()
    {
        // 获取权限
        $permission = (new PermissionService())->getLoginUserDMSDataPermission();
        $this->game_permission = $permission['game_permission'];
    }

    /**
     * 设置某个用户的game_permission和agent_permission
     *
     * @param $user_id
     */
    public function setUserPermission($user_id)
    {
        // 获取权限
        $permission = (new PermissionLogic())->getDataPermissionByUserId($user_id);
        $this->game_permission = $permission['game_permission'];
    }
}