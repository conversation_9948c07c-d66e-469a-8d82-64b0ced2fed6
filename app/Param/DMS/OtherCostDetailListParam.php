<?php

namespace App\Param\DMS;

use App\Logic\DMS\PermissionLogic;
use App\Param\AbstractParam;
use App\Service\PermissionService;

class OtherCostDetailListParam extends AbstractParam
{
    public $platform = '';

    public $root_game_id;

    public $money_type;

    public $cost_month;

    public $game_permission = [];                   // 游戏权限

    public $project_team_ids = [];

    public $remark = '';

    public $agency = '';

    /**
     * 设置登录用户的game_permission和agent_permission
     *
     */
    public function setLoginUserPermission()
    {
        // 获取权限
        $permission = (new PermissionService())->getLoginUserDMSDataPermission();
        $this->game_permission = $permission['game_permission'];
    }

    /**
     * 设置某个用户的game_permission和agent_permission
     *
     * @param $user_id
     */
    public function setUserPermission($user_id)
    {
        // 获取权限
        $permission = (new PermissionLogic())->getDataPermissionByUserId($user_id);
        $this->game_permission = $permission['game_permission'];
    }
}