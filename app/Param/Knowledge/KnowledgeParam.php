<?php

namespace App\Param\Knowledge;

use App\Exception\AppException;
use App\Param\AbstractParam;
use App\Service\GroupAssistant\KnowledgeService;

/**
 * 知识问答需要的参数
 */
class KnowledgeParam extends AbstractParam
{

    /**
     * 提问用户的union_id
     * @var string
     */
    public $union_id = '';

    /**
     * 用户和提问的问题
     *
     * @var string
     */
    public $query = '';


    /**
     * 在群里提问的群id
     * @var string
     */
    public $chat_id = '';


    /**
     * 提问类型
     * 1. 私聊 p2p
     * 2. 群聊 group
     *
     * @var string
     */
    public $type = 'p2p';


    /**
     * 消息发送类型
     * 私聊 union_id
     * 群聊 group
     *
     * @var string
     */
    public $receive_id_type = 'union_id';


    /**
     * 发送的id
     * union_id 或 chat_id
     *
     * @var string
     */
    public $receive_id = '';

    /**
     * 群id的映射
     *
     * @var array
     */
    public $chat_id_map = [];


    /**
     * 给大模型的参考资料，就是分群的聊天记录
     *
     * @var string
     */
    public $message_list = '';


    public $session_key = '';

    /**
     * debug的respond
     *
     * @var array
     */
    public $respond = [];

    /**
     * 日期范围
     * @var string
     */
    public $start_date = '';

    /**
     * 日期范围
     *
     * @var string
     */
    public $end_date = '';

    /**
     * 搜索的群范围
     *
     * @var array
     */
    public $chat_list = [];


    public function paramHook()
    {
        if ($this->type === 'group' && !$this->chat_id) {
            throw new AppException('参数异常，群聊知识问答必须输入chat_id参数');
        }

        if ($this->type === 'p2p') {
            $this->session_key = KnowledgeService::QA_SESSION_KEY . $this->union_id;
            $this->receive_id = $this->union_id;
            $this->receive_id_type = 'union_id';
        } else {
            $this->session_key = KnowledgeService::QA_SESSION_KEY . "group_{$this->chat_id}";
            $this->receive_id = $this->chat_id;
            $this->receive_id_type = 'chat_id';
        }
    }

}