<?php

namespace App\Param\ADServing;

use App\Param\AbstractParam;

class DimensionMonitorRobotLogSearchParam extends AbstractParam
{

    public $robot_id;

    public $is_effective;

    public $target_value;

    public $log_time = [];

    public $page = 1;

    public $rows = 50;

    public function paramHook()
    {
        if ($this->log_time) {
            $this->log_time = array_map(function ($time) {
                return date('Y-m-d H:i:s', substr($time, 0, 10));
            }, $this->log_time);
        }
    }

}
