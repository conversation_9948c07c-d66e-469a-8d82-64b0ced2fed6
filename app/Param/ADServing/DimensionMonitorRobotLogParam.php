<?php

namespace App\Param\ADServing;

use App\Param\AbstractParam;

class DimensionMonitorRobotLogParam extends AbstractParam
{
    public $robot_id;
    public $media_type;
    public $media_agent_type;
    public $target_type= '';
    public $target_value = '';
    public $action_target = '';
    public $action_value = '';
    public $is_effective = '';
    public $msg = '';
    public $sql = '';

    public $creator;

    public function reset()
    {
        $this->robot_id = 0;
        $this->media_type = 0;
        $this->media_agent_type = 0;
        $this->target_type = '';
        $this->target_value = '';
        $this->action_target = '';
        $this->action_value = '';
        $this->is_effective = '';
        $this->msg = '';
        $this->sql = '';
    }

    public function toData(): array
    {
        $data = parent::toArray();
        return $data;
    }

}
