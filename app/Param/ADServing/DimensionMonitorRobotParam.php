<?php

namespace App\Param\ADServing;

use App\Container;
use App\Exception\AppException;
use App\Param\AbstractParam;

class DimensionMonitorRobotParam extends AbstractParam
{
    public $name;
    public $media_type;
    public $media_agent_type;
    public $platform;
    public $exec_target_dim = '';
    public $monitoring_frequency = 3;
    public $range_time_type = 'hours';
    public $range_hour = 0;
    public $action_target;
    public $action_value;
    public $filter = [];
    public $calc = [];
    public $target = [];
    public $order = [];
    public $creator;

    public function paramHook()
    {
        $this->calc = (object)$this->calc;
        $this->target = (object)$this->target;
    }


    public function initBySql($object)
    {
        $this->name = $object->name;
        $this->media_type = $object->media_type;
        $this->media_agent_type = $object->media_agent_type;
        $this->platform = $object->platform;
        $this->exec_target_dim = $object->exec_target_dim;
        $this->monitoring_frequency = $object->monitoring_frequency;
        $this->range_time_type = $object->range_time_type;
        $this->range_hour = $object->range_hour;
        $this->action_target = $object->action_target;
        $this->action_value = $object->action_value;
        $this->filter = json_decode($object->filter, true);
        $this->calc = json_decode($object->calc, true);
        $this->order = json_decode($object->order, true);
        $this->target = json_decode($object->target, true);
        $this->creator = $object->creator;
        return $this;
    }

    public function validate()
    {
        if (!$this->action_target) {
            throw new AppException('执行必须有值');
        }
        if (!$this->action_value) {
            throw new AppException('执行值必须有值');
        }
        if (!$this->media_type) {
            throw new AppException('媒体类型必须有值');
        }
        if (!$this->media_agent_type) {
            throw new AppException('广告类型必须有值');
        }
        if (!$this->exec_target_dim) {
            throw new AppException('维度目标必须有值');
        }
        if (!$this->calc) {
            throw new AppException('条件筛选必须有值');
        }
    }

    public function toData()
    {
        $this->validate();
        $data = parent::toArray();
        if (!$data['creator']) {
            $data['creator'] = Container::getSession()->name;
        }
        $data['filter'] = json_encode($data["filter"], JSON_UNESCAPED_UNICODE);
        $data['calc'] = json_encode($data["calc"], JSON_UNESCAPED_UNICODE);
        $data['order'] = json_encode($data["order"], JSON_UNESCAPED_UNICODE);
        $data['target'] = json_encode($data["target"], JSON_UNESCAPED_UNICODE);

        return $data;
    }

}
