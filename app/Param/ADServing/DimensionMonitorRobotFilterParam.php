<?php
/**
 * User: zzh
 * Date: 2019-12-10
 */

namespace App\Param\ADServing;

use App\Constant\ADComposeFilterSqlMap;
use App\Constant\MediaType;
use App\Exception\AppException;
use App\Param\AbstractParam;
use App\Utils\SqlParser;

/**
 * Class DimensionMonitorRobotFilterParam
 * @package App\Param\ADServing
 */
class DimensionMonitorRobotFilterParam extends AbstractParam
{
    public $dimension_exec_target_dim;

    public $media_type = 0;                     // 所选媒体
    public $range_hour = 0;
    public $company = '';                       // 所选公司
    public $platform = '';                      // 所选平台
    public $target = [];                        // 所有指标
    public $filter = [];                        // 所有筛选
    public $calc = [];                          // 所有数值条件
    public $condition = 'and';                  // 数值筛选的逻辑

    public $order = [];
    public $group_by = '';
    public $limit = 5000;

    public $ad2_common_log_target = [];
    public $ad3_common_log_target = [];
    public $dim_agent_site_target = [];
    public $dim_site_game_target = [];
    public $reg_log_target = [];
    public $overview_log_target = [];
    public $hour_data_log_target = [];
    public $material_file_log_target = [];
    public $material_log_target = [];
    public $account_common_log_target = [];

    public $tt_hour_data_log_target = [];

    public $tx_component_day_data_log_target = [];

    public $tt_inefficient_material_log_target = [];

    public $main_target = [];

    public $driver_filter = [];
    public $ad2_common_log_filter = [];
    public $ad3_common_log_filter = [];
    public $dim_agent_site_filter = [];
    public $dim_site_game_filter = [];
    public $reg_log_filter = [];
    public $overview_log_filter = [];
    public $hour_data_log_filter = [];
    public $material_file_log_filter = [];
    public $material_log_filter = [];
    public $material_log_extend_filter = [];
    public $account_common_log_filter = [];
    public $tt_hour_data_log_filter = [];

    public $tx_component_day_data_log_filter = [];

    public $main_filter = [];

    public $main_calc = [];

    public $need_join_ad2_common_log = [];
    public $need_join_ad3_common_log = [];
    public $need_join_material_file_log = [];
    public $need_join_material_log = [];
    public $need_join_game_log = [];
    public $need_join_tt_inefficient_material_log = [];

    /**
     * ADMediaAccountFilterParam constructor.
     * @param array $property
     */
    public function __construct(array $property = [])
    {
        $this->paramCheck($property);
        parent::__construct($property);
    }

    /**
     * 获取表名
     * @param $index
     * @return mixed
     */
    public function getTable($index)
    {
        return ADComposeFilterSqlMap::TABLE[$index];
    }

    /**
     * agentSiteId表唯一id
     * @return array
     */
    public function agentSiteIdUniqueSiteCondition()
    {
        $table = ADComposeFilterSqlMap::TABLE['dim_agent_site'];
        return ["{$table}.agent_leader_end_time" => '2100-01-01 00:00:00'];
    }

    public function targetFormat()
    {
        foreach ($this->target as $target_key => $target_value) {
            $target_config = ADComposeFilterSqlMap::TARGET_CONFIG[$target_value] ?? '';
            if ($target_config) {
                foreach ($target_config as $table => $target) {
                    $table_target_name = "{$table}_target";
                    $this->{$table_target_name} = array_merge($this->{$table_target_name}, $target);
                }
            }
        }
        $media_target_config_map = [];
        switch ($this->media_type) {
            case MediaType::TOUTIAO:
                $media_target_config_map = ADComposeFilterSqlMap::TT_TARGET_CONFIG ?? [];
                break;
        }
        if ($media_target_config_map) {
            foreach ($this->target as $target_key => $target_value) {
                $media_target_config = $media_target_config_map[$target_value] ?? '';
                if ($media_target_config) {
                    foreach ($media_target_config as $media_table => $media_target) {
                        $media_table_target_name = "{$media_table}_target";
                        $this->{$media_table_target_name} = array_merge($this->{$media_table_target_name}, $media_target);
                    }
                }
            }
        }
    }

    public function filterFormat()
    {
        foreach ($this->filter as $filter_key => $filter_value) {
            $filter_config = ADComposeFilterSqlMap::FILTER_CONFIG[$filter_value['column']] ?? '';
            $filter_value['column'] = $this->transferFilter($filter_value['column']);
            if ($filter_config) {
                foreach ($filter_config as $table => $filter) {
                    if (!($this->need_join_ad2_common_log[$table] ?? false) && strpos($filter, "ad2_common_log") !== false) {
                        $this->need_join_ad2_common_log[$table] = true;
                    }
                    if (!($this->need_join_ad3_common_log[$table] ?? false) && strpos($filter, "ad3_common_log") !== false) {
                        $this->need_join_ad3_common_log[$table] = true;
                    }
                    if (!($this->need_join_material_file_log[$table] ?? false) && strpos($filter, "material_file_log") !== false) {
                        $this->need_join_ad3_common_log[$table] = true;
                        $this->need_join_material_file_log[$table] = true;
                    }
                    if (!($this->need_join_material_log[$table] ?? false) && strpos($filter, "material_log") !== false) {
                        $this->need_join_ad3_common_log[$table] = true;
                        $this->need_join_material_log[$table] = true;
                        $this->need_join_material_file_log[$table] = true;
                    }
                    if (!($this->need_join_game_log[$table] ?? false) && strpos($filter, "game") !== false) {
                        $this->need_join_ad2_common_log[$table] = true;
                        $this->need_join_game_log[$table] = true;
                    }
                    if (!($this->need_join_tt_inefficient_material_log[$table] ?? false) && strpos($filter, "inefficient_material_log") !== false) {
                        $this->need_join_ad3_common_log[$table] = true;
                        $this->need_join_tt_inefficient_material_log[$table] = true;
                    }
                    $table_filter_name = "{$table}_filter";
                    $filter_array = explode('.', $filter);

                    if (count($filter_array) > 1) {
                        $filter_name = array_pop($filter_array);
                        $alias = implode('.', $filter_array);
                        if (strpos($filter_value['column'], "platform-") !== false) {
                            // 适配查询构造器bug
                            $this->{$table_filter_name}[] = SqlParser::get($filter_value, '', $alias);
                        } else {
                            $this->{$table_filter_name}[] = SqlParser::get($filter_value, $filter_name, $alias);
                        }
                    } else {
                        if (strpos($filter_value['column'], "platform-") !== false) {
                            // 适配查询构造器bug
                            $this->{$table_filter_name}[] = SqlParser::get($filter_value);
                        } else {
                            $this->{$table_filter_name}[] = SqlParser::get($filter_value, $filter);
                        }
                    }
                }
            }
        }
    }

    public function calcFormat()
    {
        foreach ($this->calc as $calc_key => $calc_value) {
            $calc_config = ADComposeFilterSqlMap::CALC_CONFIG[$calc_value['column']];

            $target_config = ADComposeFilterSqlMap::TARGET_CONFIG[$calc_value['column']] ?? '';
            if ($target_config) {
                foreach ($target_config as $table => $target) {
                    $table_target_name = "{$table}_target";
                    $this->{$table_target_name} = array_merge($this->{$table_target_name}, $target);
                }
            }

            if ($calc_config) {
                foreach ($calc_config as $table => $calc) {
                    $table_calc_name = "{$table}_calc";
                    $calc_array = explode('.', $calc);
                    if (count($calc_array) > 1) {
                        $calc_name = array_pop($calc_array);
                        $alias = implode('.', $calc_array);
                        $this->{$table_calc_name}[] = SqlParser::get($calc_value, $calc_name, $alias);
                    } else {
                        $this->{$table_calc_name}[] = SqlParser::get($calc_value, $calc);
                    }
                }
            }
        }
    }

    private function transferFilter($column)
    {
        switch ($column) {
            case 'game_id':
                return 'platform-game_id';
            case 'main_game_id':
                return 'platform-main_game_id';
            case 'root_game_id':
                return 'platform-root_game_id';
            case 'site_id':
                return 'platform-site_id';
            case 'agent_group_id':
                return 'platform-agent_group_id-agent_group_name';
            case 'theme_pid':
                return 'platform-theme_pid';
            case 'material_id':
                return 'platform-material_id';
            case 'theme_id':
                return 'platform-theme_id';
            default:
                return $column;
        }
    }

    private function paramCheck($data)
    {
        if (!isset($data['media_type'])) {
            throw new AppException('请选择媒体');
        }

        if (!isset($data['platform'])) {
            throw new AppException('请选择平台');
        }

        if (isset($data['group_by']) && !empty($data['group_by']) && !in_array($data['group_by'], ['web_targeting_id', 'current_targeting_id'])) {
            throw new AppException('聚合字段非法');
        }
    }

    public function paramHook()
    {

        $this->ad2_common_log_target = array_unique($this->ad2_common_log_target);
        $this->ad3_common_log_target = array_unique($this->ad3_common_log_target);
        $this->reg_log_target = array_unique($this->reg_log_target);
        $this->overview_log_target = array_unique($this->overview_log_target);
        $this->hour_data_log_target = array_unique($this->hour_data_log_target);
        $this->tt_hour_data_log_target = array_unique($this->tt_hour_data_log_target);
        $this->material_file_log_target = array_unique($this->material_file_log_target);
        $this->material_log_target = array_unique($this->material_log_target);
        $this->account_common_log_target = array_unique($this->account_common_log_target);
        $this->tx_component_day_data_log_target = array_unique($this->tx_component_day_data_log_target);

        $this->main_target = array_unique($this->main_target);
    }

}
