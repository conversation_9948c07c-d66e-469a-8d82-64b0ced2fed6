<?php

namespace App\Param\ADServing;

use App\Model\SqlModel\Zeda\ADIntelligentMonitorBindModel;
use App\Param\AbstractParam;

class ADIntelligentMonitorBindParam extends AbstractParam
{
    public $id;

    public $media_type;
    public $media_agent_type;
    public $bind_type = 'precise';
    public $bind_target_type = '';
    public $bind_target_value = '';
    public $target_type = '';
    public $target_value = '';
    public $unbind_target_value_list = [];
    public $finish_target_value_list = [];
    public $monitor_robot_id;
    public $first_exec_time;

    public $status = ADIntelligentMonitorBindModel::RUNNING_STATUS;

    public $creator;

    public function initBySqlData($data)
    {
        $data = (array)$data;
        unset($data['id']);

        $data['unbind_target_value_list'] = json_decode($data["unbind_target_value_list"], true);
        $data['finish_target_value_list'] = json_decode($data["finish_target_value_list"], true);

        foreach ($data as $property_name => $property_value) {
            if (property_exists($this, $property_name)) {
                $this->$property_name = $property_value;
            }
        }

        return $this;
    }

    public function toSqlData(): array
    {
        $data = $this->toArray();
        unset($data['id']);
        $data['unbind_target_value_list'] = json_encode($data['unbind_target_value_list']);
        $data['finish_target_value_list'] = json_encode($data['finish_target_value_list']);

        return $data;
    }

}
