<?php

namespace App\Logic\API;

use App\Container;
use App\Controller\DSP\Controller as DSPController;
use App\Controller\DMS\Controller as DMSController;
use App\Controller\LY\Controller as LYController;
use App\Exception\AppException;
use App\Logic\DSP\MaterialShareRuleLogic;
use App\Logic\UserLogic;
use App\Model\SqlModel\Zeda\MaterialShareRuleModel;
use App\Model\SqlModel\Zeda\RankRouteListModel;
use App\Model\SqlModel\Zeda\RankRoutePermissionModel;
use App\Model\SqlModel\Zeda\RouteModel;
use App\Model\SqlModel\Zeda\RoutePermissionModel;
use App\Model\SqlModel\Zeda\UserLevelModel;
use App\Model\SqlModel\Zeda\UserModel;
use App\Model\SqlModel\Zeda\ViewPositionModel;
use App\Param\RankPermissionParam;
use App\Param\UserParam;
use App\Service\PermissionService;
use App\Service\UserService;
use App\Utils\FrontendTool;
use Common\EnvConfig;
use Exception;
use Throwable;

class PermissionLogic
{
    // 调整权限类型
    const PERMISSION_TYPE_ROUTE = 'route';
    const PERMISSION_TYPE_PLATFORM = 'platform';
    const PERMISSION_TYPE_MATERIAL = 'material';

    /**
     * 新增岗位
     * @param $module
     * @param $level
     * @param $rank_id
     * @param $name
     * @param $platform_agent_leaders array 新增覆盖原来岗位上的负责人
     * @param $route_permission
     * @param $platform_list
     * @return array
     */
    public function addRankPermission($module, $level, $rank_id, $name, $platform_agent_leaders = [], $route_permission = [], $platform_list = [])
    {
        $result = true;
        $msg = '添加成功';
        $data = [];
        try {
            switch ($module) {
                case DSPController::MODULE:
                    $logic = new \App\Logic\DSP\PermissionLogic();
                    break;
                case DMSController::MODULE:
                    $logic = new \App\Logic\DMS\PermissionLogic();
                    break;
                case LYController::MODULE:
                    $logic = new \App\Logic\LY\PermissionLogic();
                    break;
                default:
                    throw new AppException("后台类型错误");
            }

            $service = new PermissionService();

            // 岗位信息
            $card_info = (new ViewPositionModel())->getDataByModuleLevelRank($module, $level, $rank_id);
            if (empty($card_info)) {
                throw new AppException("岗位卡片不存在");
            }

            // 权限
            $route_permission_list = $service->getRankRoutePermissionList($level, $rank_id);
            $platform_list = $logic->getRankPlatformList($level, $rank_id);

            $rank_data = [
                'module' => $module,
                'level' => $level,
                'type' => $card_info['type'],
                'name' => $name,
                'platform_id' => $card_info['platform_id'],
                'department_id' => $card_info['department_id'],
                'department_group_id' => $card_info['department_group_id'],
                'department_group_position_id' => $card_info['department_group_position_id'],
                'department_group_position_worker_id' => $card_info['department_group_position_worker_id'],
                'department_six_id' => $card_info['department_six_id'],
                'department_seven_id' => $card_info['department_seven_id'],
                'department_eight_id' => $card_info['department_eight_id'],
            ];

            $rank_data = array_merge($rank_data, $this->getRoutePermissionData($route_permission_list));
            $rank_data = array_merge($rank_data, $this->getPlatformData($module, $platform_list));

            // DSP独有的协作岗位
            if ($module === DSPController::MODULE) {
                $co_position_list = $logic->getRankCoPositionList($level, $rank_id);
                $rank_data = array_merge($rank_data, $this->getCoPositionData($co_position_list));
            }

            // 新建岗位 替换负责人权限
            if ($platform_agent_leaders) {
                foreach ($rank_data['platform_list'] as $platform_key => $platform_item) {
                    if (isset($platform_agent_leaders[$platform_item['platform']])) {
                        $rank_data['platform_list'][$platform_key]['leader_list'] =
                            $this->getPlatformLeaderData($platform_agent_leaders[$platform_item['platform']], []);
                    }
                }
            }

            // 新建岗位 增加路由权限
            if ($route_permission) {
                if (isset($route_permission['route_list']) && !empty($route_permission['route_list'])) {
                    $tmp_route_list = explode(",", $route_permission['route_list']);
                    $tmp_rank_route_list = explode(",", $rank_data['route_list']);
                    $route_list = array_merge($tmp_route_list, $tmp_rank_route_list);
                    sort($route_list);
                    $route_list = array_unique($route_list);
                    $rank_data['route_list'] = implode(",", $route_list);
                }

                if (isset($route_permission['route_permission_ids']) && !empty($route_permission['route_permission_ids'])) {
                    $route_permission_ids = array_unique(array_merge($route_permission['route_permission_ids'], $rank_data['route_permission_ids']));
                    $rank_data['route_permission_ids'] = $route_permission_ids;
                }
            }

            $param = new RankPermissionParam($rank_data);
            $data = $logic->addRankPermission($param);
        } catch (Throwable $e) {
            $result = false;
            $msg = "添加失败：{$e->getMessage()}";
        }

        return [
            'result' => $result,
            'msg' => $msg,
            'data' => $data
        ];
    }

    /**
     * 格式化路由权限
     * @param $route_permission_list
     * @return array
     */
    private function getRoutePermissionData($route_permission_list)
    {
        $data = [];
        $route_list = [];
        $route_permission_ids = [];

        foreach ($route_permission_list as $element) {
            $route_list[$element[0]] = true;
            $route_list[$element[1]] = true;

            if (count($element) > 2) {
                $route_permission_ids[] = $element[count($element) - 1];
            }
        }

        ksort($route_list);
        $data['route_list'] = implode(',', array_keys($route_list));
        $data['route_permission_ids'] = $route_permission_ids;

        return $data;
    }

    /**
     * 处理平台权限
     * @param $module
     * @param $platform_data
     * @return array
     */
    public function getPlatformData($module, $platform_data)
    {
        $data = [];
        $platform_list = [];

        foreach ($platform_data as $platform) {
            if ($platform['platform'] === '') {
                continue;
            }

            $platform_item = [];
            $platform_item['platform'] = $platform['platform'];

            // 发行特殊处理
            if ($module === LYController::MODULE) {
                $platform_item['game_list'] = $this->getLYPlatformGameData($platform['root_game_list']);
                $platform_item['agent_group_list'] = $this->getLYPlatformAgentData($platform['agent_group_ids']);
            } else {
                $platform_item['game_list'] = $this->getPlatformGameData($platform['root_game_list'], $platform['main_game_list']);
                $platform_item['leader_list'] = $this->getPlatformLeaderData($platform['agent_leaders'], $platform['leader_agent_list']);
                $platform_item['agent_list'] = $this->getPlatformAgentData($platform['agent_group_ids'], $platform['agent_list']);
                $platform_item['leader_group_list'] = $this->getPlatformLeaderGroupData($platform['leader_group_ids']);

                $material_list = $this->getPlatformMaterialData($platform['material_theme_list'], $platform['material_visible_list']);
                $platform_item['material_theme_list'] = $material_list['material_theme_list'];
                $platform_item['material_visible_list'] = $material_list['material_visible_list'];
            }

            $platform_item['is_intersect_leader_and_agent'] = $platform['intersect_leader_and_agent']['value'];
            $platform_item['proxy_type'] = $platform['proxy_type'];

            $platform_list[] = $platform_item;
        }

        $data['platform_list'] = $platform_list;
        return $data;
    }

    /**
     * 处理游戏权限
     * @param $root_game_list
     * @param $main_game_list
     * @return array
     */
    public function getPlatformGameData($root_game_list, $main_game_list)
    {
        $game_list = [];

        // 根游戏
        if ($root_game_list) {
            foreach ($root_game_list as $root_game) {
                $game_list[] = [
                    'root_game_id' => $root_game['root_game_id'],
                    'plat_id' => $root_game['plat_id'],
                    'main_game_list' => []
                ];
            }
        }

        // 主游戏
        if ($main_game_list) {
            foreach ($main_game_list as $main_game) {
                $tmp_main_game_list = [];
                if (isset($main_game['main_game_options'])) {
                    foreach ($main_game['main_game_options'] as $main_game_option) {
                        $tmp_main_game_list[] = [
                            'main_game_id' => $main_game_option['main_game_id'],
                            'plat_id' => $main_game_option['plat_id']
                        ];
                    }
                }
                $game_list[] = [
                    'root_game_id' => $main_game['root_game_id'],
                    'plat_id' => $main_game['plat_id'],
                    'main_game_list' => $tmp_main_game_list
                ];
            }
        }

        return $game_list;
    }

    /**
     * 处理负责人权限
     * @param $agent_leaders
     * @param $leader_agent_list
     * @return array
     */
    public function getPlatformLeaderData($agent_leaders, $leader_agent_list)
    {
        $leader_list = [];

        foreach ($agent_leaders as $agent_leader) {
            $leader_list[] = [
                'agent_leader' => $agent_leader,
                'agent_list' => []
            ];
        }

        foreach ($leader_agent_list as $item) {
            $tmp_agent_list = [];
            if (isset($item['agent_ids'])) {
                foreach ($item['agent_ids'] as $agent_id) {
                    $tmp_agent_list[] = [
                        'agent_id' => $agent_id
                    ];
                }
            }
            $leader_list[] = [
                'agent_leader' => $item['agent_leader'],
                'agent_list' => $tmp_agent_list
            ];
        }

        return $leader_list;
    }

    /**
     * 处理渠道权限
     * @param $agent_group_ids
     * @param $platform_agent_list
     * @return array
     */
    public function getPlatformAgentData($agent_group_ids, $platform_agent_list)
    {
        $agent_list = [];

        foreach ($agent_group_ids as $agent_group_id) {
            $agent_list[] = [
                'agent_group_id' => $agent_group_id,
                'agent_list' => []
            ];
        }

        foreach ($platform_agent_list as $item) {
            $tmp_agent_list = [];
            if (isset($item['agent_ids'])) {
                foreach ($item['agent_ids'] as $agent_id) {
                    $tmp_agent_list[] = [
                        'agent_id' => $agent_id
                    ];
                }
            }
            $agent_list[] = [
                'agent_group_id' => $item['agent_group_id'],
                'agent_list' => $tmp_agent_list
            ];
        }

        return $agent_list;
    }

    /**
     * 处理负责人分组权限
     * @param $leader_group_ids
     * @return array
     */
    public function getPlatformLeaderGroupData($leader_group_ids)
    {
        $leader_group_list = [];

        foreach ($leader_group_ids as $leader_group_id) {
            $leader_group_list[] = [
                'leader_group_id' => $leader_group_id
            ];
        }

        return $leader_group_list;
    }

    /**
     * 处理素材权限
     * @param $platform_material_theme_list
     * @param $platform_material_visible_list
     * @return array
     */
    public function getPlatformMaterialData($platform_material_theme_list, $platform_material_visible_list)
    {
        $material_theme_list = [];
        $material_visible_list = [];

        foreach ($platform_material_theme_list as $item) {
            $item = (array)$item;
            $material_theme_list[] = [
                'theme_id' => $item['theme_id'],
                'type' => $item['type']
            ];
        }

        foreach ($platform_material_visible_list as $item) {
            $item = (array)$item;
            $material_visible_list[] = [
                'cate' => $item['cate'],
                'type' => $item['type'],
            ];
        }

        return [
            'material_theme_list' => $material_theme_list,
            'material_visible_list' => $material_visible_list
        ];
    }

    /**
     * 处理协作岗位权限
     * @param $co_position_list
     * @return array
     */
    public function getCoPositionData($co_position_list)
    {
        $list = [];
        foreach ($co_position_list as $item) {
            $item = (array)$item;
            $list[] = [
                'co_level' => $item['co_level'],
                'co_rank_id' => $item['co_rank_id']
            ];
        }

        return ['co_position_list' => $list];
    }

    /**
     * 处理发行游戏权限
     * @param $root_game_list
     * @return array
     */
    public function getLYPlatformGameData($root_game_list)
    {
        $game_list = [];

        // 根游戏
        if ($root_game_list) {
            foreach ($root_game_list as $root_game) {
                $game_list[] = [
                    'game_id' => $root_game['root_game_id'],
                    'plat_id' => $root_game['plat_id'],
                    'agent_list' => []
                ];
            }
        }

        return $game_list;
    }

    /**
     * 处理发行渠道权限
     * @param $agent_group_ids
     * @return array
     */
    public function getLYPlatformAgentData($agent_group_ids)
    {
        return $agent_group_ids;
    }

    /**
     * 获取岗位的平台权限
     * @param $module
     * @param $level
     * @param $rank_id
     * @return array
     */
    public function getRankPlatformPermission($module, $level, $rank_id)
    {
        switch ($module) {
            case DSPController::MODULE:
                $logic = new \App\Logic\DSP\PermissionLogic();
                break;
            case DMSController::MODULE:
                $logic = new \App\Logic\DMS\PermissionLogic();
                break;
            case LYController::MODULE:
                $logic = new \App\Logic\LY\PermissionLogic();
                break;
            default:
                throw new AppException("后台类型错误");
        }

        $platform_list = $logic->getRankPlatformList($level, $rank_id);

        foreach ($platform_list as $platform_key => $platform_item) {
            $platform_list[$platform_key]['platform_cn'] = EnvConfig::PLATFORM_MAP[$platform_item['platform']];
        }

        return $platform_list;
    }

    /**
     * 获取路由权限可选项
     * @param $module
     * @param $level
     * @param $rank_id
     * @return array
     */
    public function getRoutePermissionOptions($module, $level, $rank_id)
    {
        $route_model = new RouteModel();
        // level 就是超管级别 拿所有
        if (UserService::isSuperManager($level)) {
            $route_item = $route_model->getAllByModule($module);
            $route_permission_list = (new RoutePermissionModel())->getAll();
        } else {
            $route_str = (new RankRouteListModel())->getData($level, $rank_id);
            $route_item = $route_model->getAllByRouteStr($route_str);
            $route_permission_list = (new RankRoutePermissionModel())->getAll($level, $rank_id);
        }
        $route_cascader = $route_model->getCascader($route_item);

        return $this->formatRoutePermissionOptions($route_cascader, FrontendTool::formatRoutePermission($route_permission_list));
    }

    /**
     * 格式化路由权限
     * @param $route_cascader
     * @param $route_permission_list
     * @return array
     */
    public function formatRoutePermissionOptions($route_cascader, $route_permission_list)
    {
        return array_map(function ($father) use ($route_permission_list) {
            $father['route'] = 1;

            if (isset($father['children'])) {
                $children = array_map(function ($children) use ($route_permission_list, $father) {
                    $children['sub_route'] = 1;
                    $children['children'] = [];

                    foreach ($route_permission_list as $item) {
                        if ($item['route_id'] === $children['id']) {
                            $cate_index = array_search($item['cate_name'], array_column($children['children'], 'label'));

                            if ($cate_index === false) {
                                $children['children'][] = [
                                    'id' => $children['id'],
                                    'value' => $item['cate_name'],
                                    'label' => $item['cate_name'],
                                    'children' => []
                                ];
                                $cate_index = count($children['children']) - 1;
                            }

                            if (!empty($item['type'])) {
                                $permission_index = array_search($item['type'], array_column($children['children'][$cate_index]['children'], 'label'));

                                if ($permission_index === false) {
                                    $children['children'][$cate_index]['children'][] = [
                                        'value' => $item['type'],
                                        'label' => $item['type'],
                                        'children' => []
                                    ];
                                    $permission_index = count($children['children'][$cate_index]['children']) - 1;
                                }

                                $children['children'][$cate_index]['children'][$permission_index]['children'][] = [
                                    'id' => $item['id'],
                                    'value' => $item['id'],
                                    'label' => $item['name'],
                                    'route_id' => $children['id'],
                                    'route_pid' => $father['id'],
                                    'route_permission' => 1
                                ];
                            } else {
                                $children['children'][$cate_index]['children'][] = [
                                    'id' => $item['id'],
                                    'value' => $item['id'],
                                    'label' => $item['name'],
                                    'route_id' => $children['id'],
                                    'route_pid' => $father['id'],
                                    'route_permission' => 1
                                ];
                            }
                        }
                    }

                    if (empty($children['children'])) {
                        unset($children['children']);
                    }

                    return $children;
                }, $father['children']);

                $father['children'] = $children;
            }

            return $father;
        }, $route_cascader);
    }

    /**
     * 获取上一级等级和岗位ID
     * @param $module
     * @param $level
     * @param $rank_id
     * @return array
     */
    public function getSingleLeaderLevelRank($module, $level, $rank_id)
    {
        if (UserService::isSuperManager($level) || $level == UserService::LEVEL_PLATFORM) {
            return [0, 0];
        }

        $leader_level = $level - 1;
        $leader_level_column = UserService::getColumnNameByLevel($leader_level);
        $position_info = (new ViewPositionModel())->getDataByModuleLevelRank($module, $level, $rank_id);
        if (empty($position_info)) {
            throw new AppException('找不到该岗位');
        }

        return [$leader_level, $position_info[$leader_level_column] ?? 0];
    }

    /**
     * 同步权限
     * @param $sync_type
     * @param $module
     * @param $user_ids
     * @param $permission_type
     * @param $permission_data
     * @return true
     * @throws Exception
     */
    public function syncPermission($sync_type, $module, $user_ids, $permission_type, $permission_data)
    {
        $user_list = (new UserModel())->getListByModuleUserIds($module, $user_ids);
        $level_rank_list = [];
        // 按岗位分组用户
        $level_rank_user = [];

        foreach ($user_list as $user_info) {
            $level_column = UserService::getColumnNameByLevel($user_info->level);
            $tmp_level_rank = [
                'level' => $user_info->level,
                'rank_id' => $user_info->$level_column
            ];
            $level_rank_list[] = $tmp_level_rank;
            $key = "{$user_info->level}-{$user_info->$level_column}";
            if (!$level_rank_user[$key]) {
                $level_rank_user[$key] = [
                    'level' => $user_info->level,
                    'rank_id' => $user_info->$level_column,
                    'platform_id' => $user_info->platform_id,
                    'department_id' => $user_info->department_id,
                    'department_group_id' => $user_info->department_group_id,
                    'department_group_position_id' => $user_info->department_group_position_id,
                    'department_group_position_worker_id' => $user_info->department_group_position_worker_id,
                    'department_six_id' => $user_info->department_six_id,
                    'department_seven_id' => $user_info->department_seven_id,
                    'department_eight_id' => $user_info->department_eight_id,
                    'user_ids' => [],
                    'user_list' => []
                ];
            }

            $level_rank_user[$key]['user_ids'][] = $user_info->id;
            $level_rank_user[$key]['user_list'][] = (array)$user_info;
        }

        // 岗位下所有用户
        $rank_user_list = (new UserModel())->getAllRankUserInPositionList($level_rank_list, 1);
        // 岗位及下级所有岗位
        $next_level_rank_list = (new ViewPositionModel())->getAllInPositionList($level_rank_list);

        // 判断哪些岗位≥2个账号且没有下级 就要新增岗位 否则要编辑岗位
        $need_add_rank = [];
        $need_edit_rank = [];
        foreach ($level_rank_user as $item) {
            $level_column = UserService::getColumnNameByLevel($item['level']);
            $current_rank_user_list = $rank_user_list
                ->where('level', $item['level'])
                ->where($level_column, $item['rank_id'])
                ->pluck('id')
                ->toArray();

            $next_level_rank_count = $next_level_rank_list
                ->where('level', '>', $item['level'])
                ->where($level_column, $item['rank_id'])
                ->count();

            // 检查本次所选的账号，如果原本在同一岗位卡片里全部选到了，就不用新建岗位
            $choose_diff = array_diff($item['user_ids'], $current_rank_user_list);
            $current_diff = array_diff($current_rank_user_list, $item['user_ids']);

            if ((!empty($choose_diff) || !empty($current_diff)) && $next_level_rank_count < 1) {
                $need_add_rank[] = array_merge($item, [
                    'rank_name' => $next_level_rank_list
                            ->where('level', $item['level'])
                            ->where('rank_id', $item['rank_id'])
                            ->first()
                            ->name ?? ''
                ]);
            } else {
                $need_edit_rank[] = $item;
            }
        }

        // 新增岗位 然后迁移用户
        $user_logic = new UserLogic();
        $material_share_rule_model = new MaterialShareRuleModel();
        $material_share_rule_logic = new MaterialShareRuleLogic();
        $editor = Container::getSession()->name;
        foreach ($need_add_rank as $item) {
            // 岗位名称后缀加-1
            $rank_name = $item['rank_name'];
            $rank_name_arr = explode("-", $rank_name);
            $rank_name_code = $rank_name_arr[1] ?? 0;
            $rank_name_code++;
            $rank_name = "{$rank_name_arr[0]}-{$rank_name_code}";

            // 复制岗位 然后在下面统一调整权限
            $result = $this->addRankPermission(
                $module, $item['level'], $item['rank_id'], $rank_name
            );

            if ($result['result']) {
                $new_level_column = UserService::getColumnNameByLevel($item['level']);
                foreach ($item['user_list'] as $user_info) {
                    // 迁移用户
                    $param_data = array_merge([
                        'id' => $user_info['id'],
                        'module' => $user_info['module'],
                        'account' => $user_info['account'],
                        'name' => $user_info['name'],
                        'mobile' => $user_info['mobile'],
                        'leader' => 1,
                        'platform_id' => $user_info['platform_id'],
                        'department_id' => $user_info['department_id'],
                        'department_group_id' => $user_info['department_group_id'],
                        'department_group_position_id' => $user_info['department_group_position_id'],
                        'department_group_position_worker_id' => $user_info['department_group_position_worker_id'],
                        'department_six_id' => $user_info['department_six_id'],
                        'department_seven_id' => $user_info['department_seven_id'],
                        'department_eight_id' => $user_info['department_eight_id'],
                        'editor' => $editor
                    ], [
                        'level' => $result['data']['level'],
                        $new_level_column => $result['data']['rank_id']
                    ]);

                    $user_param = new UserParam($param_data);
                    $user_logic->editUser($user_param);
                }

                // 编辑相关交付岗位的素材共享规则
                $search_m_group = json_encode(['level' => $item['level'], 'rank_id' => $item['rank_id']]);
                $material_share_rule_list = $material_share_rule_model->getListByMGroup($search_m_group);
                $new_level_rank = [
                    'level' => $result['data']['level'],
                    'rank_id' => $result['data']['rank_id'],
                ];

                foreach ($material_share_rule_list as $rule_info) {
                    $rule_m_group = json_decode($rule_info->m_group, true);
                    $rule_m_group[] = $new_level_rank;
                    $material_share_rule_logic->editShareRule([
                        'id' => $rule_info->id,
                        'rule_type' => $rule_info->rule_type,
                        'm_group' => $rule_m_group
                    ]);
                }

                // 进编辑岗位同步权限中
                $need_edit_rank[] = array_merge($item, [
                    'level' => $result['data']['level'],
                    'rank_id' => $result['data']['rank_id'],
                    $new_level_column => $result['data']['rank_id']
                ]);
            } else {
                throw new AppException("岗位：{$rank_name} 新增失败，{$result['msg']}");
            }
        }

        // 编辑岗位 同步权限
        $position_list = [];
        foreach ($need_edit_rank as $item) {
            $tmp_position_arr = [];
            for ($i = 1; $i <= $item['level']; $i++) {
                $level_column = UserService::getColumnNameByLevel($i);
                $level_rank_id = $item[$level_column] ?? 0;
                if ($level_rank_id != 0) {
                    $tmp_position_arr[] = $level_rank_id;
                }
            }
            $position_list[] = implode("-", $tmp_position_arr);
        }

        $position_list = array_unique($position_list);

        switch ($permission_type) {
            case self::PERMISSION_TYPE_ROUTE:
                $param = new RankPermissionParam([
                    'sync_type' => $sync_type,
                    'position_list' => $position_list,
                    'route_list' => $permission_data['route_list'],
                    'route_permission_ids' => $permission_data['route_permission_ids'],
                    'super_manager_operate' => true
                ]);
                break;
            case self::PERMISSION_TYPE_PLATFORM:
            case self::PERMISSION_TYPE_MATERIAL:
                $param = new RankPermissionParam([
                    'sync_type' => $sync_type,
                    'position_list' => $position_list,
                    'platform_list' => $permission_data['platform_list'],
                    'super_manager_operate' => true
                ]);
                break;
            default:
                throw new AppException("错误的权限类型");
        }

        switch ($module) {
            case DSPController::MODULE:
                $permission_logic = new \App\Logic\DSP\PermissionLogic();
                break;
            case DMSController::MODULE:
                $permission_logic = new \App\Logic\DMS\PermissionLogic();
                break;
            case LYController::MODULE:
                $permission_logic = new \App\Logic\LY\PermissionLogic();
                break;
            default:
                throw new AppException("后台类型错误");
        }

        if ($sync_type === 'add') {
            $permission_logic->syncAddRankPermission($param);
        } else {
            $permission_logic->syncDeleteRankPermission($param);
        }

        return true;
    }

    /**
     * 路由权限调整
     * @param $sync_type
     * @param $module
     * @param $user_ids
     * @param $route_list
     * @param $route_permission_ids
     * @return true
     * @throws Exception
     */
    public function syncRoutePermission($sync_type, $module, $user_ids, $route_list, $route_permission_ids)
    {
        return $this->syncPermission(
            $sync_type,
            $module,
            $user_ids,
            self::PERMISSION_TYPE_ROUTE,
            [
                'route_list' => $route_list,
                'route_permission_ids' => $route_permission_ids
            ]
        );
    }

    /**
     * 数据权限调整
     * @param $sync_type
     * @param $module
     * @param $user_ids
     * @param $platform_list
     * @return true
     * @throws Exception
     */
    public function syncPlatformPermission($sync_type, $module, $user_ids, $platform_list)
    {
        $platform_list = $this->getPlatformData($module, $platform_list)['platform_list'];
        return $this->syncPermission(
            $sync_type,
            $module,
            $user_ids,
            self::PERMISSION_TYPE_PLATFORM,
            [
                'platform_list' => $platform_list
            ]
        );
    }

    /**
     * 同步素材权限
     * @param $sync_type
     * @param $module
     * @param $user_ids
     * @param $platform_list
     * @return true
     * @throws Exception
     */
    public function syncMaterialPermission($sync_type, $module, $user_ids, $platform_list)
    {
        // 组装素材权限
        $platform_material_list = [];
        foreach ($platform_list as $platform_item) {
            $tmp_material_theme_list = [];
            if (isset($platform_item['material_store_theme']) && !empty($platform_item['material_store_theme'])) {
                $tmp_material_theme_list = array_merge($tmp_material_theme_list, $platform_item['material_store_theme']);
            }

            if (isset($platform_item['material_effect_theme']) && !empty($platform_item['material_effect_theme'])) {
                $tmp_material_theme_list = array_merge($tmp_material_theme_list, $platform_item['material_effect_theme']);
            }

            $tmp_material_theme_list = $this->getPlatformMaterialData($tmp_material_theme_list, []);
            $platform_material_list[] = [
                'platform' => $platform_item['platform'],
                'material_theme_list' => $tmp_material_theme_list['material_theme_list'],
                'material_visible_list' => $tmp_material_theme_list['material_visible_list']
            ];
        }

        return $this->syncPermission(
            $sync_type,
            $module,
            $user_ids,
            self::PERMISSION_TYPE_MATERIAL,
            [
                'platform_list' => $platform_material_list
            ]
        );
    }

    /**
     * 通过用户ID获取后台岗位
     * @param $module
     * @param $user_id
     * @return array
     */
    public function getLevelRankByModuleUserId($module, $user_id)
    {
        $user_level = (new UserLevelModel())->getUserLevel($user_id);
        if ($user_level->isNotEmpty()) {
            $module_level_info = (array)$user_level->where('module', $module)->first();
            if (!empty($module_level_info)) {
                $level = $module_level_info['level'];
                $rank_id = (new PermissionService())->getUserRankId($module_level_info);
            } else {
                throw new AppException("获取异常，user_id:{$user_id}没有{$module}权限，无法获取");
            }
        } else {
            throw new AppException("获取异常，user_id:{$user_id}没有权限，无法获取");
        }

        return [$level, $rank_id];
    }

    /**
     * 删除用户
     * @param $user_ids
     * @return void
     * @throws Exception
     */
    public function deleteUser($user_ids)
    {
        $user_logic = new UserLogic();
        $user_name = 'BPM系统'; // 固定修改人
        foreach ($user_ids as $user_id) {
            $user_logic->deleteUser($user_id, $user_name);
        }
    }

    /**
     * 判断用户是否在当前模块存在
     * @param UserParam $param
     * @return bool
     */
    public function checkUserExist(UserParam $param)
    {
        $user_info = (new UserLogic())->getUserInfoByAccount($param->account);
        if (!$user_info) {
            return false;
        }

        $user_level = (new UserLevelModel())->getUserLevel($user_info['id']);

        return $user_level->where('module', '=', $param->module)->isNotEmpty();
    }
}
