<?php

namespace App\Logic\DSP;

use App\Container;
use App\Model\SqlModel\DataMedia\DimensionMonitorRobotFilterModel;
use App\Model\SqlModel\Zeda\ADIntelligentMonitorRobotLogModel;
use App\Model\SqlModel\Zeda\DimensionMonitorRobotLogModel;
use App\Model\SqlModel\Zeda\DimensionMonitorRobotModel;
use App\Param\ADServing\DimensionMonitorRobotFilterParam;
use App\Param\ADServing\DimensionMonitorRobotLogSearchParam;
use App\Param\ADServing\DimensionMonitorRobotParam;
use App\Param\ADServing\DimensionMonitorRobotSearchParam;
use App\Service\UserService;

class DimensionMonitorRobotLogic
{
    /**
     * @param array $input
     * @return array
     */
    public function previewDimensionMonitorRobot(array $input)
    {
        $fp = new DimensionMonitorRobotFilterParam($input);

        $filter_model = new DimensionMonitorRobotFilterModel();

        $permission_logic = new PermissionLogic();

        $agent_game_permission_data = $permission_logic->getDataPermissionByUserId(Container::getSession()->user_id);
        $material_user_permission_data = $permission_logic->getMaterialPermissionAndUserOptionsByUserId(Container::getSession()->user_id);
        $agent_permission = $agent_game_permission_data['agent_permission'];
        $game_permission = $agent_game_permission_data['game_permission'];

        $user_list = $material_user_permission_data['user_list']->pluck('name')->toArray();

        $result = $filter_model->getFilter($fp, $user_list, $agent_permission, $game_permission);

        return (new ADFilterCalcLogic())->getMaterialFileFilterSumAndFormat($result, $fp->media_type);
    }

    public function getDimensionMonitorRobot(DimensionMonitorRobotSearchParam $param)
    {
        $user_list = UserService::isSuperManager() ? [] : [Container::getSession()->name];

        $exec_body_model = new DimensionMonitorRobotModel();

        $data = $exec_body_model->getList($param, $user_list);

        foreach ($data['list'] as $value) {
            $value->filter = json_decode($value->filter, true);
            $value->calc = json_decode($value->calc, true);
            $value->target = json_decode($value->target, true);
            $value->order = json_decode($value->order, true);
        }

        return $data;
    }

    public function addDimensionMonitorRobot(DimensionMonitorRobotParam $param)
    {
        return (new DimensionMonitorRobotModel())->addDimensionMonitorRobot($param);
    }

    public function updateDimensionMonitorRobot($id, DimensionMonitorRobotParam $param): int
    {
        $user_list = UserService::isSuperManager() ? [] : [Container::getSession()->name];
        $exec_body_model = new DimensionMonitorRobotModel();
        return $exec_body_model->updateDimensionMonitorRobot($id, $param, $user_list);
    }

    public function getDimensionMonitorRobotLog(DimensionMonitorRobotLogSearchParam $param)
    {
        $data = (new DimensionMonitorRobotLogModel())->getList($param);
        return $data;
    }
}
