<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/05/07
 * Time: 10:29
 */

namespace App\Logic\DSP;

use App\Utils\Math;
use Illuminate\Support\Collection;

class ADFilterCalcLogic
{
    private function formatValue($list)
    {
        foreach ($list as $i_key => &$item) {
            foreach ($item as $key => &$value) {
                switch ($key) {
                    case 'tencent_approved_rate':
                    case 'tencent_7day_approved':
                    case 'first_day_pay_rate':
                    case 'rate_day_stay_2':
                    case 'rate_day_stay_3':
                    case 'rate_day_stay_7':
                    case 'rate_day_stay_15':
                    case 'rate_day_roi_2':
                    case 'rate_day_roi_3':
                    case 'rate_day_roi_7':
                    case 'rate_day_roi_15':
                    case 'rate_day_roi_30':
                    case 'rate_day_three_login_uid_count':
                    case 'rate_day_old_root_game_reg_uid':
                    case 'rate_day_old_clique_game_reg_uid':
                    case 'tt_wifi_play_rate':
                    case 'tt_play_over_rate':
                    case 'tt_valid_play_rate':
                    case 'tt_loan_completion_rate':
                    case 'tt_install_finish_rate':
                    case 'tt_download_start_rate':
                    case 'tt_deep_convert_rate':
                    case 'tt_download_finish_rate':
                    case 'tt_active_register_rate':
                    case 'tt_active_rate':
                    case 'conversion_rate':
                    case 'active_pay_rate':
                    case 'click_rate':
                    case 'reg_rate':
                    case 'active_rate':
                    case 'convert_rate':
                    case 'cost_process':
                    case 'first_day_roi':
                    case 'total_roi':
                    case 'approved_rate':
                    case 'action_uid_reg_rate':
                    case 'tx_cheout_om_reward':
                    case 'tx_click_activated_rate':
                    case 'tx_activate_register_rate':
                    case 'tx_mini_game_register_rate':
                    case 'tx_key_behavior_conversions_rate':
                    case 'tx_app_retention_d3_rate':
                    case 'tx_mini_game_retention_d1_rate':
                    case 'tx_purchase_clk_rate':
                    case 'atx_purchase_act_rate':
                    case 'tx_purchase_roi':
                    case 'tx_cheout_1d_rate':
                    case 'tx_cheout_fd_reward':
                    case 'tx_cheout_3d_rate':
                    case 'tx_cheout_td_reward':
                    case 'tx_cheout_5d_rate':
                    case 'tx_cheout_7d_rate':
                    case 'tx_cheout_ow_reward':
                    case 'tx_cheout_tw_reward':
                        $value = ($value * 100) . '%';
                        break;
                    case  'size':
                        $value = round(($value / 1024 / 1024), 4);
                        break;
                    case  'duration':
                        $value = round($value, 2);
                        break;
                }
            }
        }
        return $list;
    }

    /**
     * 非单一特殊处理的合计项
     * @param $sum
     * @param $media_type
     * @return mixed
     */
    private function specialSum($sum, $media_type)
    {
        // 计算非单一合计的指标
        foreach ($sum as $sum_target => &$sum_value) {
            switch ($sum_target) {
                case 'first_day_ltv' :
                    $sum_value = Math::div($sum['sum_first_day_pay_money'], $sum['sum_day_reg_count']);
                    break;
                case 'total_ltv' :
                    $sum_value = Math::div($sum['sum_total_pay_money'], $sum['sum_day_reg_count']);
                    break;
                case 'first_day_pay_rate' :
                    $sum_value = Math::div($sum['sum_first_day_pay_count'], $sum['sum_day_reg_count']);
                    break;
                case 'cost_per_first_day_pay' :
                    $sum_value = Math::div($sum['sum_cost'], $sum['sum_first_day_pay_count']);
                    break;
                case 'first_day_roi' :
                    $sum_value = Math::div($sum['sum_first_day_pay_money'], $sum['sum_cost']);
                    break;
                case 'total_roi' :
                    $sum_value = Math::div($sum['sum_total_pay_money'], $sum['sum_cost']);
                    break;
                case 'first_day_arppu' :
                    $sum_value = Math::div($sum['sum_first_day_pay_money'], $sum['sum_first_day_pay_count']);
                    break;
                case 'total_arppu' :
                    $sum_value = Math::div($sum['sum_total_pay_money'], $sum['sum_total_pay_count']);
                    break;
                case 'action_uid_reg_rate' :
                    $sum_value = Math::div($sum['sum_day_reg_muid_distinct_count'], $sum['sum_day_action_muid_distinct_count']);
                    break;
                case 'click_rate' :
                    $sum_value = Math::div($sum['sum_click'], $sum['sum_show']);
                    break;
                case 'cost_per_convert' :
                    $sum_value = Math::div($sum['sum_cost'], $sum['sum_convert']);
                    break;
                case 'cost_per_reg' :
                    $sum_value = Math::div($sum['sum_cost'], $sum['reg_uid_count']);
                    break;
                case 'cost_per_active' :
                    $sum_value = Math::div($sum['sum_cost'], $sum['sum_active']);
                    break;
                case 'cost_per_pay' :
                    $sum_value = Math::div($sum['sum_cost'], $sum['sum_pay_count']);
                    break;
                case 'cpm' :
                    $sum_value = Math::div($sum['sum_cost'], Math::div($sum['sum_show'], 1000));
                    break;
                case 'cpc' :
                    $sum_value = Math::div($sum['sum_cost'], $sum['sum_click']);
                    break;
                case 'convert_rate' :
                    $sum_value = Math::div($sum['sum_convert'], $sum['sum_click']);
                    break;
                case 'reg_rate' :
                    $sum_value = Math::div($sum['sum_register'], $sum['sum_click']);
                    break;
                case 'active_rate' :
                    $sum_value = Math::div($sum['sum_active'], $sum['sum_click']);
                    break;
                case 'pay_rate' :
                    $sum_value = Math::div($sum['sum_pay_count'], $sum['sum_register']);
                    break;
                case 'media_cost_per_reg' :
                    $sum_value = Math::div($sum['sum_cost'], $sum['sum_register']);
                    break;
                case 'rate_day_stay_2' :
                    $sum_value = Math::div($sum['sum_day_second_login_count'], $sum['sum_day_reg_uid_count']);
                    break;
                case 'rate_day_stay_3' :
                    $sum_value = Math::div($sum['sum_day_third_login_count'], $sum['reg_uid_count']);
                    break;
                case 'rate_day_stay_7' :
                    $sum_value = Math::div($sum['sum_day_seventh_login_count'], $sum['reg_uid_count']);
                    break;
                case 'rate_day_stay_15' :
                    $sum_value = Math::div($sum['sum_day_fifteenth_login_count'], $sum['reg_uid_count']);
                    break;
                case 'rate_day_roi_2' :
                    $sum_value = Math::div($sum['sum_second_day_pay_money'], $sum['sum_cost']);
                    break;
                case 'rate_day_roi_3' :
                    $sum_value = Math::div($sum['sum_third_day_pay_money'], $sum['sum_cost']);
                    break;
                case 'rate_day_roi_7' :
                    $sum_value = Math::div($sum['sum_seventh_day_pay_money'], $sum['sum_cost']);
                    break;
                case 'rate_day_roi_15' :
                    $sum_value = Math::div($sum['sum_fifteenth_day_pay_money'], $sum['sum_cost']);
                    break;
                case 'rate_day_roi_30' :
                    $sum_value = Math::div($sum['sum_thirty_day_pay_money'], $sum['sum_cost']);
                    break;
                case 'rate_day_three_login_uid_count':
                    $sum_value = Math::div($sum['sum_day_three_login_uid_count'], $sum['reg_uid_count']);
                    break;
                case 'rate_day_old_root_game_reg_uid':
                    $sum_value = Math::div($sum['sum_day_old_root_game_reg_uid_count'], $sum['sum_day_reg_muid_count']);
                    break;
                case 'rate_day_old_clique_game_reg_uid':
                    $sum_value = Math::div($sum['sum_day_old_clique_game_reg_uid_count'], $sum['sum_day_reg_muid_count']);
                    break;
                case 'cost_process':
                    $sum_value = Math::div($sum['sum_ori_cost_for_cost_process'], $sum['sum_budget_for_cost_process']);
                    break;
                case 'tx_activated_cost':
                    $sum_value = Math::div($sum['tx_sum_ori_cost'], $sum['tx_sum_activated_count']);
                    break;
                case 'tx_click_activated_rate':
                    $sum_value = Math::div($sum['tx_sum_activated_count'], $sum['tx_sum_valid_click_count']);
                    break;
                case 'tx_reg_cost':
                    $sum_value = Math::div($sum['tx_sum_ori_cost'], $sum['tx_sum_reg_pv']);
                    break;
                case 'tx_reg_clk_rate':
                    $sum_value = Math::div($sum['tx_sum_reg_pv'], $sum['tx_sum_valid_click_count']);
                    break;
                case 'tx_activate_register_rate':
                    $sum_value = Math::div($sum['tx_sum_reg_pv'], $sum['tx_sum_activated_count']);
                    break;
                case 'tx_mini_game_register_cost':
                    $sum_value = Math::div($sum['tx_sum_ori_cost'], $sum['tx_sum_mini_game_register_users']);
                    break;
                case 'tx_mini_game_register_rate':
                    $sum_value = Math::div($sum['tx_sum_mini_game_register_users'], $sum['tx_sum_valid_click_count']);
                    break;
                case 'tx_first_pay_cost':
                    $sum_value = Math::div($sum['tx_sum_ori_cost'], $sum['tx_sum_first_pay_count']);
                    break;
                case 'tx_first_pay_rate':
                    $sum_value = Math::div($sum['tx_sum_first_pay_count'], $sum['tx_sum_activated_count']);
                    break;
                case 'tx_key_behavior_conversions_cost':
                    $sum_value = Math::div($sum['tx_sum_ori_cost'], $sum['tx_sum_key_behavior_conversions_count']);
                    break;
                case 'tx_key_behavior_conversions_rate':
                    $sum_value = Math::div($sum['tx_sum_key_behavior_conversions_count'], $sum['tx_sum_valid_click_count']);
                    break;
                case 'tx_mini_game_create_role_cost':
                    $sum_value = Math::div($sum['tx_sum_ori_cost'], $sum['tx_sum_mini_game_create_role_users']);
                    break;
                case 'tx_retention_cost':
                    $sum_value = Math::div($sum['tx_sum_ori_cost'], $sum['tx_sum_retention_count']);
                    break;
                case 'tx_retention_rate':
                    $sum_value = Math::div($sum['tx_sum_retention_count'], $sum['tx_sum_activated_count']);
                    break;
                case 'tx_app_retention_d3_cost':
                    $sum_value = Math::div($sum['tx_sum_ori_cost'], $sum['tx_sum_app_retention_d3_uv']);
                    break;
                case 'tx_app_retention_d3_rate':
                    $sum_value = Math::div($sum['tx_sum_app_retention_d3_uv'], $sum['tx_sum_activated_count']);
                    break;
                case 'tx_app_retention_d5_cost':
                    $sum_value = Math::div($sum['tx_sum_ori_cost'], $sum['tx_sum_app_retention_d5_uv']);
                    break;
                case 'app_retention_d7_cost':
                    $sum_value = Math::div($sum['tx_sum_ori_cost'], $sum['tx_sum_app_retention_d7_uv']);
                    break;
                case 'tx_app_retention_d7_rate':
                    $sum_value = Math::div($sum['tx_sum_app_retention_d7_uv'], $sum['tx_sum_activated_count']);
                    break;
                case 'tx_app_retention_lt7_cost':
                    $sum_value = Math::div($sum['tx_sum_ori_cost'], $sum['tx_sum_app_retention_lt7']);
                    break;
                case 'tx_mini_game_retention_d1_cost':
                    $sum_value = Math::div($sum['tx_sum_ori_cost'], $sum['tx_sum_mini_game_retention_d1']);
                    break;
                case 'tx_mini_game_retention_d1_rate':
                    $sum_value = Math::div($sum['tx_sum_mini_game_retention_d1'], $sum['tx_sum_valid_click_count']);
                    break;
                case 'tx_purchase_cost':
                    $sum_value = Math::div($sum['tx_sum_ori_cost'], $sum['tx_sum_purchase_pv']);
                    break;
                case 'tx_app_purchase_clk_rate':
                    $sum_value = Math::div($sum['tx_sum_purchase_pv'], $sum['tx_sum_valid_click_count']);
                    break;
                case 'tx_purchase_act_arpu':
                case 'tx_purchase_act_rate':
                    $sum_value = Math::div($sum['tx_sum_purchase_pv'], $sum['tx_sum_activated_count']);
                    break;
                case 'tx_purchase_roi':
                    $sum_value = Math::div($sum['tx_sum_purchase_amount'], $sum['tx_sum_ori_cost']);
                    break;
                case 'tx_purchase_reg_arpu':
                    $sum_value = Math::div($sum['tx_sum_purchase_amount'], $sum['tx_sum_reg_pv']);
                    break;
                case 'tx_purchase_reg_arppu':
                    $sum_value = Math::div($sum['tx_sum_purchase_amount'], $sum['tx_sum_purchase_pv']);
                    break;
                case 'tx_cheout_1d_cost':
                    $sum_value = Math::div($sum['tx_sum_ori_cost'], $sum['tx_sum_cheout_pv_1d']);
                    break;
                case 'tx_cheout_1d_rate':
                    $sum_value = Math::div($sum['tx_sum_cheout_pv_1d'], $sum['tx_sum_valid_click_count']);
                    break;
                case 'tx_cheout_fd_reward':
                    $sum_value = Math::div($sum['tx_sum_cheout_fd'], $sum['tx_sum_ori_cost']);
                    break;
                case 'tx_cheout_3d_cost':
                    $sum_value = Math::div($sum['tx_sum_ori_cost'], $sum['tx_sum_cheout_pv_3d']);
                    break;
                case 'tx_cheout_3d_rate':
                    $sum_value = Math::div($sum['tx_sum_cheout_pv_3d'], $sum['tx_sum_valid_click_count']);
                    break;
                case 'tx_cheout_td_reward':
                    $sum_value = Math::div($sum['tx_sum_cheout_td'], $sum['tx_sum_ori_cost']);
                    break;
                case 'tx_cheout_5d_rate':
                    $sum_value = Math::div($sum['tx_sum_cheout_pv_5d'], $sum['tx_sum_valid_click_count']);
                    break;
                case 'tx_cheout_5d_cost':
                    $sum_value = Math::div($sum['tx_sum_ori_cost'], $sum['tx_sum_cheout_pv_7d']);
                    break;
                case 'tx_cheout_7d_cost':
                    $sum_value = Math::div($sum['tx_sum_cheout_pv_7d'], $sum['tx_sum_valid_click_count']);
                    break;
                case 'tx_cheout_ow_reward':
                    $sum_value = Math::div($sum['tx_sum_cheout_ow'], $sum['tx_sum_ori_cost']);
                    break;
                case 'tx_cheout_tw_reward':
                    $sum_value = Math::div($sum['tx_sum_cheout_tw'], $sum['tx_sum_ori_cost']);
                    break;
                case 'tx_cheout_15d_reward':
                    $sum_value = Math::div($sum['tx_sum_cheout_15d'], $sum['tx_sum_ori_cost']);
                    break;
                case 'tx_cheout_om_reward':
                    $sum_value = Math::div($sum['tx_sum_cheout_om'], $sum['tx_sum_ori_cost']);
                    break;
                case 'tx_mini_game_paying_arpu':
                    $sum_value = Math::div($sum['tx_sum_purchase_amount'], $sum['tx_sum_mini_game_register_users']);
                    break;

            }

            if ($media_type == 1) {
                switch ($sum_target) {
                    // 头条媒体特殊字段
                    case 'tt_loan_completion_cost':
                        $sum_value = Math::div($sum['tt_sum_ori_cost'], $sum['tt_sum_loan_completion']);
                        break;
                    case 'tt_loan_completion_rate':
                        $sum_value = Math::div($sum['tt_sum_loan_completion'], $sum['tt_sum_register']);
                        break;
                    case 'tt_download_finish_cost':
                        $sum_value = Math::div($sum['tt_sum_ori_cost'], $sum['tt_sum_download_finish']);
                        break;
                    case 'tt_install_finish_cost':
                        $sum_value = Math::div($sum['tt_sum_ori_cost'], $sum['tt_sum_install_finish']);
                        break;
                    case 'tt_download_start_rate':
                        $sum_value = Math::div($sum['tt_sum_download_start'], $sum['tt_sum_click']);
                        break;
                    case 'tt_install_finish_rate':
                        $sum_value = Math::div($sum['tt_sum_install_finish'], $sum['tt_sum_download_start']);
                        break;
                    case 'tt_deep_convert_rate':
                        $sum_value = Math::div($sum['tt_sum_deep_convert'], $sum['tt_sum_convert']);
                        break;
                    case 'tt_download_finish_rate':
                        $sum_value = Math::div($sum['tt_sum_download_finish'], $sum['tt_sum_download_start']);
                        break;
                    case 'tt_next_day_open_rate':
                        $sum_value = Math::div($sum['tt_sum_next_day_open'], $sum['tt_sum_active']);
                        break;
                    case 'tt_next_day_open_cost':
                        $sum_value = Math::div($sum['tt_sum_ori_cost'], $sum['tt_sum_next_day_open']);
                        break;
                    case 'tt_active_rate':
                        $sum_value = Math::div($sum['tt_sum_active'], $sum['tt_sum_click']);
                        break;
                    case 'tt_game_addiction_rate':
                        $sum_value = Math::div($sum['tt_sum_game_addiction'], $sum['tt_sum_active']);
                        break;
                    case 'tt_game_addiction_cost':
                        $sum_value = Math::div($sum['tt_sum_ori_cost'], $sum['tt_sum_game_addiction']);
                        break;
                    case 'tt_wifi_play_rate':
                        $sum_value = Math::div($sum['tt_sum_wifi_play'], $sum['tt_sum_total_play']);
                        break;
                    case 'tt_play_over_rate':
                        $sum_value = Math::div($sum['tt_sum_play_over'], $sum['tt_sum_total_play']);
                        break;
                    case 'tt_valid_play_cost':
                        $sum_value = Math::div($sum['tt_sum_ori_cost'], $sum['tt_sum_valid_play']);
                        break;
                    case 'tt_valid_play_rate':
                        $sum_value = Math::div($sum['tt_sum_valid_play'], $sum['tt_sum_total_play']);
                        break;
                    case 'tt_deep_convert_cost':
                        $sum_value = Math::div($sum['tt_sum_ori_cost'], $sum['tt_sum_deep_convert']);
                        break;
                    case 'tt_download_start_cost':
                        $sum_value = Math::div($sum['tt_sum_ori_cost'], $sum['tt_sum_download_start']);
                        break;
                }
            }
        }
        return $sum;
    }

    /**
     * 单一累计即可的合计项
     * @param $list
     * @param $sum
     * @return mixed
     */
    private function commonSum(&$list, $sum)
    {
        foreach ($list as $index => &$item) {
            foreach ($sum as $target => $target_value) {
                if (is_numeric($item->$target)) {
                    $sum[$target] = bcadd($sum[$target], $item->$target, 2);
                } else {
                    $item->$target = 0;
                }
            }
        }
        return $sum;
    }

    /**
     * 创建一个初始化全部指标为0的合计项
     * @param $targets
     * @return array
     */
    private function makeZeroSum($targets)
    {
        $sum = [];
        foreach ($targets as $target) {
            $sum[$target] = 0;
        }
        return $this->unsetSumTarget($sum);
    }

    /**
     * 删除不应该有的合计指标
     * @param $sum
     * @return mixed
     */
    private function unsetSumTarget($sum)
    {
        unset($sum['account_id']);
        unset($sum['agent_leader']);
        unset($sum['ad_name']);
        unset($sum['campaign_id']);
        unset($sum['ad_id']);
        unset($sum['audience_md5']);
        unset($sum['ad_create_time']);
        unset($sum['account_name']);
        unset($sum['account_leader']);
        unset($sum['platform']);
        unset($sum['targeting_id']);
        unset($sum['filename']);
        unset($sum['id']);
        unset($sum['material_id']);
        unset($sum['file_type']);
        unset($sum['url']);
        unset($sum['width']);
        unset($sum['height']);
        unset($sum['title']);
        unset($sum['create_time']);
        unset($sum['material_name']);
        unset($sum['material_id']);
        unset($sum['author']);
        unset($sum['c_author']);
        unset($sum['a_author']);
        unset($sum['m1_author']);
        unset($sum['m2_author']);
        unset($sum['m3_author']);
        unset($sum['m4_author']);
        unset($sum['actor']);
        unset($sum['shoot']);
        unset($sum['uploader']);
        unset($sum['web_creator']);
        unset($sum['signature']);
        unset($sum['agent_group_name']);
        unset($sum['bitrate']);
        unset($sum['approved_rate']);
        unset($sum['effect_grade30']);
        unset($sum['effect_grade7']);
        unset($sum['ad1_id']);
        unset($sum['ad2_id']);
        unset($sum['ad3_id']);
        unset($sum['ad4_id']);
        return $sum;
    }

    /**
     * 获取账号筛选的合计项和格式化数据
     * @param $result
     * @param $media_type
     * @return array
     */
    public function getMediaAccountFilterSumAndFormat($result, $media_type)
    {
        /* @var Collection $list */
        $list = $result['list'];
        // 处理合击项
        if ($list->isNotEmpty()) {
            $sum = $this->makeZeroSum(array_keys((array)$list[0]));
            // 计算所有指标的合计项
            $sum = $this->commonSum($list, $sum);
            $sum = $this->specialSum($sum, $media_type);
            $sum['account_id'] = '合计';
            $result['sum'] = $this->formatValue([$sum])[0];
        } else {
            $result['sum'] = [];
        }
        $list = $this->formatValue($list);
        $result['list'] = $list;
        return $result;
    }

    /**
     * 获取定向筛选的合计项和格式化数据
     * @param $result
     * @param $media_type
     * @return array
     */
    public function getTargetingFilterSumAndFormat($result, $media_type)
    {
        /* @var Collection $list */
        $list = $result['list'];

        // 处理合击项
        if ($list->isNotEmpty()) {
            $sum = $this->makeZeroSum(array_keys((array)$list[0]));
            // 计算所有指标的合计项
            $sum = $this->commonSum($list, $sum);
            $sum = $this->specialSum($sum, $media_type);
            $sum['targeting_id'] = '合计';
            $sum['account_id'] = '';
            $sum['ad_id'] = '';
            $sum['ad_name'] = '';
            $sum['campaign_id'] = '';
            $result['sum'] = $this->formatValue([$sum])[0];
        } else {
            $result['sum'] = [];
        }
        $list = $this->formatValue($list);
        $result['list'] = $list;
        return $result;
    }

    /**
     * 获取素材文件筛选的合计项和格式化数据
     * @param $result
     * @param $media_type
     * @return array
     */
    public function getMaterialFileFilterSumAndFormat($result, $media_type)
    {
        /* @var Collection $list */
        $list = $result['list'];

        // 处理合击项
        if ($list->isNotEmpty()) {
            $sum = $this->makeZeroSum(array_keys((array)$list[0]));
            // 计算所有指标的合计项
            $sum = $this->commonSum($list, $sum);
            $sum = $this->specialSum($sum, $media_type);
            $sum['id'] = '合计';
            $sum['ad1_id'] = '合计';
            $sum['ad2_id'] = '合计';
            $sum['ad3_id'] = '合计';
            $sum['ad4_id'] = '合计';
            $sum['account_id'] = '合计';
            $sum['media_type'] = '';
            $sum['agent_leader'] = '';
//            $sum['width'] = '合计';
//            $sum['height'] = '合计';
            $result['sum'] = $this->formatValue([$sum])[0];
        } else {
            $result['sum'] = [];
        }
        $list = $this->formatValue($list);
        $result['list'] = $list;
        return $result;
    }

    /**
     * 获取文案筛选的合计项和格式化数据
     * @param $result
     * @param $media_type
     * @return array
     */
    public function getWordContentFilterSumAndFormat($result, $media_type)
    {
        /* @var Collection $list */
        $list = $result['list'];

        // 处理合击项
        if ($list->isNotEmpty()) {
            $sum = $this->makeZeroSum(array_keys((array)$list[0]));
            // 计算所有指标的合计项
            $sum = $this->commonSum($list, $sum);
            $sum = $this->specialSum($sum, $media_type);
            $sum['title'] = '合计';
            $result['sum'] = $this->formatValue([$sum])[0];
        } else {
            $result['sum'] = [];
        }
        $list = $this->formatValue($list);
        $result['list'] = $list;
        return $result;
    }
}
