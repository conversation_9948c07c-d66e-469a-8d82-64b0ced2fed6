<?php
/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON>.
 * User: Melody
 * Date: 2020/5/18
 * Time: 11:34
 */

namespace App\Logic\DMS;


use App\Constant\BillTemplate;
use App\Constant\Environment;
use App\Constant\OuterOverViewSqlMap;
use App\Constant\ResponseCode;
use App\Container;
use App\Exception\AppException;
use App\Model\SqlModel\Agency\RankAwemeModel;
use App\Model\SqlModel\Tanwan\OuterDataModel;
use App\Model\SqlModel\Tanwan\PullDataModel;
use App\Model\SqlModel\Tanwan\V2DimAdAwemeModel;
use App\Model\SqlModel\Tanwan\V2DimAgentIdModel;
use App\Model\SqlModel\Tanwan\V2DimGameIdModel;
use App\Model\SqlModel\Agency\RouteModel;
use App\Model\SqlModel\Agency\RoutePermissionModel;
use App\Model\SqlModel\Agency\RankRoutePermissionModel;
use App\Model\SqlModel\Agency\UserModel;
use App\Model\SqlModel\Agency\RankAgentGroupModel;
use App\Model\SqlModel\Agency\RankAgentModel;
use App\Model\SqlModel\Agency\RankPermissionAllModel;
use App\Model\SqlModel\Tanwan\V2DimProjectTeamModel;
use App\Model\SqlModel\Tanwan\V2DimSiteIdModel;
use App\Model\SqlModel\Zeda\AgentModel;
use App\Model\SqlModel\Zeda\SiteDateModel;
use App\Model\SqlModel\Zeda\SiteModel;
use App\Model\SqlModel\Zeda\SiteSettlementModel;
use App\MysqlConnection;
use App\Param\DMS\AgencyUserListParam;
use App\Param\DMS\AgencyUserParam;
use App\Param\DMS\OuterOverViewListFilterParam;
use App\Param\DMS\OuterSiteListParam;
use App\Param\DMS\SiteSettlementListParam;
use App\Param\DMSCostInputLogParam;
use App\Param\SiteGroupEditParam;
use App\Service\OuterService;
use App\Service\SiteService;
use App\Service\UserService;
use App\Utils\DimensionTool;
use App\Utils\FrontendTool;
use App\Utils\Helpers;
use App\Utils\Math;
use Common\EnvConfig;
use Exception;
use Illuminate\Support\Collection;
use ZipArchive;

class OuterLogic
{


    /**
     * 获取投放总览列表
     *
     * @param OuterOverViewListFilterParam $param
     *
     * @return array
     */
    public function getOverviewList(OuterOverViewListFilterParam $param)
    {
        $model = new OuterDataModel();
        if ($param->statistic_caliber == 1) {
            $data = $model->getPlatformOverViewData($param);
        } else if ($param->statistic_caliber == 2) {
            $data = $model->getOverViewData($param);
        } else {
            // 按根 按回流都在这里
            $data = $model->getRootGameOverViewData($param);
        }

        $list = $data['list'];
        /**
         * @var $list Collection
         */
        $sum = ['date' => '合计'];  // 初始化合计数据
        $keys = [];
        if ($list->isNotEmpty()) {
            $keys = array_keys((array)$list[0]);
            foreach ($keys as $key) {
                $sum[$key] = 0;
            }
        }

        $agent_list_index = [];
        // 判断维度是否含有agent_id 有的话补上代理商
        if (in_array('agent_id', $param->dimension)) {
            $platform_agent_data = [];
            foreach ($list as $item) {
                $platform_agent_data[] = [
                    'platform' => $item->platform,
                    'agent_id' => $item->agent_id,
                ];
            }
            /*-- 构建agent_list_index --*/
            if ($platform_agent_data) {
                $offset = 0;
                while (1) {
                    $agent_data = array_slice($platform_agent_data, $offset, 1000);
                    if (empty($agent_data)) {
                        break;
                    }
                    $agent_list = (new AgentModel())->getAgentListByMultiplePlatformAndAgent($agent_data);
                    foreach ($agent_list as $item) {
                        $agent_key = $item->platform . '|' . $item->agent_id;
                        $agent_list_index[$agent_key] = $item;
                    }
                    $offset += 1000;
                }
            }
        }


        // 各种计算
        foreach ($list as $item) {
            if ($param->aggregation_time === '按月') {
                $item->date = date("Y-m", strtotime($item->date . '01'));
            }
            if (isset($item->statistical_type)) {
                $item->statistical_type = $item->statistical_type == 1 ? '平台' : ($item->statistical_type == 2 ? "按子" : '按根');
            }
            // 合计
            foreach ($keys as $key) {
                if (is_numeric($item->$key)) {
                    $sum[$key] += $item->$key;
                }
            }

            // 有渠道id的情况下补上
            if (isset($item->agent_id)) {
                $agent_key = $item->platform . '|' . $item->agent_id;
                $item->agency = isset($agent_list_index[$agent_key]) ? $agent_list_index[$agent_key]->bank_holder : '';
            }

            /*************** 其他相关字段的计算 **************/
            isset($item->total_pay_money, $item->old_uid_pay_money) && $item->first_day_pay_money = $item->total_pay_money - $item->old_uid_pay_money;
            // 媒体转化链
            isset($param->compute_target['click_ip_ldy_same_percent']) && $item->click_ip_ldy_same_percent = $item->click_ip_count_ldy > 0 ? (round((1 - ($item->click_ip_distinct_count_ldy / $item->click_ip_count_ldy)) * 100, 2) . '%') : '0%';
            isset($param->compute_target['click_muid_ldy_same_percent']) && $item->click_muid_ldy_same_percent = $item->click_muid_count_ldy > 0 ? (round((1 - ($item->click_muid_distinct_count_ldy / $item->click_muid_count_ldy)) * 100, 2) . '%') : '0%';
            isset($param->compute_target['click_old_muid_count_ldy_percent']) && $item->click_old_muid_count_ldy_percent = $item->click_count_ldy > 0 ? (round($item->click_old_muid_count_ldy / $item->click_count_ldy * 100, 2) . '%') : '0%';
            isset($param->compute_target['click_old_clique_muid_count_ldy_percent']) && $item->click_old_clique_muid_count_ldy_percent = $item->click_count_ldy > 0 ? (round($item->click_old_clique_muid_count_ldy / $item->click_count_ldy * 100, 2) . '%') : '0%';
            isset($param->compute_target['click_ip_same_percent']) && $item->click_ip_same_percent = $item->click_ip_count > 0 ? (round((1 - ($item->click_ip_distinct_count / $item->click_ip_count)) * 100, 2) . '%') : '0%';
            isset($param->compute_target['click_muid_same_percent']) && $item->click_muid_same_percent = $item->click_muid_count > 0 ? (round((1 - ($item->click_muid_distinct_count / $item->click_muid_count)) * 100, 2) . '%') : '0%';
            isset($param->compute_target['click_old_muid_count_percent']) && $item->click_old_muid_count_percent = $item->click_count > 0 ? (round($item->click_old_muid_count / $item->click_count * 100, 2) . '%') : '0%';
            isset($param->compute_target['click_old_clique_muid_count_percent']) && $item->click_old_clique_muid_count_percent = $item->click_count > 0 ? (round($item->click_old_clique_muid_count / $item->click_count * 100, 2) . '%') : '0%';
            isset($param->compute_target['action_ip_same_percent']) && $item->action_ip_same_percent = $item->action_ip_count > 0 ? (round((1 - ($item->action_ip_distinct_count / $item->action_ip_count)) * 100, 2) . '%') : '0%';
            isset($param->compute_target['action_muid_same_percent']) && $item->action_muid_same_percent = $item->action_muid_count > 0 ? (round((1 - ($item->action_muid_distinct_count / $item->action_muid_count)) * 100, 2) . '%') : '0%';
            isset($param->compute_target['action_old_muid_count_percent']) && $item->action_old_muid_count_percent = $item->action_count > 0 ? (round($item->action_old_muid_count / $item->action_count * 100, 2) . '%') : '0%';
            isset($param->compute_target['action_old_clique_muid_count_percent']) && $item->action_old_clique_muid_count_percent = $item->action_count > 0 ? (round($item->action_old_clique_muid_count / $item->action_count * 100, 2) . '%') : '0%';
            isset($param->compute_target['reg_ip_same_percent']) && $item->reg_ip_same_percent = $item->reg_ip_count > 0 ? (round((1 - ($item->reg_ip_distinct_count / $item->reg_ip_count)) * 100, 2) . '%') : '0%';
            isset($param->compute_target['reg_muid_same_percent']) && $item->reg_muid_same_percent = $item->reg_muid_count > 0 ? (round((1 - ($item->reg_muid_distinct_count / $item->reg_muid_count)) * 100, 2) . '%') : '0%';
            isset($param->compute_target['reg_old_muid_count_percent']) && $item->reg_old_muid_count_percent = $item->reg_uid_count > 0 ? (round($item->reg_old_muid_count / $item->reg_uid_count * 100, 2) . '%') : '0%';
            isset($param->compute_target['reg_old_clique_muid_count_percent']) && $item->reg_old_clique_muid_count_percent = $item->reg_uid_count > 0 ? (round($item->reg_old_clique_muid_count / $item->reg_uid_count * 100, 2) . '%') : '0%';
            isset($param->compute_target['role_create_count_percent']) && $item->role_create_count_percent = $item->reg_uid_count > 0 ? (round($item->role_create_count / $item->reg_uid_count * 100, 2) . '%') : '0%';
            isset($param->compute_target['first_day_role_login_count_percent']) && $item->first_day_role_login_count_percent = $item->reg_uid_count > 0 ? (round($item->first_day_role_login_count / $item->reg_uid_count * 100, 2) . '%') : '0%';

            // 成本相关
            isset($param->compute_target['cost_per_reg']) && $item->cost_per_reg = $item->reg_uid_count > 0 ? (round($item->cost_money / $item->reg_uid_count, 2)) : 0;
            isset($param->compute_target['cost_per_pay']) && $item->cost_per_pay = $item->reg_first_day_pay_uid_count > 0 ? (round($item->cost_money / $item->reg_first_day_pay_uid_count, 2)) : 0;
            isset($param->compute_target['cost_per_second_login']) && $item->cost_per_second_login = $item->second_login_count > 0 ? (round($item->cost_money / $item->second_login_count, 2)) : 0;
            isset($param->compute_target['cost_per_first_day_pay']) && $item->cost_per_first_day_pay = $item->first_day_pay_count > 0 ? (round($item->cost_money / $item->first_day_pay_count, 2)) : 0;
            isset($param->compute_target['first_day_roi']) && $item->first_day_roi = $item->cost_money > 0 ? (round($item->reg_first_day_pay_money / $item->cost_money * 100, 2) . '%') : '0%';
            isset($param->compute_target['roi']) && $item->roi = $item->cost_money > 0 ? (round($item->reg_total_pay_money / $item->cost_money * 100, 2) . '%') : '0%';
            isset($param->compute_target['per_role_create_cost']) && $item->per_role_create_cost = $item->role_create_count > 0 ? (round($item->cost_money / $item->role_create_count, 2)) : 0;

            // 用户质量
            isset($param->compute_target['first_day_pay_percent']) && $item->first_day_pay_percent = $item->reg_uid_count > 0 ? (round($item->first_day_pay_count / $item->reg_uid_count * 100, 2) . '%') : '0%';
            isset($param->compute_target['first_day_arppu']) && $item->first_day_arppu = $item->first_day_pay_count > 0 ? (round($item->first_day_pay_money / $item->first_day_pay_count, 2)) : 0;
            isset($param->compute_target['first_day_ltv']) && $item->first_day_ltv = $item->reg_uid_count > 0 ? (round($item->first_day_pay_money / $item->reg_uid_count, 2)) : 0;
            isset($param->compute_target['arppu']) && $item->arppu = $item->reg_total_pay_count > 0 ? (round($item->reg_total_pay_money / $item->reg_total_pay_count, 2)) : 0;
            isset($param->compute_target['ltv']) && $item->ltv = $item->reg_uid_count > 0 ? (round($item->reg_total_pay_money / $item->reg_uid_count, 2)) : 0;
            isset($param->compute_target['total_pay_percent']) && $item->total_pay_percent = $item->reg_uid_count > 0 ? (round(($item->reg_total_pay_count / $item->reg_uid_count) * 100, 2) . '%') : '0%';
            isset($param->compute_target['second_login_percent']) && $item->second_login_percent = $item->reg_uid_count > 0 ? (round($item->second_login_count / $item->reg_uid_count * 100, 2) . '%') : '0%';
            isset($param->compute_target['third_login_percent']) && $item->third_login_percent = $item->reg_uid_count > 0 ? (round($item->third_login_count / $item->reg_uid_count * 100, 2) . '%') : '0%';
            isset($param->compute_target['seventh_login_percent']) && $item->seventh_login_percent = $item->reg_uid_count > 0 ? (round($item->seventh_login_count / $item->reg_uid_count * 100, 2) . '%') : '0%';
            isset($param->compute_target['fifteenth_login_percent']) && $item->fifteenth_login_percent = $item->reg_uid_count > 0 ? (round($item->fifteenth_login_count / $item->reg_uid_count * 100, 2) . '%') : '0%';

            // 整体情况
            isset($param->compute_target['range_new_arpu']) && $item->range_new_arpu = $item->reg_uid_count > 0 ? (round($item->first_day_pay_money / $item->reg_uid_count, 2)) : 0;
            isset($param->compute_target['range_new_arppu']) && $item->range_new_arppu = $item->first_day_pay_count > 0 ? (round($item->first_day_pay_money / $item->first_day_pay_count, 2)) : 0;
            isset($param->compute_target['reg_uid_new_pay_percent']) && $item->reg_uid_new_pay_percent = $item->reg_uid_count > 0 ? (round($item->first_day_pay_count / $item->reg_uid_count * 100, 2) . '%') : '0%';
            isset($param->compute_target['range_old_arpu']) && $item->range_old_arpu = $item->old_uid_count > 0 ? (round($item->old_uid_pay_money / $item->old_uid_count, 2)) : 0;
            isset($param->compute_target['range_old_arppu']) && $item->range_old_arppu = $item->old_uid_pay_count > 0 ? (round($item->old_uid_pay_money / $item->old_uid_pay_count, 2)) : 0;
            isset($param->compute_target['old_uid_pay_percent']) && $item->old_uid_pay_percent = $item->old_uid_count > 0 ? (round($item->old_uid_pay_count / $item->old_uid_count * 100, 2) . '%') : '0%';
            isset($param->compute_target['range_arpu']) && $item->range_arpu = $item->uid_count > 0 ? (round($item->total_pay_money / $item->uid_count, 2)) : 0;
            isset($param->compute_target['range_arppu']) && $item->range_arppu = ($item->old_uid_pay_count + $item->first_day_pay_count) > 0 ? (round($item->total_pay_money / ($item->old_uid_pay_count + $item->first_day_pay_count), 2)) : 0;
            isset($param->compute_target['range_pay_count']) && $item->range_pay_count = $item->old_uid_pay_count + $item->first_day_pay_count;
            isset($param->compute_target['reg_uid_new_pay_money']) && $item->reg_uid_new_pay_money = $item->total_pay_money - $item->old_uid_pay_money;
            isset($param->compute_target['range_uid_pay_percent']) && $item->range_uid_pay_percent = $item->uid_count > 0 ? (round(($item->first_day_pay_count + $item->old_uid_pay_count) / $item->uid_count * 100, 2) . '%') : '0%';

            // 转化率
            isset($param->compute_target['click_ldy_action_percent']) && $item->click_ldy_action_percent = $item->click_count_ldy > 0 ? (round(($item->action_count / $item->click_count_ldy) * 100, 2) . '%') : '0%';
            isset($param->compute_target['click_action_percent']) && $item->click_action_percent = $item->click_count > 0 ? (round(($item->action_count / $item->click_count) * 100, 2) . '%') : '0%';
            isset($param->compute_target['click_ldy_uid_reg_percent']) && $item->click_ldy_uid_reg_percent = $item->click_count_ldy > 0 ? (round(($item->reg_uid_count / $item->click_count_ldy) * 100, 2) . '%') : '0%';
            isset($param->compute_target['click_uid_reg_percent']) && $item->click_uid_reg_percent = $item->click_count > 0 ? (round(($item->reg_uid_count / $item->click_count) * 100, 2) . '%') : '0%';
            isset($param->compute_target['click_ldy_role_create_percent']) && $item->click_ldy_role_create_percent = $item->click_count_ldy > 0 ? (round(($item->role_create_count / $item->click_count_ldy) * 100, 2) . '%') : '0%';
            isset($param->compute_target['click_role_create_percent']) && $item->click_role_create_percent = $item->click_count > 0 ? (round(($item->role_create_count / $item->click_count) * 100, 2) . '%') : '0%';
            isset($param->compute_target['action_uid_reg_percent']) && $item->action_uid_reg_percent = $item->action_count > 0 ? (round(($item->reg_uid_count / $item->action_count) * 100, 2) . '%') : '0%';
            isset($param->compute_target['action_role_create_percent']) && $item->action_role_create_percent = $item->action_count > 0 ? (round(($item->role_create_count / $item->action_count) * 100, 2) . '%') : '0%';

            // 媒体展示
            isset($param->compute_target['show_old_muid_count_percent']) && $item->show_old_muid_count_percent = $item->click_count_yeyou_show > 0 ? (round(($item->click_old_muid_count_yeyou_show / $item->click_count_yeyou_show) * 100, 2) . '%') : '0%';
            isset($param->compute_target['show_old_clique_muid_count_percent']) && $item->show_old_clique_muid_count_percent = $item->click_count_yeyou_show > 0 ? (round(($item->click_old_clique_muid_count_yeyou_show / $item->click_count_yeyou_show) * 100, 2) . '%') : '0%';

            // 几个首日
            isset($param->compute_target['reg_first_day_ltv']) && $item->reg_first_day_ltv = $item->reg_uid_count > 0 ? (round($item->reg_first_day_pay_money / $item->reg_uid_count, 2)) : 0;
            isset($param->compute_target['reg_first_day_pay_percent']) && $item->reg_first_day_pay_percent = $item->reg_uid_count > 0 ? (round($item->reg_first_day_pay_uid_count / $item->reg_uid_count * 100, 2) . '%') : '0%';

            // 格式化显示
            foreach (OuterOverViewSqlMap::FORMAT_COLUMN as $column) {
                in_array($column, $param->target) && $item->$column = round($item->$column);
            }
            $item->cost_money = Math::decimal($item->cost_money, 2);
        }

        /*****************  计算合计的其他字段  *****************/
        if ($list->isNotEmpty()) {

            isset($sum['total_pay_money'], $sum['old_uid_pay_money']) && $sum['first_day_pay_money'] = $sum['total_pay_money'] - $sum['old_uid_pay_money'];
            // 媒体转化链
            isset($param->compute_target['click_ip_ldy_same_percent']) && $sum['click_ip_ldy_same_percent'] = $sum['click_ip_count_ldy'] > 0 ? (round((1 - ($sum['click_ip_distinct_count_ldy'] / $sum['click_ip_count_ldy'])) * 100, 2) . '%') : '0%';;
            isset($param->compute_target['click_muid_ldy_same_percent']) && $sum['click_muid_ldy_same_percent'] = $sum['click_muid_count_ldy'] > 0 ? (round((1 - ($sum['click_muid_distinct_count_ldy'] / $sum['click_muid_count_ldy'])) * 100, 2) . '%') : '0%';
            isset($param->compute_target['click_old_muid_count_ldy_percent']) && $sum['click_old_muid_count_ldy_percent'] = $sum['click_count_ldy'] > 0 ? (round($sum['click_old_muid_count_ldy'] / $sum['click_count_ldy'] * 100, 2) . '%') : '0%';
            isset($param->compute_target['click_old_clique_muid_count_ldy_percent']) && $sum['click_old_clique_muid_count_ldy_percent'] = $sum['click_count_ldy'] > 0 ? (round($sum['click_old_clique_muid_count_ldy'] / $sum['click_count_ldy'] * 100, 2) . '%') : '0%';
            isset($param->compute_target['click_ip_same_percent']) && $sum['click_ip_same_percent'] = $sum['click_ip_count'] > 0 ? (round((1 - ($sum['click_ip_distinct_count'] / $sum['click_ip_count'])) * 100, 2) . '%') : '0%';
            isset($param->compute_target['click_muid_same_percent']) && $sum['click_muid_same_percent'] = $sum['click_muid_count'] > 0 ? (round((1 - ($sum['click_muid_distinct_count'] / $sum['click_muid_count'])) * 100, 2) . '%') : '0%';
            isset($param->compute_target['click_old_muid_count_percent']) && $sum['click_old_muid_count_percent'] = $sum['click_count'] > 0 ? (round($sum['click_old_muid_count'] / $sum['click_count'] * 100, 2) . '%') : '0%';
            isset($param->compute_target['click_old_clique_muid_count_percent']) && $sum['click_old_clique_muid_count_percent'] = $sum['click_count'] > 0 ? (round($sum['click_old_clique_muid_count'] / $sum['click_count'] * 100, 2) . '%') : '0%';
            isset($param->compute_target['action_ip_same_percent']) && $sum['action_ip_same_percent'] = $sum['action_ip_count'] > 0 ? (round((1 - ($sum['action_ip_distinct_count'] / $sum['action_ip_count'])) * 100, 2) . '%') : '0%';
            isset($param->compute_target['action_muid_same_percent']) && $sum['action_muid_same_percent'] = $sum['action_muid_count'] > 0 ? (round((1 - ($sum['action_muid_distinct_count'] / $sum['action_muid_count'])) * 100, 2) . '%') : '0%';
            isset($param->compute_target['action_old_muid_count_percent']) && $sum['action_old_muid_count_percent'] = $sum['action_count'] > 0 ? (round($sum['action_old_muid_count'] / $sum['action_count'] * 100, 2) . '%') : '0%';
            isset($param->compute_target['action_old_clique_muid_count_percent']) && $sum['action_old_clique_muid_count_percent'] = $sum['action_count'] > 0 ? (round($sum['action_old_clique_muid_count'] / $sum['action_count'] * 100, 2) . '%') : '0%';
            isset($param->compute_target['reg_ip_same_percent']) && $sum['reg_ip_same_percent'] = $sum['reg_ip_count'] > 0 ? (round((1 - ($sum['reg_ip_distinct_count'] / $sum['reg_ip_count'])) * 100, 2) . '%') : '0%';
            isset($param->compute_target['reg_muid_same_percent']) && $sum['reg_muid_same_percent'] = $sum['reg_muid_count'] > 0 ? (round((1 - ($sum['reg_muid_distinct_count'] / $sum['reg_muid_count'])) * 100, 2) . '%') : '0%';
            isset($param->compute_target['reg_old_muid_count_percent']) && $sum['reg_old_muid_count_percent'] = $sum['reg_uid_count'] > 0 ? (round($sum['reg_old_muid_count'] / $sum['reg_uid_count'] * 100, 2) . '%') : '0%';
            isset($param->compute_target['reg_old_clique_muid_count_percent']) && $sum['reg_old_clique_muid_count_percent'] = $sum['reg_uid_count'] > 0 ? (round($sum['reg_old_clique_muid_count'] / $sum['reg_uid_count'] * 100, 2) . '%') : '0%';
            isset($param->compute_target['role_create_count_percent']) && $sum['role_create_count_percent'] = $sum['reg_uid_count'] > 0 ? (round($sum['role_create_count'] / $sum['reg_uid_count'] * 100, 2) . '%') : '0%';
            isset($param->compute_target['first_day_role_login_count_percent']) && $sum['first_day_role_login_count_percent'] = $sum['reg_uid_count'] > 0 ? (round($sum['first_day_role_login_count'] / $sum['reg_uid_count'] * 100, 2) . '%') : '0%';
            // 成本相关
            isset($param->compute_target['cost_per_reg']) && $sum['cost_per_reg'] = $sum['reg_uid_count'] > 0 ? (round($sum['cost_money'] / $sum['reg_uid_count'], 2)) : 0;
            isset($param->compute_target['cost_per_pay']) && $sum['cost_per_pay'] = $sum['reg_first_day_pay_uid_count'] > 0 ? (round($sum['cost_money'] / $sum['reg_first_day_pay_uid_count'], 2)) : 0;
            isset($param->compute_target['cost_per_second_login']) && $sum['cost_per_second_login'] = $sum['second_login_count'] > 0 ? (round($sum['cost_money'] / $sum['second_login_count'], 2)) : 0;
            isset($param->compute_target['cost_per_first_day_pay']) && $sum['cost_per_first_day_pay'] = $sum['first_day_pay_count'] > 0 ? (round($sum['cost_money'] / $sum['first_day_pay_count'], 2)) : 0;
            isset($param->compute_target['first_day_roi']) && $sum['first_day_roi'] = $sum['cost_money'] > 0 ? (round($sum['reg_first_day_pay_money'] / $sum['cost_money'] * 100, 2) . '%') : '0%';
            isset($param->compute_target['roi']) && $sum['roi'] = $sum['cost_money'] > 0 ? (round($sum['reg_total_pay_money'] / $sum['cost_money'] * 100, 2) . '%') : '0%';
            isset($param->compute_target['per_role_create_cost']) && $sum['per_role_create_cost'] = $sum['role_create_count'] > 0 ? (round($sum['cost_money'] / $sum['role_create_count'], 2)) : 0;

            // 用户质量
            isset($param->compute_target['first_day_pay_percent']) && $sum['first_day_pay_percent'] = $sum['reg_uid_count'] > 0 ? (round($sum['first_day_pay_count'] / $sum['reg_uid_count'] * 100, 2) . '%') : '0%';
            isset($param->compute_target['first_day_arppu']) && $sum['first_day_arppu'] = $sum['first_day_pay_count'] > 0 ? (round($sum['first_day_pay_money'] / $sum['first_day_pay_count'], 2)) : 0;
            isset($param->compute_target['first_day_ltv']) && $sum['first_day_ltv'] = $sum['reg_uid_count'] > 0 ? (round($sum['first_day_pay_money'] / $sum['reg_uid_count'], 2)) : 0;
            isset($param->compute_target['second_login_percent']) && $sum['second_login_percent'] = $sum['reg_uid_count'] > 0 ? (round($sum['second_login_count'] / $sum['reg_uid_count'] * 100, 2) . '%') : '0%';
            isset($param->compute_target['third_login_percent']) && $sum['third_login_percent'] = $sum['reg_uid_count'] > 0 ? (round($sum['third_login_count'] / $sum['reg_uid_count'] * 100, 2) . '%') : '0%';
            isset($param->compute_target['seventh_login_percent']) && $sum['seventh_login_percent'] = $sum['reg_uid_count'] > 0 ? (round($sum['seventh_login_count'] / $sum['reg_uid_count'] * 100, 2) . '%') : '0%';
            isset($param->compute_target['fifteenth_login_percent']) && $sum['fifteenth_login_percent'] = $sum['reg_uid_count'] > 0 ? (round($sum['fifteenth_login_count'] / $sum['reg_uid_count'] * 100, 2) . '%') : '0%';
            isset($param->compute_target['arppu']) && $sum['arppu'] = $sum['reg_total_pay_count'] > 0 ? (round($sum['reg_total_pay_money'] / $sum['reg_total_pay_count'], 2)) : 0;
            isset($param->compute_target['ltv']) && $sum['ltv'] = $sum['reg_uid_count'] > 0 ? (round($sum['reg_total_pay_money'] / $sum['reg_uid_count'], 2)) : 0;
            isset($param->compute_target['total_pay_percent']) && $sum['total_pay_percent'] = $sum['reg_uid_count'] > 0 ? (round(($sum['reg_total_pay_count'] / $sum['reg_uid_count']) * 100, 2) . '%') : '0%';


            // 整体情况
            isset($param->compute_target['range_new_arpu']) && $sum['range_new_arpu'] = $sum['reg_uid_count'] > 0 ? (round($sum['first_day_pay_money'] / $sum['reg_uid_count'], 2)) : 0;
            isset($param->compute_target['range_new_arppu']) && $sum['range_new_arppu'] = $sum['first_day_pay_count'] > 0 ? (round($sum['first_day_pay_money'] / $sum['first_day_pay_count'], 2)) : 0;
            isset($param->compute_target['reg_uid_new_pay_percent']) && $sum['reg_uid_new_pay_percent'] = $sum['reg_uid_count'] > 0 ? (round($sum['first_day_pay_count'] / $sum['reg_uid_count'] * 100, 2) . '%') : '0%';
            isset($param->compute_target['range_old_arpu']) && $sum['range_old_arpu'] = $sum['old_uid_count'] > 0 ? (round($sum['old_uid_pay_money'] / $sum['old_uid_count'], 2)) : 0;
            isset($param->compute_target['range_old_arppu']) && $sum['range_old_arppu'] = $sum['old_uid_pay_count'] > 0 ? (round($sum['old_uid_pay_money'] / $sum['old_uid_pay_count'], 2)) : 0;
            isset($param->compute_target['old_uid_pay_percent']) && $sum['old_uid_pay_percent'] = $sum['old_uid_count'] > 0 ? (round($sum['old_uid_pay_count'] / $sum['old_uid_count'] * 100, 2) . '%') : '0%';
            isset($param->compute_target['range_arpu']) && $sum['range_arpu'] = $sum['uid_count'] > 0 ? (round($sum['total_pay_money'] / $sum['uid_count'], 2)) : 0;
            isset($param->compute_target['range_arppu']) && $sum['range_arppu'] = ($sum['old_uid_pay_count'] + $sum['first_day_pay_count']) > 0 ? (round($sum['total_pay_money'] / ($sum['old_uid_pay_count'] + $sum['first_day_pay_count']), 2)) : 0;
            isset($param->compute_target['range_pay_count']) && $sum['range_pay_count'] = $sum['old_uid_pay_count'] + $sum['first_day_pay_count'];
            isset($param->compute_target['range_uid_pay_percent']) && $sum['range_uid_pay_percent'] = $sum['uid_count'] > 0 ? (round(($sum['old_uid_pay_count'] + $sum['first_day_pay_count']) / $sum['uid_count'] * 100, 2) . '%') : '0%';
            isset($param->compute_target['reg_uid_new_pay_money']) && $sum['reg_uid_new_pay_money'] = $sum['total_pay_money'] - $sum['old_uid_pay_money'];

            // 转化率
            isset($param->compute_target['click_ldy_action_percent']) && $sum['click_ldy_action_percent'] = $sum['click_count_ldy'] > 0 ? (round($sum['action_count'] / $sum['click_count_ldy'] * 100, 2) . '%') : '0%';
            isset($param->compute_target['click_action_percent']) && $sum['click_action_percent'] = $sum['click_count'] > 0 ? (round($sum['action_count'] / $sum['click_count'] * 100, 2) . '%') : '0%';
            isset($param->compute_target['click_ldy_uid_reg_percent']) && $sum['click_ldy_uid_reg_percent'] = $sum['click_count_ldy'] > 0 ? (round($sum['reg_uid_count'] / $sum['click_count_ldy'] * 100, 2) . '%') : '0%';
            isset($param->compute_target['click_uid_reg_percent']) && $sum['click_uid_reg_percent'] = $sum['click_count'] > 0 ? (round($sum['reg_uid_count'] / $sum['click_count'] * 100, 2) . '%') : '0%';
            isset($param->compute_target['click_ldy_role_create_percent']) && $sum['click_ldy_role_create_percent'] = $sum['click_count_ldy'] > 0 ? (round($sum['role_create_count'] / $sum['click_count_ldy'] * 100, 2) . '%') : '0%';
            isset($param->compute_target['click_role_create_percent']) && $sum['click_role_create_percent'] = $sum['click_count'] > 0 ? (round($sum['role_create_count'] / $sum['click_count'] * 100, 2) . '%') : '0%';
            isset($param->compute_target['action_uid_reg_percent']) && $sum['action_uid_reg_percent'] = $sum['action_count'] > 0 ? (round($sum['reg_uid_count'] / $sum['action_count'] * 100, 2) . '%') : '0%';
            isset($param->compute_target['action_role_create_percent']) && $sum['action_role_create_percent'] = $sum['action_count'] > 0 ? (round($sum['role_create_count'] / $sum['action_count'] * 100, 2) . '%') : '0%';

            // 媒体展示
            isset($param->compute_target['show_old_muid_count_percent']) && $sum['show_old_muid_count_percent'] = $sum['click_count_yeyou_show'] > 0 ? (round(($sum['click_old_muid_count_yeyou_show'] / $sum['click_count_yeyou_show']) * 100, 2) . '%') : '0%';
            isset($param->compute_target['show_old_clique_muid_count_percent']) && $sum['show_old_clique_muid_count_percent'] = $sum['click_count_yeyou_show'] > 0 ? (round(($sum['click_old_clique_muid_count_yeyou_show'] / $sum['click_count_yeyou_show']) * 100, 2) . '%') : '0%';

            // 几个首日
            isset($param->compute_target['reg_first_day_ltv']) && $sum['reg_first_day_ltv'] = $sum['reg_uid_count'] > 0 ? (round($sum['reg_first_day_pay_money'] / $sum['reg_uid_count'], 2)) : 0;
            isset($param->compute_target['reg_first_day_pay_percent']) && $sum['reg_first_day_pay_percent'] = $sum['reg_uid_count'] > 0 ? (round($sum['reg_first_day_pay_uid_count'] / $sum['reg_uid_count'] * 100, 2) . '%') : '0%';

            // 格式化显示
            foreach (OuterOverViewSqlMap::FORMAT_COLUMN as $column) {
                in_array($column, $param->target) && $sum[$column] = round($sum[$column]);
            }
            $sum['cost_money'] = round($sum['cost_money'], 2);
            $sum['date'] = '合计';
        }

        return [
            'sum'  => $sum,
            'list' => $list,
            'sql'  => $data['sql']
        ];
    }


    /**
     *
     * @return array
     */
    public function allPermissionDetail()
    {
        $route_model = new RouteModel();

        $route_item = $route_model->getAll();
        $route_permission_list = (new RoutePermissionModel())->getAll();
        $platform = (new V2DimGameIdModel())->getPlatformList();
        $route_cascader = $route_model->getCascader($route_item);
        return [
            'route_cascader'        => $route_cascader,
            'route_permission_list' => FrontendTool::formatRoutePermission($route_permission_list),
            'platform_list'         => $platform
                ->filter(function ($platform) {
                    return isset(EnvConfig::PLATFORM_MAP[$platform]);
                })
                ->map(function ($platform) {
                    return [
                        'platform_id'   => $platform,
                        'platform_name' => EnvConfig::PLATFORM_MAP[$platform]
                    ];
                })
                ->values(),
        ];
    }


    /**
     * 获取某个用户的某个平台下的渠道组列表
     *
     * @param $platform
     *
     * @return array
     */
    public function getPlatformAgentGroupPermission($platform)
    {
        $agent_group_list = (new V2DimAgentIdModel())->getAllAgentGroupByPlatform($platform);
        return [
            'agent_group_list' => $agent_group_list,
        ];
    }


    /**
     * 获取渠道列表
     *
     * @param        $agent_group_id
     * @param        $platform
     * @param string $keyword
     *
     * @return Collection
     */
    public function getAgentListByGroup($agent_group_id, $platform, $keyword = '')
    {
        $agent_list = (new V2DimAgentIdModel())->getAgentList($agent_group_id, $platform, $keyword);
        $agent_id_list = $agent_list->pluck('agent_id')->toArray();

        $agent_info_list = (new AgentModel())->getListByAgentId($agent_id_list, $platform);
        $agent_info_list = $agent_info_list->keyBy('agent_id');

        foreach ($agent_list as $item) {
            $item->statistic_caliber = $agent_info_list[$item->agent_id]->statistic_caliber ?? 0;
        }

        return $agent_list;
    }


    /**
     * @param AgencyUserListParam $param
     *
     * @return array
     */
    public function getUserList(AgencyUserListParam $param, $agent_permission = -1)
    {

        $data = (new UserModel())->getList($param, $agent_permission);
        $key = 'XZZX_GROUP_@)@)';
        $time = time();
        foreach ($data['list'] as $item) {
            unset($item->password);
            $index = 'x-token';
            $item->timestamp = $time;
            $item->$index = md5($item->account . $key . $time);
        }

        return $data;
    }

    /**
     * @param AgencyUserParam $user_param
     *
     * @throws Exception
     */
    public function addUser(AgencyUserParam $user_param)
    {
        // 获取agent_group_id和agent_id
        $agent_group_id = $user_param->permission_list['agent_list'][0]['agent_group_id'] ?? 0;
        $agent_id = $user_param->permission_list['agent_list'][0]['agent_list'][0]['agent_id'] ?? 0;

        // 渠道和直播间的权限二选一
        if (!($agent_group_id && $agent_id) && empty($user_param->aweme_accounts)) {
            throw new AppException('权限错误');
        }
        $user_param->agent_group_id = $agent_group_id;
        $user_param->agent_id = $agent_id;
        $user_param->pub_password = $user_param->password;

        // 开始事务
        MysqlConnection::getConnection('agency')->beginTransaction();
        try {
            (new OuterService())->addUserPermission($user_param);
            MysqlConnection::getConnection('agency')->commit();
        } catch (\Exception $e) {
            MysqlConnection::getConnection('agency')->rollBack();
            throw new AppException($e->getMessage(), ResponseCode::FAILURE);
        }
    }

    /**
     * @param AgencyUserParam $user_param
     *
     * @throws \Exception
     */
    public function editUser(AgencyUserParam $user_param)
    {
        // 编辑用户基本信息
        $user_model = new UserModel();
        if (!empty($user_param->password)) {
            $user_param->pub_password = $user_param->password;
            $user_param->password = UserService::genHashPwd($user_param->password);
        }


        $user_model->edit($user_param);
    }

    /**
     * 删除用户
     *
     * @param $user_id
     * @param $editor
     *
     * @throws \Exception
     */
    public function deleteUser($user_id, $editor)
    {
        // 开始事务
        MysqlConnection::getConnection('agency')->beginTransaction();
        try {
            // 删除用户
            (new UserModel())->remove($user_id, $editor);
//            // 删除权限 todo  不删除权限 只是禁用
//            $this->delRankAllPermission($user_id);
            MysqlConnection::getConnection('agency')->commit();
        } catch (\Exception $e) {
            MysqlConnection::getConnection('agency')->rollBack();
            throw new AppException($e->getMessage(), ResponseCode::FAILURE);
        }
    }

    /**
     * @param AgencyUserParam $param
     *
     * @throws \Exception
     */
    public function editUserPermission(AgencyUserParam $param)
    {
        // 处理的表有点多 开启一下事务
        MysqlConnection::getConnection('agency')->beginTransaction();
        try {
            $this->editUser($param);
            $user_id = $param->user_id;
            // 删除权限 然后再加
            (new RankRoutePermissionModel())->delete($user_id);
            (new RankAwemeModel())->delete($user_id);
            // 处理路由权限
            $add_rank_route_permission_result = $this->addRankRoutePermission($user_id, $param->route_permission_ids);
            $add_rank_aweme_result = $this->addRankAweme($user_id, $param->aweme_accounts, $param->platform);
            if (!($add_rank_route_permission_result && $add_rank_aweme_result)) {
                throw new AppException('编辑失败！失败原因：路由列表处理失败');
            }
            // 提交事务
            MysqlConnection::getConnection('agency')->commit();
        } catch (\Throwable $e) {
            MysqlConnection::getConnection('agency')->rollBack();
            throw new AppException($e->getMessage());
        }

    }


    /**
     * 组装渠道数据
     *
     * @param array $permission_list 前端传过来的超级复杂的东西
     * @param string $platform 平台
     *
     * @return array
     */
    private function assemblyAgentList($permission_list, $platform)
    {
        $agent_group_list = $agent_list = [];

        // 组装数据
        if (isset($permission_list['agent_list']) && is_array($permission_list['agent_list'])) {
            if (empty($permission_list['agent_list'])) {
                throw new AppException('渠道组权限不能为空');
            }
            foreach ($permission_list['agent_list'] as $group) {
                if (!isset($group['agent_list']) || !$group['agent_list']) {
                    $agent_group_list[] = [
                        'agent_group_id' => $group['agent_group_id'],
                        'platform'       => $platform,
                    ];
                } else {
                    foreach ($group['agent_list'] as $item) {
                        $agent_list[] = [
                            'agent_group_id' => $group['agent_group_id'],
                            'agent_id'       => $item['agent_id'],
                            'platform'       => $platform,
                        ];
                    }
                }
            }
        }

        return [
            'agent_group_list' => $agent_group_list,
            'agent_list'       => $agent_list,
        ];
    }

    /**
     * 添加路由权限
     *
     * @param $user_id
     * @param $route_permission_ids
     *
     * @return bool
     */
    public function addRankRoutePermission($user_id, $route_permission_ids)
    {
        return (new RankRoutePermissionModel())->addMultiple($user_id, $route_permission_ids);
    }

    /**
     * 添加渠道权限
     *
     * @param int $user_id
     * @param array $agent
     *
     * @return bool
     */
    public function addRankAgent($user_id, $agent)
    {
        $agent_group_list = $agent['agent_group_list'];
        $agent_list = $agent['agent_list'];
        $agent_group_add_result = $agent_add_result = true;
        if (!empty($agent_group_list)) {
            // 处理一下全选的问题
            $this->handleAgentGroupAllPermission($user_id, $agent_group_list);
            $agent_group_model = new RankAgentGroupModel();
            $agent_group_add_result = $agent_group_model->addMultiple($user_id, $agent_group_list);
        }
        if (!empty($agent_list)) {
            $agent_model = new RankAgentModel();
            $agent_add_result = $agent_model->addMultiple($user_id, $agent_list);
        }


        return $agent_group_add_result && $agent_add_result;
    }

    public function getUserAllPermissionDetail($user_id)
    {
        $user_info = (new UserModel())->getDataByUserId($user_id);
        unset($user_info['password']);
        $platform_list = $this->getRankPlatformList($user_id, $user_info['platform']);
        $route_permission_list = $this->getRankRoutePermissionList($user_id, $user_info['route_list']);
        $aweme_list = $this->getRankAwemeList($user_id);
        return [
            'user_info'             => $user_info,
            'platform'              => $platform_list,
            'route_permission_list' => $route_permission_list,
            'aweme'                 => $aweme_list
        ];
    }

    /**
     * 获取某个等级的所有路由权限（返回前端需要的结构）
     *
     * @param $user_id
     * @param $route_id_list
     *
     * @return array
     */
    private function getRankRoutePermissionList($user_id, $route_id_list)
    {
        $route_permission_list = FrontendTool::formatRoutePermission((new RankRoutePermissionModel())->getAll($user_id));

        $route_model = new RouteModel();
        $route_tree = $route_model->getTree($route_model->getAllByRouteStr($route_id_list));

        $route_permission_ids = [];
        foreach ($route_tree as $key => $father) {
            // 下级无选择
            if (!isset($father['children'])) {
                continue;
            }
            foreach ($father['children'] as $children) {
                $route_permission_ids[$children['id']] = [$father['id'], $children['id']];
                $origin_length = count($route_permission_ids);
                foreach ($route_permission_list as $route_permission) {
                    if ($route_permission['route_id'] === $children['id']) {
                        if (isset($route_permission['type']) && $route_permission['type']) {
                            $route_permission_ids[$children['id'] . '-' . $route_permission['id']] = [$father['id'], $children['id'], $route_permission['cate_name'], $route_permission['type'], $route_permission['id']];
                        } else {
                            $route_permission_ids[$children['id'] . '-' . $route_permission['id']] = [$father['id'], $children['id'], $route_permission['cate_name'], $route_permission['id']];
                        }
                    }
                }
                if ($origin_length !== count($route_permission_ids)) {
                    unset($route_permission_ids[$children['id']]);
                }

            }
        }

        return array_values($route_permission_ids);
    }


    /**
     * 获取某个等级的平台数据权限 返回前端需要的数据结构
     *
     * @param $user_id
     * @param $platform
     *
     * @return array
     */
    public function getRankPlatformList($user_id, $platform)
    {

        // 获取用户的的 agent_group agent_list
        $agent_group_list = (new RankAgentGroupModel())->getAll($user_id);
        $agent_list = (new RankAgentModel())->getAllByADPlatformId($user_id);
        (new V2DimAgentIdModel())->injectName($agent_group_list, $agent_list);

        // 组装数据
        $result_list = [];
        $arr['platform'] = $platform;

        $arr['agent_group_ids'] = $agent_group_list->where('platform', $platform)->pluck('agent_group_id')->toArray();

        $tmp_agent_list = $agent_list->where('platform', $platform);
        $arr['agent_list'] = [];
        foreach ($tmp_agent_list as $agent) {
            $agent = (array)$agent;
            if (isset($arr['agent_list'][$agent['agent_group_id']])) {
                $arr['agent_list'][$agent['agent_group_id']]['agent_ids'][] = $agent['agent_id'];
                $arr['agent_list'][$agent['agent_group_id']]['agent_options'][] = [
                    'agent_id'   => $agent['agent_id'],
                    'agent_name' => $agent['agent_name']
                ];
            } else {
                $arr['agent_list'][$agent['agent_group_id']] = [
                    'agent_ids'        => [$agent['agent_id']],
                    'agent_group_id'   => $agent['agent_group_id'],
                    'agent_group_name' => $agent['agent_group_name'] ?? '',
                    'agent_options'    => [[
                                               'agent_id'   => $agent['agent_id'],
                                               'agent_name' => $agent['agent_name']
                                           ]],
                ];
            }
        }
        $arr['agent_list'] = array_values($arr['agent_list']);

        $result_list[] = $arr;

        return $result_list;
    }


    public function getSiteList(OuterSiteListParam $param)
    {
        $site_model = new SiteModel();

        $data = $site_model->getOuterSiteList($param);
        foreach ($data['list'] as $item) {
            $item->ad_pop_zk = Math::div($item->ad_pop_zk, 100);
            $item->last_ad_pop_zk = Math::div($item->last_ad_pop_zk, 100);

            $item->settlement_type = (new OuterService())->switchSettlementPayType('', $item->pay_type);
            if ($item->settlement_type === 'cps') {
                $item->cps_divide_rate = $item->settlement_base_value;
            } else if ($item->settlement_type === 'cpa') {
                $item->ad_price = Math::decimal($item->settlement_base_value, 0);
                $item->settlement_base_value = $item->ad_price;
            }
            $item->deduction_type = $item->ad_pop_zk_type == 1 ? '扣注册数' : ($item->ad_pop_zk_type == 2 ? '扣订单数' : '扣订单金额');
            $item->deduction_value = $item->ad_pop_zk;
        }

        return $data;
    }

    /**
     * 根据platform获取agent list
     *
     * @param string $platform
     * @param mixed $agent_permission
     * @param string $keyword
     *
     * @return Collection
     */
    public function getAgentListByPlatform($platform, $agent_permission, $keyword = '')
    {
//        $agent_model = new AgentModel();

        $agent_model = new V2DimSiteIdModel();
        // 2025年02月12日，需要查看页游的数据，所以这里改为从ADB取数
//        return $agent_model->getAgentListByPlatform($platform, $agent_permission, $keyword);
        return $agent_model->getAgentListByPlatform($platform, $agent_permission, $keyword);
    }


    /**
     * @param $platform
     * @param $agent_id
     *
     * @return Collection
     */
    public function getSiteListByAgent($platform, $agent_id, $filter)
    {
//        $site_model = new SiteModel();
        $site_model = new V2DimSiteIdModel();
        return $site_model->getOutSiteList($platform, $agent_id, $filter);
    }


    /**
     * @param $platform
     * @param $site_id
     *
     * @return array
     */
    public function getSiteDetail($platform, $site_id)
    {
        $site_model = new SiteModel();
        $data = $site_model->getDataByPlatformSiteId($platform, $site_id);

        if ($data) {
            $res_data['ad_pop_zk'] = Math::div($data->ad_pop_zk, 100);
            $res_data['ad_pop_zk_type'] = $data->ad_pop_zk_type;
            $res_data['tax_rate'] = $data->tax_rate;
            $res_data['channel_fee_rate'] = $data->channel_fee_rate;
            $res_data['settlement_type'] = (new OuterService())->switchSettlementPayType('', $data->pay_type);
            return $res_data;
        } else {
            throw new AppException('找不到广告位');
        }

    }


    /**
     * 新增结算周期广告位
     *
     * @param OuterSiteListParam $param
     *
     * @throws Exception
     */
    public function addSiteDate(OuterSiteListParam $param)
    {
        $site_date_model = new SiteDateModel();
//        // 判断能不能新增 查出这一批site_id的最大开始日期
//        $max_start_date = $site_date_model->getMaxStartDate($param->platform, $param->site_id);
//        if (strtotime($max_start_date) >= strtotime($param->start_date)) {
//            throw new AppException('开始日期必须大于' . $max_start_date);
//        }

        MysqlConnection::getConnection('default')->beginTransaction();
        try {
            // 更新掉最后那条记录
            $last_end_date = date("Y-m-d", strtotime($param->start_date) - 86400);
            $site_date_model->updateLastEndDate($param->platform, $param->site_id, $param->start_date, $last_end_date, $param->editor);

            // 删除后面的数据
            $site_date_model->deleteByTime($param->platform, $param->site_id, $param->start_date);

            // 插入新记录
            $site_date_model->addSiteDate($param);

            MysqlConnection::getConnection('default')->commit();
        } catch (\Throwable $exception) {
            MysqlConnection::getConnection('default')->rollBack();
            throw new AppException('数据库报错,错误信息：' . $exception->getMessage());
        }

    }

    /**
     * 编辑某个结算周期的广告位
     *
     * @param OuterSiteListParam $param
     *
     * @return int
     */
    public function editSiteDate(OuterSiteListParam $param)
    {
        $site_date_model = new SiteDateModel();
        if (!in_array($param->settlement_type, ['cps', 'cpa'], true)) {
            throw new AppException('错误的结算类型');
        }

        // 只更新更新时间
        if ($param->settlement_type === 'cps' && $param->cps_divide_rate == -1) {
            return $site_date_model->updateTime($param->platform, $param->site_id[0], $param->start_date);
        }
        // 只更新更新时间
        if ($param->settlement_type === 'cpa' && $param->ad_price == -1) {
            return $site_date_model->updateTime($param->platform, $param->site_id[0], $param->start_date);
        }

        return $site_date_model->updateSiteDateValue($param);
    }


    /**
     * 删除某个结算周期的广告位
     *
     * @param $platform
     * @param $site_id
     * @param $start_date
     *
     * @throws \Exception
     */
    public function deleteSiteDate($platform, $site_id, $start_date)
    {
        $site_date_model = new SiteDateModel();
        // 判断能不能删除 查出这一个site_id的最大开始日期 删除的只能是最大日期
        $max_start_date = $site_date_model->getMaxStartDate($platform, [$site_id]);
        if (strtotime($max_start_date) !== strtotime($start_date)) {
            throw new AppException('只能删除最后的结算开始日期：' . $max_start_date);
        }

        MysqlConnection::getConnection('default')->beginTransaction();
        try {
            // 更新上一条记录
            $last_end_date = date("Y-m-d", strtotime($start_date) - 86400);
            $site_date_model->updateLastEndDateByEndDate($platform, [$site_id], $last_end_date, Container::getSession()->get('name'));

            // 删除记录
            $site_date_model->delete($platform, $site_id, $start_date);

            MysqlConnection::getConnection('default')->commit();
        } catch (\Throwable $exception) {
            MysqlConnection::getConnection('default')->rollBack();
            throw new AppException('数据库报错,错误信息：' . $exception->getMessage());
        }
    }


    /**
     * 编辑（新增）对外后台广告位
     *
     * @param OuterSiteListParam $param
     *
     */
    public function editSite(OuterSiteListParam $param)
    {
        // 调外部接口
        $outer_param = [
            'platform' => $param->platform,
            'agent_id' => $param->agent_id,
            'site_ids' => $param->site_id,
            'pay_type' => (new OuterService())->switchSettlementPayType($param->settlement_type),
        ];
        if ($param->ad_pop_zk_type != -1) {
            $outer_param['ad_pop_zk_type'] = $param->ad_pop_zk_type;
        }
        if ($param->ad_pop_zk != -1) {
            $outer_param['ad_pop_zk'] = $param->ad_pop_zk * 100;
        }
        if ($param->settlement_type === 'cpa' && $param->ad_price != -1) {
            $outer_param['ad_price'] = $param->ad_price;
        } elseif ($param->settlement_type === 'cps' && $param->cps_divide_rate != -1) {
            $outer_param['cps_divide_rate'] = $param->cps_divide_rate * 100;
        } elseif ($param->settlement_type === 'cpt' || $param->settlement_type === '免费') {
            // cpt和免费都要清0
            $outer_param['ad_pop_zk_type'] = 0;
            $outer_param['ad_pop_zk'] = 0;
        }
        try {
            (new SiteService())->groupEdit(new SiteGroupEditParam($outer_param), Container::getSession()->user_id, Container::getSession()->name);
        } catch (\Exception $exception) {
            throw new AppException('调用外部接口错误, 错误信息：' . $exception->getMessage());
        }

        // 更新泽达的数据
        $site_model = new SiteModel();
        unset($outer_param['platform'], $outer_param['site_ids']);
        $outer_param['editor_id'] = $param->editor_id;
        $outer_param['editor'] = $param->editor;
        $outer_param['last_ad_pop_zk'] = $param->last_ad_pop_zk * 100;
        if ($param->tax_rate != -1) {
            $outer_param['tax_rate'] = $param->tax_rate;
        }
        if ($param->channel_fee_rate != -1) {
            $outer_param['channel_fee_rate'] = $param->channel_fee_rate;
        }
        $site_model->editInSiteId($param->platform, $param->site_id, $outer_param);

        // 更新数据中心的数据库
        $data = (new AgentModel())->getDataByPlatformAgentId($param->platform, $param->agent_id);
        $site_id_model = new V2DimSiteIdModel();
        $site_id_update_data['settlement_type'] = $param->settlement_type;
        // 不更改统计类型 这里没得更改的
//        $site_id_update_data['statistical_type'] = $data->statistic_caliber;
        if ($param->ad_pop_zk_type != -1) {
            $site_id_update_data['deduction_type'] = $param->ad_pop_zk_type;
        }
        if ($param->ad_pop_zk != -1) {
            $site_id_update_data['deduction_value'] = $param->ad_pop_zk;
        }
        if ($param->tax_rate != -1) {
            $site_id_update_data['tax_rate'] = $param->tax_rate;
        }
        if ($param->channel_fee_rate != -1) {
            $site_id_update_data['channel_fee_rate'] = $param->channel_fee_rate;
        }
        if ($param->settlement_type === 'cps' && $param->cps_divide_rate != -1) {
            $site_id_update_data['settlement_base_value'] = $param->cps_divide_rate;
        } else if ($param->settlement_type === 'cpa' && $param->ad_price != -1) {
            $site_id_update_data['settlement_base_value'] = $param->ad_price;
        } elseif ($param->settlement_type === 'cpt' || $param->settlement_type === '免费') {
            // cpt和免费都清0
            $site_id_update_data['settlement_base_value'] = 0;
            $site_id_update_data['deduction_type'] = 0;
            $site_id_update_data['deduction_value'] = 0;
            $site_id_update_data['statistical_type'] = 0;
            $site_id_update_data['tax_rate'] = 0;
            $site_id_update_data['channel_fee_rate'] = 0;
        }

        $site_id_model->editInSiteId($param->platform, $param->site_id, $site_id_update_data);

    }


    /**
     * 批量导入
     *
     * @param $data_list
     *
     * @throws Exception
     */
    public function importSiteDate($data_list)
    {
        // 数据格式校验
        $platform_site_data = $this->importDataCheck($data_list);

        // 逻辑性校验
        $insert_data_list = $this->importLogicCheck($data_list, $platform_site_data);

        $logger = Helpers::getLogger('site_date_import');

        // 插入数据
        foreach ($insert_data_list as $index => $insert_data) {
            $param = new OuterSiteListParam($insert_data);
            $agent_info = (new AgentModel())->getDataByPlatformAgentId($param->platform, $param->agent_id);
            // 这几个字段如果为空，则拿agent里面的去填充
            if (!$param->bank_holder) {
                $param->bank_holder = $agent_info->bank_holder;
            }
            if (!$param->bank_name) {
                $param->bank_name = $agent_info->bank_name;
            }
            if (!$param->bank_card_number) {
                $param->bank_card_number = $agent_info->bank_card_number;
            }
            try {
                $this->addSiteDate($param);
                $this->editSite($param);
            } catch (\Throwable $exception) {
                $logger->error('第' . ($index + 2) . '行发生错误,错误信息：' . $exception->getMessage(), ['insert_data' => $insert_data]);
                throw new AppException('第' . ($index + 2) . '行发生错误,错误信息：' . $exception->getMessage());
            }
        }
    }

    /**
     * 获取结算或者未结算列表
     *
     * @param SiteSettlementListParam $param
     *
     * @return array
     */
    public function getSettlementList(SiteSettlementListParam $param)
    {
        $model = new SiteSettlementModel();
        $data = $model->getList($param);

        $map = $this->getProjectTeamMap();

        // 判断合计是否精准
        $is_accurate = ($data['list']->count() < $param->limit) ? 1 : 0;
        foreach ($data['list'] as $item) {
            $item->settlement_money = Math::decimal($item->settlement_money, 2);
            $item->exclude_channel_fee_money = Math::decimal($item->exclude_channel_fee_money, 2);
            $item->is_channel = $item->is_channel == 1 ? '发行' : ($item->is_channel == 0 ? '买量' : '未知');
            $item->project_team_name = $map[$item->project_team_id]->project_team ?? '';
            $item->desc = $item->remark;
            unset($item->remark);
        }
        $sum = $model->getSumData($param);
        if ($sum) {
            $sum->settlement_money = Math::decimal($sum->settlement_money, 2);
            $sum->exclude_channel_fee_money = Math::decimal($sum->exclude_channel_fee_money, 2);
            $sum->log_value = Math::decimal($sum->log_value, 2);
            $sum->log_true_value = Math::decimal($sum->log_true_value, 2);
        }

        $data['sum'] = $sum;
        $data['is_accurate'] = $is_accurate;
        return $data;
    }

    /**
     * 结算
     *
     * @param $ids
     *
     * @return array
     * @throws Exception
     */
    public function settleTmp($ids)
    {
        $settlement_model = new SiteSettlementModel();
        $data_list = $settlement_model->getListByIds($ids, SiteSettlementModel::SETTLED);
        if (count($ids) !== $data_list->count()) {
            throw new AppException('传入的id错误');
        }
        // 组装成导入的数据，录入消耗
        $data[] = ['platform', 'game_id', 'site_id', 'money_type', 'begin_date', 'end_date', 'money', 'ori_money', 'remark'];
        $data_num_index_list = [];
        foreach ($data_list as $index => $item) {
            if ($item->settlement_base_value <= 0) {
                throw new AppException('结算单价和分成比例不能是0');
            }
            $arr = [$item->platform, $item->game_id, $item->site_id, 1, $item->log_date, $item->log_date, $item->settlement_money, 0, ''];
            $data[] = $arr;
            $data_num_index_list[$index + 2] = $item;
        }

        try {
            (new CostInputLogic())->importCostDataTMP($data, 13);
            // 更新为已结算
//            $settlement_model->updateType($ids, SiteSettlementModel::SETTLED);

        } catch (AppException $exception) {
            $error_message = $exception->getMessage();
            // 匹配 "行" 错误
            if (preg_match("/第[\d]+/", $error_message, $parts)) {
                $error_index = substr($parts[0], 3);
                $true_message = substr($error_message, stripos($error_message, '不正确，') + 12);
                $error_data = $data_num_index_list[$error_index];
                $platform = EnvConfig::PLATFORM_MAP[$error_data->platform];
                $res_message = $true_message . " 平台：$platform, 广告位id: $error_data->site_id, 游戏id: $error_data->game_id,日期: $error_data->log_date";
                throw new AppException($res_message);
            } else {
                // 没匹配到继续抛异常
                throw new AppException($error_message);
            }
        }


        return [];
    }


    /**
     * 结算
     *
     * @param $ids
     *
     * @return array
     * @throws Exception
     */
    public function settle($ids)
    {
        $settlement_model = new SiteSettlementModel();
        $data_list = $settlement_model->getListByIds($ids, SiteSettlementModel::UN_SETTLEMENT);
        if (count($ids) !== $data_list->count()) {
            throw new AppException('传入的id错误');
        }
        // 组装成导入的数据，录入消耗
        $data[] = ['platform', 'game_id', 'site_id', 'money_type', 'begin_date', 'end_date', 'money', 'ori_money', 'remark'];
        $data_num_index_list = [];
        foreach ($data_list as $index => $item) {
            if ($item->settlement_base_value <= 0) {
                throw new AppException('结算单价和分成比例不能是0');
            }
            $arr = [$item->platform, $item->game_id, $item->site_id, 1, $item->log_date, $item->log_date, $item->settlement_money, 0, ''];
            $data[] = $arr;
            $data_num_index_list[$index + 2] = $item;
        }

        try {
            (new CostInputLogic())->importCostData($data, 13);
            // 更新为已结算
            $settlement_model->updateType($ids, SiteSettlementModel::SETTLED, Container::getSession()->name);

        } catch (AppException $exception) {
            $error_message = $exception->getMessage();
            // 匹配 "行" 错误
            if (preg_match("/第[\d]+/", $error_message, $parts)) {
                $error_index = substr($parts[0], 3);
                $true_message = substr($error_message, stripos($error_message, '不正确，') + 12);
                $error_data = $data_num_index_list[$error_index];
                $platform = EnvConfig::PLATFORM_MAP[$error_data->platform];
                $res_message = $true_message . " 平台：$platform, 广告位id: $error_data->site_id, 游戏id: $error_data->game_id,日期: $error_data->log_date";
                throw new AppException($res_message);
            } else {
                // 没匹配到继续抛异常
                throw new AppException($error_message);
            }
        }


        return [];
    }

    /**
     * 生成结算单
     *
     * @param $ids
     *
     * @return array
     */
    public function createBill($ids)
    {
        $settlement_model = new SiteSettlementModel();
        $data_list = $settlement_model->getListByIdsWithNetSettlementAmount($ids);
        if (count($ids) !== $data_list->count()) {
            throw new AppException('传入的id错误，其中有未结算id');
        }
        // 分成两大类，1.cps 2.cpa 有且仅有两类
        $class_list = $data_list->groupBy('settlement_type');

        // 分类处理
        $cpa_file_names = $cps_file_names = [];

        // 生成唯一临时文件夹
        $dir = TMP_DIR . '/settlement/' . md5(Container::getUUID());
        if (!file_exists($dir)) {
            mkdir($dir, 0777, true);
        }
        foreach ($class_list as $settlement_type => $item_list) {
            if (!in_array($settlement_type, ['cps', 'cpa'])) {
                throw new AppException('数据错误');
            }

            /**@var Collection $item_list */
            if ($settlement_type === 'cpa') {
                $cpa_file_names = $this->createCPABill($item_list, $dir);
            } else {
                $cps_file_names = $this->createCPSBill($item_list, $dir);
            }
        }

        // 生成的文件名
        $filenames = array_merge($cpa_file_names, $cps_file_names);

        // 如果是单文件 则不需要打包成zip
        if (count($filenames) === 1) {
            return [
                'filename'          => $filenames[0],
                'download_filename' => substr($filenames[0], strlen($dir) + 1)
            ];
        }

        // 加入zip文件
        $zip_filename = $dir . '/' . md5(Container::getUUID()) . '.zip';
        $zip = new ZipArchive();
        $zip->open($zip_filename, ZipArchive::CREATE);
        foreach ($filenames as $filename) {
            $zip->addFile($filename, basename($filename));
        }

        $zip->close();
        // 删除生成的临时文件
        foreach ($filenames as $filename) {
            @unlink($filename);
        }
        $date = date("YmdHis");
        return [
            'filename'          => $zip_filename,
            'download_filename' => "结算单-$date.zip"
        ];
    }


    /**
     * 删除已结算
     *
     * @param $ids
     * @param $cost_input_id
     * @param string $desc
     */
    public function deleteSettlement($ids, $cost_input_id, string $desc = '')
    {
        $settlement_model = new SiteSettlementModel();
        $cost_input_logic = new CostInputLogic();
        $data_list = $settlement_model->getListByIds($ids, SiteSettlementModel::SETTLED);
        if (count($ids) !== $data_list->count()) {
            throw new AppException('传入的id错误，其中有未结算id');
        }

        // 判断一下订单
        $data_list->where('trade_nos', '!=', '')->each(function ($item) {
            throw new AppException("存在已锁定订单的记录，不能删除。site_id：{$item->site_id},game_id：{$item->game_id},日期：{$item->log_date}");
        });

        $delete_data_list = [];
        foreach ($data_list as $item) {
            $item = (array)$item;
            $item['tdate'] = $item['log_date'];
            $item['money_type'] = 1;
            $delete_data_list[] = $item;
        }
        try {
            $cost_input_logic->deleteCostInput($delete_data_list, $cost_input_id);
            // 最后更新为未结算
            $settlement_model->updateType($ids, SiteSettlementModel::UN_SETTLEMENT, Container::getSession()->name, $desc);
        } catch (Exception $e) {
            Helpers::getLogger('settlement')->error('消耗删除失败：', ['failed' => $e->getMessage()]);
            throw new AppException('消耗数据删除失败，请联系管理员');
        }
    }


    public function flushSettlementData($start_date, $end_date)
    {
        $site_settlement_model = new SiteSettlementModel();
        $logger = Helpers::getLogger('pull_settlement_data');
        $logger->info('任务开始');

        // 拉取数据
        $logger->info('开始拉取数据');
        $list = (new PullDataModel())->getSettlementData($start_date, $end_date);


        if (empty($list)) {
            $logger->info('拉取的数据为空，退出脚本');
            return;
        }

        $logger->info('数据拉取完成。开始删除表数据');
        try {
            MysqlConnection::getConnection('default')->beginTransaction();
        } catch (Exception $e) {
            $logger->error('事务开启异常，结束任务。');
            return;
        }

        try {
            // 先删除之前的数据
            $site_settlement_model->deleteByDateRange($start_date, $end_date);
            $logger->info('表删除完成，开始插入数据');
            $now = date("Y-m-d H:i:s");
            // 分批次插入 一次1000
            $offset = 0;
            while (1) {
                $insert_data = [];
                $f_list = array_slice($list, $offset, 1000);
                if (empty($f_list)) {
                    $logger->info('插入完成，开始更新数据');
                    break;
                }
                foreach ($f_list as $index => $item) {
                    // 过滤真实量不大于0的
                    if ($item->log_true_value <= 0) {
                        continue;
                    }
                    $insert_data[] = [
                        'platform'              => $item->platform,
                        'agent_group_name'      => $item->agent_group_name,
                        'agent_id'              => $item->agent_id,
                        'agent_name'            => $item->agent_name,
                        'agent_leader'          => $item->agent_leader,
                        'site_id'               => $item->site_id,
                        'site_name'             => $item->site_name,
                        'game_id'               => $item->game_id,
                        'game_name'             => $item->game_name,
                        'settlement_type'       => $item->settlement_type,
                        'settlement_base_value' => 0, // 后面手动更新
                        'deduction_value'       => $item->deduction_value,
                        'log_date'              => $item->log_date,
                        'log_value'             => $item->log_value,
                        'log_true_value'        => $item->log_true_value,
                        'tax_rate'              => $item->tax_rate,
                        'channel_fee_rate'      => $item->channel_fee_rate,
                        'is_channel'            => $item->is_channel,
                        'os'                    => $item->os,
                        'type'                  => 1,// 未结算
                        'create_time'           => $now,
                    ];
                }


                $site_settlement_model->addMultipleIgnore($insert_data);
                $offset += 1000;
                $logger->info('插入数据成功');
            }

            // 连表更新分成比例或广告单价 还有开户人
            $site_settlement_model->updateSettlementBaseValue($start_date, $end_date);
            $logger->info('数据更新成功，任务结束');
            MysqlConnection::getConnection('default')->commit();
        } catch (\Throwable $throwable) {
            $logger->error('任务出错，结束任务。错误信息:' . $throwable->getMessage());
            try {
                MysqlConnection::getConnection('default')->rollBack();
            } catch (\Exception $e) {
                $logger->error('事务回滚异常！');
            }
        }
    }

    public function flushDeduction($platform, $site_id, $start_time, $end_time)
    {
        $site_model = new V2DimSiteIdModel();
        $site_info = $site_model->getDataBySite($platform, $site_id);
        if (!$site_info) {
            throw new AppException('广告位信息不存在');
        }

        if ((strtotime($end_time) - strtotime($start_time)) >= 2592000) {
            throw new AppException('时间跨度不能超过30天');
        }

        $api = 'https://zx-exector.zx.com/executor/call/TASK/';
        // 不同统计口径刷不同接口
        switch (true) {
            case ($site_info->statistical_type == 1 && $site_info->settlement_type === 'cpa'):
                $api .= 'uid_reg_update_is_foreign_by_site_id';
                break;
            case ($site_info->statistical_type == 2 && $site_info->settlement_type === 'cpa'):
                $api .= 'game_reg_update_is_foreign_by_site_id';
                break;
            case ($site_info->statistical_type == 3 && $site_info->settlement_type === 'cpa'):
                $api .= 'root_game_reg_update_is_foreign_by_site_id';
                break;
            case ($site_info->statistical_type == 1 && $site_info->settlement_type === 'cps'):
                $api .= 'pay_order_update_is_foreign_by_uid_reg';
                break;
            case ($site_info->statistical_type == 2 && $site_info->settlement_type === 'cps'):
                $api .= 'pay_order_update_is_foreign_by_game_reg';
                break;
            case ($site_info->statistical_type == 3 && $site_info->settlement_type === 'cps'):
                $api .= 'pay_order_update_is_foreign_by_root_game_reg';
                break;
            default:
                throw new AppException('非CPA或CPS的渠道请勿操作');
        }

        $data = [
            'async'      => true,
            'simulation' => EnvConfig::ENV !== Environment::PROD, // 是否模拟执行 生产环境才真正执行
            'bindings'   => [
                'start_time' => date("Y-m-d 00:00:00", strtotime($start_time)),
                'end_time'   => date("Y-m-d 23:59:59", strtotime($end_time)),
                'platform'   => $platform,
                'site_id'    => (string)$site_id, // Java那边需要字符串
            ],
            'remark'     => ''
        ];
        $this->post($api, json_encode($data), [
            'header' => [
                'Content-Type: application/json',
            ]
        ]);
    }


    public function flushSettlementBaseValue($start_date, $end_date)
    {
        // 连表更新分成比例或广告单价
        $site_settlement_model = new SiteSettlementModel();
        $site_settlement_model->updateSettlementBaseValue($start_date, $end_date);
    }

    /**
     * 生成cpa结算单
     *
     * @param Collection $list
     *
     * @param string $dir
     *
     * @return array
     */
    private function createCPABill(Collection $list, $dir)
    {
        $agent_model = new AgentModel();
        $file_names = [];
        // 按平台+渠道分文件
        $file_dimension_list = $this->groupByDimension($list, ['platform', 'agent_id']);
        foreach ($file_dimension_list as $platform_agent_key => $item_list) {
            // 获取渠道信息
            $platform_agent = explode('|', $platform_agent_key);
            $agent_info = $agent_model->getDataByPlatformAgentId($platform_agent[0], $platform_agent[1]);
            if (!$agent_info) {
                throw new AppException('渠道信息不存在，数据异常');
            }

            // 单文件按site_id分行，然后每行数据汇总
            $site_dimension_list = $this->groupByDimension($item_list, ['site_id']);
            $site_id_list = [];
            $total_log_value = $total_settle_money = $agent_leader = $agent_name = $agent_group_name = $total_net_settlement_amount = $our_company = 0;
            $all_min_time = time();
            $all_max_time = 0;
            foreach ($site_dimension_list as $key => $sub_list) {
                $log_value = $settlement_money = $site_id = 0;
                $site_name = $start_time = $end_time = $tax_rate = $settlement_base_value = $net_settlement_amount = 0;
                foreach ($sub_list as $index => $value) {
                    $time = strtotime($value->log_date);
                    if ($index == 0) {
                        // 赋初始值
                        $start_time = $time;
                        $end_time = $time;
                        $tax_rate = $value->tax_rate;
                        $site_id = $value->site_id;
                        $site_name = $value->site_name;
                        $settlement_base_value = $value->settlement_base_value;
                        $agent_leader = $value->agent_leader;
                        $agent_name = $value->agent_name;
                        $agent_group_name = $value->agent_group_name;
                        $our_company = $value->our_company;
                    }
                    $log_value += $value->log_value;
                    $settlement_money += $value->settlement_money;
                    $net_settlement_amount += $value->net_settlement_amount;

                    // 总数
                    $total_log_value += $value->log_value;
                    $total_net_settlement_amount += $value->net_settlement_amount;

                    // 开始时间和结束时间
                    if ($time > $end_time) $end_time = $time;
                    if ($time < $start_time) $start_time = $time;

                    // 总的时间周期
                    if ($time > $all_max_time) $all_max_time = $time;
                    if ($time < $all_min_time) $all_min_time = $time;
                }
                $settlement_money = round($settlement_money, 2);
                $tmp['time'] = date('n.j', $start_time) . ' - ' . date('n.j', $end_time);
                $tmp['agent_name'] = $agent_name;
                $tmp['site_id_name'] = $site_name . "[$site_id]";
                $tmp['log_value'] = $log_value;
                $tmp['settlement_base_value'] = $settlement_base_value;
                $tmp['tax_rate'] = $tax_rate;
                $tmp['settlement_money'] = $settlement_money;

                // 跳过结算金额为0的广告位
                if ($settlement_money == 0) {
                    continue;
                }

                // 总结算金额，放这里算，这样尾数能保持一致
                $total_settle_money += $settlement_money;
                $site_id_list[$key] = $tmp;
            }

            // 遍历生成${$site_date_list}
            $site_date_list = '';
            foreach ($site_id_list as $item) {
                $site_date_list .= '
                <tr style="height: 25px">
                    <td align="center">' . $item['time'] . '</td>
                    <td align="center">' . $item['agent_name'] . '</td>
                    <td align="center">' . $item['site_id_name'] . '</td>
                    <td align="center">' . round($item['log_value'], 0) . '</td>
                    <td align="center">￥' . round($item['settlement_base_value'], 2) . '</td>
                    <td align="center">' . round($item['tax_rate'] * 100, 2) . '%</td>
                    <td align="center">￥' . $item['settlement_money'] . '</td>
                </tr>';
            }

            // 新新的CPA特殊配置
            if ($agent_info->platform === 'xinxin') {
                $agent_leader_address = BillTemplate::PLATFORM_MAP['xinxinCPA']['agent_leader_address'];
                $our_company = BillTemplate::PLATFORM_MAP['xinxinCPA']['invoice'];
                $our_bank = BillTemplate::PLATFORM_MAP['xinxinCPA']['bank_name'];
                $our_bank_number = BillTemplate::PLATFORM_MAP['xinxinCPA']['bank_card_number'];
                $agent_leader_mobile = BillTemplate::PLATFORM_MAP['xinxinCPA']['mobile'];
                $agent_leader = BillTemplate::PLATFORM_MAP['xinxinCPA']['agent_leader_name'];
            } // 广州贪玩的处理
            else if (in_array($agent_info->agent_id, BillTemplate::GZTW_AGENT_ID_LIST) && $agent_info->platform === 'TW') {
                $agent_leader_address = BillTemplate::PLATFORM_MAP['GZTW']['agent_leader_address'] ?? '';
                $our_company = BillTemplate::PLATFORM_MAP['GZTW']['invoice'] ?? '';
                $our_bank = BillTemplate::PLATFORM_MAP['GZTW']['bank_name'] ?? '';
                $our_bank_number = BillTemplate::PLATFORM_MAP['GZTW']['bank_card_number'] ?? '';
                $agent_leader_mobile = BillTemplate::MOBILE_MAP[$agent_leader] ?? '';
            } else {
                // 我方公司信息
                if ($agent_group_name == '短信' && $agent_info->platform === 'TW') {
                    $agent_leader_address = BillTemplate::PLATFORM_MAP['DX']['agent_leader_address'];
                    $our_company = BillTemplate::PLATFORM_MAP['DX']['invoice'];
                    $our_bank = BillTemplate::PLATFORM_MAP['DX']['bank_name'];
                    $our_bank_number = BillTemplate::PLATFORM_MAP['DX']['bank_card_number'];
                } else {
                    // 用查表出来的our_company，是一个简称
                    $agent_leader_address = BillTemplate::COMPANY_MAP[$our_company]['agent_leader_address'] ?? '';
                    $our_bank = BillTemplate::COMPANY_MAP[$our_company]['bank_name'] ?? '';
                    $our_bank_number = BillTemplate::COMPANY_MAP[$our_company]['bank_card_number'] ?? '';

                    // 映射成完整的公司主体名字
                    $our_company = BillTemplate::COMPANY_MAP[$our_company]['invoice'];

                }
                $agent_leader_mobile = BillTemplate::MOBILE_MAP[$agent_leader] ?? '';

                // 万紫特殊配置一波
                if ($agent_info->platform === 'wanzi') {
                    $agent_leader_mobile = BillTemplate::PLATFORM_MAP['wanzi']['mobile'];
                    $agent_leader = BillTemplate::PLATFORM_MAP['wanzi']['agent_leader_name'];
                }
            }
            $total_net_settlement_amount = round($total_net_settlement_amount, 2);
            $all_time = date("Y-m-d", $all_min_time) . ' - ' . date("Y-m-d", $all_max_time);
            $find = [
                '{$agent_name}', '{$agent_id}', '{$platform}', '{$all_time}', '{$now_date}', '{$site_date_list}', '{$total_log_value}',
                '{$total_settle_money}', '{$agent_leader}', '{$agent_leader_mobile}', '{$agent_leader_address}',
                '{$our_company}', '{$our_bank}', '{$our_bank_number}', '{$other_mobile}', '{$other_address}',
                '{$other_bank_holder}', '{$other_bank}', '{$other_bank_number}', '{$other_company}', '{$total_net_settlement_amount}',
            ];
            $replace = [
                $agent_name, $agent_info->agent_id, EnvConfig::PLATFORM_MAP[$agent_info->platform], $all_time, date("Y-m-d"), $site_date_list, $total_log_value,
                round($total_settle_money, 2), $agent_leader, $agent_leader_mobile, $agent_leader_address,
                $our_company, $our_bank, $our_bank_number, $agent_info->mobile, $agent_info->address,
                $agent_info->bank_holder, $agent_info->bank_name, $agent_info->bank_card_number, $agent_info->bank_holder, $total_net_settlement_amount
            ];

            // 生成文件名
            $file_name = $dir . '/' . $agent_name . '&' . EnvConfig::PLATFORM_MAP[$agent_info->platform] . 'CPA结算确认函';
            $file_name .= '(' . date('n.j', $all_min_time) . '-' . date('n.j', $all_max_time) . ').xls';
            $file_names[] = $file_name;
            $res_str = str_replace($find, $replace, BillTemplate::CPA);
            file_put_contents($file_name, $res_str);
        }


        return $file_names;
    }

    /**
     * 生成cps结算单
     *
     * @param Collection $list
     *
     * @param string $dir
     *
     * @return array
     */
    private function createCPSBill(Collection $list, string $dir)
    {
        $file_names = [];

        // 按平台+渠道分文件 改：按我放信息跟对方公司名称分文件
        $file_dimension_list = $list->groupBy(function ($item) {
            if ($item->agent_group_name == '短信') {
                $our_company = BillTemplate::PLATFORM_MAP['DX']['invoice'];
            } else if (in_array($item->agent_id, BillTemplate::GZTW_AGENT_ID_LIST) && $item->platform === 'TW') {
                $our_company = BillTemplate::PLATFORM_MAP['GZTW']['invoice'] ?? '';
            } else {
                $our_company = $item->our_company;
            }

            return $our_company . '|' . $item->bank_holder;
        });

//        $file_dimension_list = $this->groupByDimension($list, ['platform', 'agent_id']);
        foreach ($file_dimension_list as $info_key => $item_list) {
//            // 获取分割信息
            $platform_agent = explode('|', $info_key);
            $our_company = $platform_agent[0];
            $other_company = $platform_agent[1];
            $total_money = $total_channel_fee = $total_settle_money = $agent_leader = $total_net_settlement_amount = 0;
            $bank_holder = $bank_card_number = $agent_group_name = $bank_name = $platform = '';
            $agent_id = 0;
            $all_min_time = time();
            $all_max_time = 0;


            $result = $item_list
                ->sort(function ($a, $b) {
                    // 先按site_id排序
                    if ($a->site_id > $b->site_id) return -1;
                    if ($a->site_id < $b->site_id) return 1;

                    // 如果site_id相同，再按log_date排序
                    return strcmp($a->log_date, $b->log_date);
                })
                ->values() // 重置键名确保顺序正确
                ->reduce(function ($carry, $item) use (
                    &$bank_holder,
                    &$bank_card_number,
                    &$agent_group_name,
                    &$bank_name,
                    &$platform,
                    &$agent_id,
                    &$total_money,
                    &$total_channel_fee,
                    &$total_settle_money,
                    &$agent_leader,
                    &$total_net_settlement_amount,
                    &$all_max_time,
                    &$all_min_time
                ) {
                    // 总的时间周期
                    $time = strtotime($item->log_date);
                    $all_max_time = max($all_max_time, $time);
                    $all_min_time = min($all_min_time, $time);

                    // 维度
                    if ($item->bank_holder) {
                        $bank_holder = $item->bank_holder;
                    }
                    if ($item->bank_card_number) {
                        $bank_card_number = $item->bank_card_number;
                    }
                    if ($item->bank_name) {
                        $bank_name = $item->bank_name;
                    }
                    $platform = $item->platform;
                    $agent_group_name = $item->agent_group_name;
                    $agent_leader = $item->agent_leader;

                    // 计算总数
                    $total_money += $item->log_value;
                    $channel_fee_tmp = $item->log_value * $item->channel_fee_rate;
                    $total_channel_fee += $channel_fee_tmp;
                    $total_net_settlement_amount += $item->net_settlement_amount;


                    // 首次迭代初始化第一个分组
                    if (is_null($carry['current'])) {
                        $carry['current'] = [
                            'time'                  => date('n.j', strtotime($item->log_date)) . ' - ' . date('n.j', strtotime($item->log_date)),
                            'site_id_name'          => $item->site_id . '-' . $item->site_name,
                            'channel_fee_rate'      => $item->channel_fee_rate,
                            'tax_rate'              => $item->tax_rate,
                            'channel_fee'           => $item->log_value * $item->channel_fee_rate,
                            'settlement_base_value' => $item->settlement_base_value,
                            'log_value'             => $item->log_value,
                            'settlement_money'      => $item->settlement_money,
                            'net_settlement_amount' => $item->net_settlement_amount,
                            'agent_id_name'         => $item->agent_name . '-' . $item->agent_id,
                            'start_date'            => $item->log_date,
                            'end_date'              => $item->log_date,
                            'site_id'               => $item->site_id,
                        ];
                        return $carry;
                    }


                    // 检查分组条件
                    $isSameGroup =
                        $carry['current']['site_id'] === $item->site_id &&
                        $carry['current']['settlement_base_value'] === $item->settlement_base_value &&
                        $carry['current']['channel_fee_rate'] === $item->channel_fee_rate &&
                        $carry['current']['tax_rate'] === $item->tax_rate;

                    // 满足条件时扩展当前分组
                    if ($isSameGroup) {
                        $carry['current']['end_date'] = $item->log_date;

                        // 聚合字段计算
                        $carry['current']['channel_fee'] += $channel_fee_tmp;
                        $carry['current']['log_value'] += $item->log_value;
                        $carry['current']['settlement_money'] += $item->settlement_money;
                        $carry['current']['net_settlement_amount'] += $item->net_settlement_amount;
                        $carry['current']['time'] = date('n.j', strtotime($carry['current']['start_date'])) . ' - ' . date('n.j', strtotime($carry['current']['end_date']));
                    } else {
                        // 保存当前分组并创建新分组
                        $carry['result'][] = $carry['current'];
                        $carry['current'] = [
                            'time'                  => date('n.j', strtotime($item->log_date)) . ' - ' . date('n.j', strtotime($item->log_date)),
                            'site_id_name'          => $item->site_id . '-' . $item->site_name,
                            'channel_fee_rate'      => $item->channel_fee_rate,
                            'tax_rate'              => $item->tax_rate,
                            'channel_fee'           => $channel_fee_tmp,
                            'settlement_base_value' => $item->settlement_base_value,
                            'log_value'             => $item->log_value,
                            'settlement_money'      => $item->settlement_money,
                            'net_settlement_amount' => $item->net_settlement_amount,
                            'agent_id_name'         => $item->agent_name . '-' . $item->agent_id,
                            'start_date'            => $item->log_date,
                            'end_date'              => $item->log_date,
                            'site_id'               => $item->site_id,
                        ];
                    }

                    return $carry;
                }, ['result' => [], 'current' => null]);

            // 处理最后一个分组
            if (!is_null($result['current'])) {
                $result['result'][] = $result['current'];
            }

            $site_id_list = $result['result'];
            foreach ($site_id_list as $index => &$site_data) {
                // 跳过结算金额为0的广告位
                if ($site_data['settlement_money'] == 0) {
                    unset($site_id_list[$index]);
                    continue;
                }
                $site_data['settlement_money'] = round($site_data['settlement_money'], 2);
                // 总结算金额，放这里算，这样尾数能保持一致
                $total_settle_money += $site_data['settlement_money'];
            }

            // 遍历生成${$site_date_list}
            $site_date_list = '';
            foreach ($site_id_list as $item) {
                $site_date_list .= '
                <tr style="height: 25px">
                <td align="center">&nbsp;' . $item['time'] . '</td>
                <td align="center">' . $item['agent_id_name'] . '</td>
                <td align="center">' . $item['site_id_name'] . '</td>
                <td align="center">' . round($item['log_value'], 2) . '</td>
                <td align="center">' . round($item['channel_fee_rate'] * 100, 2) . '%</td>
                <td align="center">' . round($item['channel_fee'], 2) . '</td>
                <td align="center">' . round($item['tax_rate'] * 100, 2) . '%</td>
                <td align="center">' . round($item['settlement_base_value'] * 100, 2) . '%</td>
                <td align="center">' . $item['settlement_money'] . '</td>';
            }
            // 发票抬头
            if ($agent_group_name == '短信') {
                $our_invoice = BillTemplate::PLATFORM_MAP['DX']['invoice'];
                $agent_leader_address = BillTemplate::PLATFORM_MAP['DX']['agent_leader_address'];
            } else if (in_array($agent_id, BillTemplate::GZTW_AGENT_ID_LIST) && $platform === 'TW') {
                $our_invoice = BillTemplate::PLATFORM_MAP['GZTW']['invoice'] ?? '';
                $agent_leader_address = BillTemplate::PLATFORM_MAP['GZTW']['agent_leader_address'] ?? '';
            } else {
                $our_invoice = BillTemplate::COMPANY_MAP[$our_company]['invoice'] ?? '';
                $agent_leader_address = BillTemplate::COMPANY_MAP[$our_company]['agent_leader_address'] ?? '';

                // 变成全称
                $our_company = BillTemplate::COMPANY_MAP[$our_company]['invoice'] ?? '';
            }

            $all_time = date("Y-m-d", $all_min_time) . ' - ' . date("Y-m-d", $all_max_time);

            $total_net_settlement_amount = round($total_net_settlement_amount, 2);
//            // 老后台针对新新的结算单特殊处理
//            if ($platform === 'xinxin') {
//                $agent_leader = '刘希金';
//            }
            $agent_leader_mobile = BillTemplate::MOBILE_MAP[$agent_leader] ?? '';
            $find = [
                '{$title}', '{$all_time}', '{$site_date_list}', '{$total_money}',
                '{$total_channel_fee}', '{$total_settle_money}',
                '{$chinese_num}', '{$our_invoice}', '{$agent_leader}', '{$agent_leader_mobile}',
                '{$agent_leader_address}', '{$account_name}', '{$account_bank}',
                '{$account_number}', '{$our_company}', '{$other_company}', '{$net_settlement_amount}', '{$net_settlement_amount_chinese}'
            ];
            $replace = [
                $our_company . '&' . $other_company, $all_time, $site_date_list, round($total_money, 2),
                round($total_channel_fee, 2), round($total_settle_money, 2),
                Helpers::moneyToString($total_settle_money), $our_invoice, $agent_leader, $agent_leader_mobile,
                $agent_leader_address, $bank_holder, $bank_name,
                $bank_card_number, $our_company, $bank_holder, $total_net_settlement_amount, Helpers::moneyToString($total_net_settlement_amount)
            ];
            $res_str = str_replace($find, $replace, BillTemplate::CPS);
            // 生成文件名
            $file_name = $dir . '/' . $our_company . '&' . $other_company . 'CPS结算确认函';
            $file_name .= '(' . date('n.j', $all_min_time) . '-' . date('n.j', $all_max_time) . ').xls';
            $file_names[] = $file_name;
            file_put_contents($file_name, $res_str);
        }

//        $sum_file_name = $this->summarizeDataRows($dir, date("Y-m-d", $summarize_min_time), date("Y-m-d", $summarize_max_time), $summarize_data_list);
//        $file_names[] = $sum_file_name;

        return $file_names;
    }


    /**
     * 汇总导出的文件
     *
     * @param $start_time
     * @param $end_time
     * @param $summarize_data_list
     * @return array
     */
    public function summarizeDataRows($start_time, $end_time, $summarize_data_list)
    {
        $logic = new OuterLogic();
        $out_data_list = Collection::make();


        // 按统计口径分 site_id
        $site_list = [];
        foreach ($summarize_data_list as $item) {
            $site_list[$item['statistical_type']][] = $item;
        }

        // 按统计口径去查流水数据
        foreach ($site_list as $statistical_type => $data_list) {
            // 组装查询条件
            $values = [];// site_id的筛选条件
            foreach ($data_list as $item) {
                $values[] = EnvConfig::PLATFORM_MAP[$item['platform']] . '-' . $item['site_id'];
            }
            $dimension_filter = [
                'site' => [
                    'column'    => 'platform-site_id',
                    'comma'     => false,
                    'condition' => "in",
                    'confirm'   => true,
                    'name'      => "广告位ID",
                    'show'      => true,
                    'type'      => "int",
                    'value'     => $values,
                ]
            ];
            $condition = [
                'aggregation_time'  => '按日',
                'deduction'         => 1,
                'dimension'         => ['platform', 'site_id'],
                'start_time'        => $start_time,
                'end_time'          => $end_time,
                'statistic_caliber' => $statistical_type,
                'target'            => ['reg_uid_count', 'total_pay_money'],
                'dimension_filter'  => $dimension_filter,
                'limit'             => 1000000,
            ];
            $param = new OuterOverViewListFilterParam($condition);
            // 处理指标
            $param->handleTarget();
            $res = $logic->getOverviewList($param);
            $out_data_list = $out_data_list->merge($res['list']);
        }

        $out_data_list = $out_data_list->groupBy(function ($item) {
            return $item->platform . '|' . $item->site_id;
        });


        // 写入数据
        $res_list = [];
        foreach ($summarize_data_list as $item) {
            $key = $item['platform'] . "|" . $item['site_id'];
            // 合计数值
            $total_pay_money = $reg_uid_count = $cost_money = 0;
            $site_data_list = $out_data_list[$key] ?? Collection::make();
            /**@var$site_data_list  Collection */
            $site_data_list = $site_data_list->whereBetween('date', [$item['start_date'], $item['end_date']]);

            foreach ($site_data_list as $data) {
                $total_pay_money += $data->total_pay_money;
                $reg_uid_count += $data->reg_uid_count;
                $cost_money += $data->cost_money;
            }

            $res_list[] = [
                'our_company'               => $item['our_company'],
                'other_company'             => $item['other_company'],
                'agent_name'                => $item['agent_name'],
                'agent_id'                  => $item['agent_id'],
                'agent_group_name'          => $item['agent_group_name'],
                'operator'                  => $item['operator'],
                'project_team_name'         => $item['project_team_name'],
                'time'                      => $item['time'],
                'site_id_name'              => $item['site_id_name'],
                'game_id_name'              => $item['game_id'] . '-' . $item['game_name'],
                'agent_leader'              => $item['agent_leader'],
                'agent_leader_group_name'   => $item['agent_leader_group_name'],
                'statistical_type'          => $item['statistical_type'] == 1 ? "平台" : ($item['statistical_type'] == 2 ? "按子" : "按根"),
                'channel_fee_rate'          => $item['channel_fee_rate'],
                'channel_fee'               => $item['channel_fee'],
                'tax_rate'                  => $item['tax_rate'],
                'settlement_base_value'     => $item['settlement_base_value'],
                'settlement_money'          => Math::decimal($item['settlement_money'], 2),
                'exclude_channel_fee_money' => Math::decimal($item['exclude_channel_fee_money'], 2),
                'cost_money'                => Math::decimal($cost_money, 2),
                'log_value'                 => $item['log_value'],
                'total_pay_money'           => Math::decimal($total_pay_money, 2),
                'diff'                      => $total_pay_money - $item['log_value'],
                'reg_uid_count'             => $reg_uid_count,
                'is_channel'                => $item['is_channel'] == 1 ? '发行' : ($item['is_channel'] == 0 ? '买量' : '未知'),
                'os'                        => $item['os'],
                'live_order_ids'            => $item['live_order_ids'],
                //                'bank_holder'           => $item['bank_holder'],
            ];
        }


        return $res_list;
    }

    /**
     * 给出指定的集合数组，按给定的按维度分行
     *
     * @param mixed $list
     * @param array $dimensions
     *
     * @return array
     */
    private function groupByDimension($list, $dimensions)
    {
        $res_list = [];
        foreach ($list as $item) {
            $key = '';
            foreach ($dimensions as $dimension) {
                $key .= $item->$dimension . '|';
            }
            $res_list[$key][] = $item;
        }

        return $res_list;
    }

    /**
     * 批量导入时，数据格式的校验
     *
     * @param $data_list
     *
     * @return array  顺便返回platform，site_id数组
     */
    private function importDataCheck($data_list)
    {
        if (!(count($data_list) > 1)) {
            throw new AppException('导入的数据为空！');
        }

        // 拿出表头
        $header = $data_list[0];
        $header_length = count($header);
        $length = 7;

        // 验证表头长度：
        if ($header_length != $length) {
            throw new AppException('表头数据格式不正确');
        }
        $repeat = [];// 用于重复性检查
        $res_data = [];
        // 数据格式校验
        foreach ($data_list as $index => $data) {
            // 首部 跳过
            if ($index === 0) continue;

            foreach ($data as &$data_item) {
                $data_item = trim($data_item);
            }
            if (count($data) !== $header_length) {
                throw new AppException('第' . ($index + 1) . '行数据格式不正确，错误的单元格数');
            }
            if (!isset(EnvConfig::PLATFORM_MAP[$data[0]])) {
                throw new AppException('第' . ($index + 1) . '行数据格式不正确，错误的平台');
            }
            if (!Helpers::isInt($data[1]) || $data[1] <= 0) {
                throw new AppException('第' . ($index + 1) . '行数据格式不正确，错误的广告位id');
            }
            if (!Helpers::validDate($data[2])) {
                throw new AppException('第' . ($index + 1) . '行数据格式不正确，错误的日期格式。');
            }
            if (!(is_numeric($data[4]) && $data[4] <= 1 && $data[4] >= 0)) {
                throw new AppException('第' . ($index + 1) . '行数据格式不正确，错误的扣量比例');
            }
            if (!(is_numeric($data[5]) && $data[5] <= 1 && $data[5] >= 0)) {
                throw new AppException('第' . ($index + 1) . '行数据格式不正确，错误的渠道费率');
            }
            if (!(is_numeric($data[6]) && $data[6] <= 1 && $data[6] >= 0)) {
                throw new AppException('第' . ($index + 1) . '行数据格式不正确，错误的税率');
            }
            // 自身重复性校验
            $key = $data[0] . '_' . $data[1];
            if (isset($repeat[$key])) {
                throw new AppException('第' . ($index + 1) . '行数据格式不正确，自身数据有重复');
            }
            // 标记一下 用于重复性判断
            $repeat[$key] = 1;

            $res_data[] = ['platform' => $data[0], 'site_id' => $data[1]];
        }

        return $res_data;
    }

    /**
     * 批量导入的逻辑性校验，比如权限,site_id是否存在等等
     *
     * @param $data_list
     * @param $platform_site_data
     *
     * @return array
     */
    private function importLogicCheck($data_list, $platform_site_data)
    {
        $cost_input_logic = (new CostInputLogic());
        $agent_permission = $cost_input_logic->getAgentPermission();

        /*-- 构建site_list_index --*/
        $site_list = (new SiteModel())->getListInPlatformAndSite($platform_site_data);
        $site_list_index = [];
        $platform_agent_data = [];
        foreach ($site_list as $item) {
            $key = $item->platform . '|' . $item->site_id;
            $site_list_index[$key] = $item;
            $agent_key = $item->platform . '|' . $item->agent_id;
            $platform_agent_data[$agent_key] = ['platform' => $item->platform, 'agent_id' => $item->agent_id];
        }
        /*-- 构建site_list_index --*/

        /*-- 构建agent_list_index --*/
        $agent_list = (new AgentModel())->getAgentListByMultiplePlatformAndAgent($platform_agent_data);
        $agent_list_index = [];
        foreach ($agent_list as $item) {
            $agent_key = $item->platform . '|' . $item->agent_id;
            $agent_list_index[$agent_key] = $item;
        }
        /*-- 构建agent_list_index --*/

        // 获取这批platform_site_id的最大结算开始日期，录入的日期要大于这个
        $site_date_list = (new SiteDateModel())->getMaxStartDateByMultiplePlatformAndSite($platform_site_data);
        // 做个索引
        $site_date_list_index = [];
        foreach ($site_date_list as $item) {
            $key = $item->platform . '|' . $item->site_id;
            $site_date_list_index[$key] = $item;
        }

        $editor = Container::getSession()->get('name');
        $editor_id = Container::getSession()->get('user_id');
        $insert_data_list = []; // 等会要插入的数据
        foreach ($data_list as $index => $data) {
            // 首部 跳过
            if ($index === 0) continue;

            // 渠道信息是否正确
            $site_key = $data[0] . '|' . $data[1];
            if (!isset($site_list_index[$site_key])) {
                throw new AppException('第' . ($index + 1) . '行数据格式不正确，site_id不存在');
            }
            $agent_key = $data[0] . '|' . $site_list_index[$site_key]->agent_id;
            $pay_type = $agent_list_index[$agent_key]->pay_type;
            if ($pay_type == 3) {
                // cpa
                if (!Helpers::isInt($data[3]) || $data[3] <= 0) {
                    throw new AppException('第' . ($index + 1) . '行数据格式不正确，错误的结算单价');
                }
            } else if ($pay_type == 6) {
                // cps
                if (!(is_numeric($data[3]) && $data[3] <= 1 && $data[3] >= 0)) {
                    throw new AppException('第' . ($index + 1) . '行数据格式不正确，错误的分成比例');
                }
            } else {
                throw new AppException('第' . ($index + 1) . '行数据格式不正确，结算类型不是cps或cpa');
            }

            // 日期检查
            $max_start_date = $site_date_list_index[$site_key]->start_date ?? '2010-01-01';
            if (strtotime($max_start_date) >= strtotime($data[2])) {
                throw new AppException('第' . ($index + 1) . '行数据格式不正确，日期必须大于' . $max_start_date);
            }

            // 渠道权限检查
            if (!$cost_input_logic->checkAgentPermission($agent_permission, $data[0], $agent_list_index[$agent_key]->agent_id)) {
                throw new AppException('第' . ($index + 1) . '行数据格式不正确，您没有该渠道的权限');
            }

            $insert_data_list[] = [
                'platform'         => $data[0],
                'site_id'          => [$data[1]],// 搞成数组 兼容一下
                'agent_id'         => $agent_list_index[$agent_key]->agent_id,
                'settlement_type'  => $pay_type == 3 ? 'cpa' : 'cps',
                'start_date'       => date("Y-m-d", strtotime($data[2])), // 格式化一下比较稳
                'cps_divide_rate'  => $pay_type == 6 ? $data[3] : 0,
                'ad_price'         => $pay_type == 3 ? $data[3] : 0,
                'ad_pop_zk_type'   => $pay_type == 3 ? 1 : 2,
                'ad_pop_zk'        => $data[4],
                'channel_fee_rate' => $data[5],
                'tax_rate'         => $data[6],
                'editor_id'        => $editor_id,
                'editor'           => $editor,
                'bank_holder'      => $data[7] ?? '',
                'bank_name'        => $data[8] ?? '',
                'bank_card_number' => $data[9] ?? '',
            ];

        }

        return $insert_data_list;
    }

    /**
     * 处理全选的渠道组
     *
     * @param $user_id
     * @param $agent_group_list
     */
    private function handleAgentGroupAllPermission($user_id, $agent_group_list)
    {
        // 把ad_platform_list转成集合好处理  (按平台分组)
        $agent_group_list = collect($agent_group_list)->groupBy('platform');
        // 获取所有的平台
        $platform_list = $agent_group_list->keys();

        // 获取所有的list 然后比对
        $all_agent_group_list = (new V2DimAgentIdModel())->getAllAgentGroupByPlatformList($platform_list);

        $rank_permission_list = [];
        foreach ($agent_group_list as $platform => $list) {
            // 判断是不是全选  是的话入表。
            /*** @var $list Collection */
            $add_list = $all_agent_group_list->where('platform', $platform)->keyBy('agent_group_id')->diffKeys($list->keyBy('agent_group_id'));

            if ($add_list->isEmpty()) {
                $rank_permission_list [] = [
                    'user_id' => $user_id, 'platform' => $platform, 'type' => RankPermissionAllModel::TYPE_AGENT_GROUP
                ];
            }
        }

        if (!empty($rank_permission_list)) {
            (new RankPermissionAllModel())->replace($rank_permission_list);
        }
    }

    private function post($api, $data, $options)
    {
        $response = Helpers::postCurl($api, $data, $options);
        $decode_data = $this->decode($api, $data, $response);

        Helpers::getLogger("deduction")->info("$api", [
            'request_data'  => $data,
            'response_data' => json_decode($response, true),
            'options'       => $options,
        ]);
        return $decode_data;

    }

    private function decode($api, $data, $json)
    {
        if (empty($json)) {
            Helpers::getLogger('deduction')->error("$api return empty response", [
                'request_data' => $data
            ]);
            throw new AppException("$api 大数据平台无响应");
        }

        $response_data = json_decode($json, true);

        if ($response_data === null) {
            Helpers::getLogger('deduction')->error("$api return invalid json format", [
                'request_data' => $data,
                'response_str' => $json
            ]);
            throw new AppException("$api 大数据平台不合法的JSON格式:" . $json);
        }
        if ($response_data['status'] !== 0) {
            Helpers::getLogger('deduction')->error("$api return error code", [
                'request_data'  => $data,
                'response_data' => $response_data
            ]);
            throw new AppException($response_data['message'] ? "大数据平台返回错误信息: {$response_data['message']}" : '无返回信息');
        }
        return $response_data['data'];

    }

    /**
     * @param $user_id
     *
     * @return Collection
     */
    private function getRankAwemeList($user_id)
    {
        return (new RankAwemeModel())->getAllByUserId($user_id)->map(function ($item) {
            return [
                'aweme_account' => $item->aweme_account,
                'aweme_name'    => $item->aweme_name,
            ];
        });
    }

    /**
     * @param $user_id
     * @param $aweme_accounts
     * @param $platform
     *
     * @return bool
     */
    private function addRankAweme($user_id, $aweme_accounts, $platform)
    {
        if (empty($aweme_accounts)) {
            return true;
        }
        return (new RankAwemeModel())->addMultiple($user_id, $aweme_accounts, $platform);

    }

    public function getAwemeList($keyword, $page = 1, $rows = 50)
    {
        return (new V2DimAdAwemeModel())->getListLikeAwemeAccount(
            $keyword,
            [],
            (new PermissionLogic())->getLoginUserAgentPermission(),
            $page,
            $rows
        )->map(function ($item) {
            return [
                'aweme_name'    => $item->aweme_account . "-{$item->aweme_name}",
                'aweme_account' => $item->aweme_account,
            ];
        });
    }

    public function getProjectTeamMap()
    {
        return (new V2DimProjectTeamModel())->getAll()->keyBy('id')->toArray();
    }
}
