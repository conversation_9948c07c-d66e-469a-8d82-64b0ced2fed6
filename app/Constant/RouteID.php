<?php
/**
 * 路由id 跟数据库的路由表的id对应
 * User: melody
 * Date: 2019-08-14
 * Time: 15:06
 */

namespace App\Constant;


class RouteID
{
    /************************ DMS ***********************/
    const HOME_MAP = 2;                         // 数据地图
    const HOME_DASHBOARD = 3;                   // 数据看板
    const MARKET_OVERVIEW = 5;                  // 投放总览
    const MARKET_RETAIN = 6;                    // 留存情况
    const MARKET_PAYMENT = 7;                   // 付费情况
    const MARKET_ACTION_RETAIN = 231;           // 激活留存
    const OPERATION_OVERVIEW = 9;               // 运营总览
    const OPERATION_RETAIN = 10;                // 留存情况
    const OPERATION_PAYMENT = 11;               // 付费情况
    const PERMISSION_MEMBER = 13;               // 成员管理
    const DMP_DEVICE = 15;                      // 设备导出
    const INDIVIDUATION_DASHBOARD = 17;         // 看板展示
    const INDIVIDUATION_KPI = 18;               // KPI导入
    const INDIVIDUATION_FEEDBACK = 19;          // 用户反馈
    const TWDBLOG_DATA_QUERY = 28;              // 数据查询
    const DMP_DOWNLOAD_HISTORY = 23;            // 下载历史
    const DMP_AUDIENCE = 24;                    // 客户人群
    const MARKET_HOUR_OVERVIEW = 25;            // 分时总览
    const DMP_MEDIA_ACCOUNT = 26;               // 媒体账户
    const DMP_FILE_LIST = 27;                   // 文件列表
    const MARKET_SIMPLER_HOUR_OVERVIEW = 29;    // 投放分时数据
    const OPERATION_SIMPLER_HOUR_OVERVIEW = 30; // 运营分时数据
    const ADMIN_PUSH_MANAGER = 31;              // 推送管理
    const INDIVIDUATION_GAME_WARNING = 33;      // 游戏绑定
    const RECHARGE_RANK = 34;                   // 充值排行榜
    const DATA_CONFIG = 36;                     // 数据配置
    const EARLY_WARNING_STANDARD = 37;          // 预警标准
    const WASTAGE = 38;                         // 流失情况
    const OPERATION_PROFIT = 60;                // 运营利润
    const TAX_WRITE = 62;                       // 税务录入
    const FRONT_END_CONVERSION = 65;            // 前端转换
    const OPERATION_PROFIT_MAIN_CONFIG = 100021;// 运营利润-二级页面，主游戏配置列表 (route表是没有这个id的)
    const GAME_ONLINE = 75;                     // 游戏在线情况
    const PAY_ESTIMATE = 91;                    // 付费预估
    const ORDER_CONVERSION = 93;                // 订单转化
    const ORDER_COUNT = 217;                    // 订单统计
    const OUTER_MEMBER = 97;                    // 对外成员管理
    const OUTER_SITE_CONFIG = 98;               // 广告位配置
    const ACCOUNT_BIND = 100;                   // 账号绑定情况
    const COST_INPUT = 101;                     // 消耗录入
    const SERVER_ECOLOGY = 103;                 // 区服生态
    const SERVER_ECOLOGY_LAYERED_PAYMENT = 100022; // 区服生态-二级页面，分层付费配置列表 (route表是没有这个id的)
    const OUTER_OVERVIEW = 106;                 // 对外总览
    const OUTER_UN_SETTLEMENT = 107;            // 未结算列表
    const OUTER_SETTLED = 108;                  // 已结算列表
    const SERVER_DATA_OVERVIEW = 113;           // 服务概览
    const FLOW_ESTIMATE = 132;                  // 流水预估
    const AUDIT_OVERVIEW = 142;                 // 审计总览
    const FINANCE_ACCOUNT = 155;                // 财务台账
    const REBATE_INPUT = 167;                   // 台账信息录入
    const AGENT_REBATE = 176;                   // 返点消耗
    const MARKET_RECHARGE_RANK = 186;           // 投放总揽-充值排行榜
    const ABNORMAL_CLICK = 187;                 // 渠道异常点击
    const OPERATION_WEBSITE_RECHARGE = 191;     // 官网充值
    const TEAM_CONFIG_DATA = 202;               // 团队数据
    const PAYMENT_MONTH = 209;                  // 付费情况(自然月)
    const PRODUCT_SCORE = 207;                  // 产品评级
    const USER_GROUP = 210;                     // 用户分群
    const PAY_ESTIMATE_MONTH = 235;             // 付费预估(自然月)
    const OPERATION_HOUR_OVERVIEW = 237;        // 运营数据分析-分时总览
    const INTERNAL_USER = 242;                  // 运营数据分析-内部账号资源管理
    const COST_SHARE = 263;                     // 消耗分摊
    const TEAM_GROWTH_FACTOR = 265;             // 团队数据增长配置
    const AD_INSPIRE_COST = 269;                // 激励广告
    const OPERATION_ACTION_RETAIN = 277;        // 运营数据分析-激活留存
    const AD_INSPIRE_COST_MAIN_CONFIG = 100023; // 激励广告-二级页面，主游戏配置列表 (route表是没有这个id的)
    const DATA_CONFIG_AD_SLOT_ID = 100024;      // 数据配置-激励广告位代码位 (route表是没有这个id的)
    const DATA_CONFIG_GAME_DIMENSION = 100025;  // 数据配置-新老游戏配置 (route表是没有这个id的)
    const PROFIT_STATEMENT = 295;               // 数据管理-产品利润
    const USER_PROFIT = 300;                    // 运营数据分析-用户画像


    /************************ LY ***********************/
    const LY_MARKET_RETAIN = 52;                 // 留存情况
    const LY_MARKET_PAYMENT = 57;                // 付费情况
    const LY_MARKET_OVERVIEW = 58;               // 发行总览
    const LY_HOUR_OVERVIEW = 59;                 // 分时数据
    const LY_RECHARGE_RANK = 61;                 // 充值排行榜
    const LY_COST_INPUT = 64;                    // 消耗录入
    const LY_PERMISSION_MEMBER = 54;             // 成员管理
    const LY_DATA_CONFIG = 85;                   // 数据配置
    const LY_WASTAGE = 88;                       // 流失情况
    const LY_GAME_ONLINE = 89;                   // 游戏在线情况
    const LY_FRONT_END_CONVERSION = 90;          // 前端转换
    const LY_ACCOUNT_BIND = 92;                  // 账号绑定情况
    const LY_CHANNEL_PROFIT = 87;                // 发行利润
    const LY_ENTER_COST = 94;                    // 成本录入
    const LY_DATA_QUERY = 99;                    // 数据查询


    /************************ DSP **********************/
    const DSP_ADVERTISING_AGENT = 40;            // 渠道管理
    const DSP_ADVERTISING_LDY = 41;              // 落地页管理
    const DSP_ADVERTISING_SITE = 42;             // 广告配置管理
    const DSP_ADVERTISING_SDK = 46;              // SDK管理
    const DSP_ADVERTISING_GAME_RELATION = 201;   // 游戏关系
    const DSP_AD_ANALYSIS_REPORT = 49;           // 基础报表
    const DSP_PERMISSION_MEMBER = 56;            // 成员管理
    const DSP_AD_TARGETING = 67;                 // 广告定向包
    const DSP_AD_WORD = 68;                      // 广告文案包
    const DSP_AD_TAG = 69;                       // 广告标签包
    const DSP_AD_MATERIAL = 70;                  // 广告素材包
    const DSP_AD_ACCOUNT = 71;                   // 广告账号包
    const DSP_AD_SETTING = 72;                   // 广告参数包
    const DSP_AD_COMPOSE = 73;                   // 广告组合
    const DSP_AD_TASK = 74;
    // 广告任务
    const DSP_AD_INTELLIGENT_COMPOSE = 280;                      // 智创组合

    const DSP_AD_DIMENSION_MONITOR_ROBOT = 310;                  // 维度监控机器人
    const DSP_MATERIAL_STORE = 77;               // 素材库
    const DSP_MATERIAL_EFFECT = 81;              // 素材效果
    const DSP_MATERIAL_SHARE_RULE = 82;          // 素材效果
    const DSP_MEDIA_ACCOUNT = 84;                // 媒体账户
    const DSP_MATERIAL_FILE = 105;               // 素材文件
    const DSP_MATERIAL_AUTHOR_EFFECT = 114;      // 绩效统计
    const DSP_FINANCE_FUND = 140;                // 资金流水记录
    const DSP_FINANCE_REPORT = 141;              // 财务对账报表
    const DSP_FINANCE_SCREENSHOT_MISSION = 169;  // 截图任务
    const DSP_SHIELDED_WORD = 173;               // 屏蔽词
    const DSP_SHIELDED_USER = 174;               // 屏蔽用户
    const DSP_FINANCE_TRANSACTION_CHECK = 196;   // 转账校验
    const DSP_FINANCE_RETURN_GOODS_RECORD = 199; // 返货记录
    const DSP_FINANCE_MAIL = 219;                // 邮件结算
    const DSP_LIVE_USER = 236;                   // 直播账号
    const DSP_FINANCE_ACCOUNT_CENTER = 252;      // 台账中心
    const DSP_GAME_MANAGE = 260;                 // 游戏出包
    const DSP_AD_LIVE_ANALYSIS_REPORT = 270;     // 直播分析
    const DSP_AD_LIVE_AUDIO_ANALYSIS_REPORT = 273;     // 直播话术分析
    const DSP_ENTERPRISE_VIDEO_ANALYSIS_REPORT = 279;  // 企业视频号分析
    const DSP_TENCENT_MATERIAL_EFFECT = 281;     //腾讯3.0素材效果
    const DSP_MATERIAL_TOOL_AUTO_CLASSIFY = 284;     // 序列帧智能工具-自动分类
    const DSP_MATERIAL_TOOL_EXPAND = 285;            // 序列帧智能工具-序列帧拓展
    const DSP_MATERIAL_TOOL_RENAME = 286;            // 序列帧智能工具-重命名
    const DSP_MATERIAL_TOOL_CUTTING_ALIGN = 287;     // 序列帧智能工具-素材裁切/对齐
    const DSP_MATERIAL_TOOL_IMAGE_CONVERT_GIF = 288; // 序列帧智能工具-图片转GIF
    const DSP_TOUTIAO_STAR_COMPOSE_LIST = 290;   // 星图达人管理
    const DSP_TOUTIAO_STAR_TASK_LIST = 291;      // 星图需求任务
    const DSP_MATERIAL_TOOL_TEXT_TO_AUDIO = 296; // 智能工具-文本转语音
    const DSP_AD_LIVE_COST_REPORT = 301; // 直播成本
    const DSP_AD_LIVE_SETTLE_REPORT = 308; // 游点代运营结算


    /************************ FA **********************/
    const FA_MEMBER = 118;                      // 成员管理
    const FA_STAFF = 120;                       // 员工档案库
    const FA_SBU_STAFF = 121;                   // 组织架构
    const FA_BASIC_SALARY = 125;                // 基础数据-工资
    const FA_BASIC_COST = 126;                  // 基础数据-费用
    const FA_SHARE_SALARY = 128;                // 分摊数据-工资
    const FA_SHARE_COST = 129;                  // 分摊数据-费用
    const FA_PROFIT = 131;                      // 利润报表
    const FA_DATA_CONFIG = 134;                 // 数据配置
    const FA_RESUME = 135;                      // 简历库
    const FA_EXAM_QUESTION = 136;               // 试题管理
    const FA_EXAM_RECORD = 137;                 // 答题记录
    const FA_USER_COUNT = 138;                  // 组织架构人数
    const FA_ATTENDANCE_RULE = 181;             // 排班制个人规则
    const FA_ATTENDANCE_CONFIG = 148;           // 部门规则
    const FA_ATTENDANCE_RECORD_LIST = 150;      //考勤记录
    const FA_ATTENDANCE_RECORD_DETAIL = 153;    //考勤日志
    const FA_ATTENDANCE_RECORD_PERSONAL = 154;  //个人考勤日志
    const FA_ATTENDANCE_RECORD_PERSONAL_HISTORY = 168; //个人历史考勤
    const FA_ATTENDANCE_TASK = 179;             //考勤任务
    const FA_ATTENDANCE_MANAGER = 182;          //考勤负责人
    const FA_WORKFLOW_PROJECT = 212;            //项目工时-项目确认
    const FA_WORKFLOW_APPROVAL_PENDING = 213;   //项目工时-待审批流程
    const FA_WORKFLOW_APPROVAL_FININSHED = 214; //项目工时-已结束流程
    const FA_WORKFLOW_APPROVAL_REJECTED = 215;  //项目工时-已驳回流程
    const FA_WORKFLOW_WORK_HOUR = 216;          //项目工时-工时确认
    const FA_WORKFLOW_PROJECT_CONFIG = 218;     //项目工时项目配置
    const FA_WORKFLOW_PERMISSION = 223;         //项目工时-工可视权限
    const FA_ATTENDANCE_RECORD_WARN = 233;      // 考勤提醒
    const FA_WORKFLOW_WARN = 234;               // 工时提醒
    const FA_ATTENDANCE_OT_CONFIG = 244;        // 考勤-加班弹性上下班配置
    const FA_ATTENDANCE_ANNUAL_LEAVE = 245;     // 考勤-年假配置

    /************************ ERP **********************/
    const ERP_FINANCE_STOCK = 228;              // 出入库管理
    const ERP_FINANCE_PRODUCT = 222;            // 产品管理
    const ERP_FINANCE_STOCK_ADDRESS = 224;      // 创库地址管理
    const ERP_FINANCE_CUSTOMER = 225;           // 客户管理
    const ERP_FINANCE_SUPPLIER = 226;           // 供应商管理
    const ERP_FINANCE_COMBINATION = 227;        // 组合管理
    const ERP_FINANCE_CONSUMABLES = 232;        // 耗材录入

    // admin route
    const DATAHUB_ERROR_LOG = 10000;             // 错误日志
    const ADMIN_LOG = 10001;                     // 日志管理
    const ADMIN_ES_LOG = 10002;                     // es日志管理
    const LEADER_GROUP = 10004;                  // 负责人分组
    const MEDIA_TYPE = 10005;                    // 媒体类型

    // 其他特殊非菜单路由
    const ALERT_PAY_DATA = 11000;                 // 首日回本预警弹窗

    const ROUTE_NAME_MAP = [
        // dms
        '/dms/home/<USER>'                            => self::HOME_MAP,
        '/dms/home/<USER>'                      => self::HOME_DASHBOARD,
        '/dms/market/overview'                     => self::MARKET_OVERVIEW,
        '/dms/market/retain'                       => self::MARKET_RETAIN,
        '/dms/market/action-retain'                => self::MARKET_ACTION_RETAIN,
        '/dms/market/payment'                      => self::MARKET_PAYMENT,
        '/dms/operation/overview'                  => self::OPERATION_OVERVIEW,
        '/dms/operation/retain'                    => self::OPERATION_RETAIN,
        '/dms/operation/payment'                   => self::OPERATION_PAYMENT,
        '/dms/permission/member'                   => self::PERMISSION_MEMBER,
        '/dms/dmp/device'                          => self::DMP_DEVICE,
        '/dms/individuation/dashboard'             => self::INDIVIDUATION_DASHBOARD,
        '/dms/individuation/kpi'                   => self::INDIVIDUATION_KPI,
        '/dms/individuation/feedback'              => self::INDIVIDUATION_FEEDBACK,
        '/dms/individuation/notice-message'        => self::ADMIN_PUSH_MANAGER,
        '/dms/dmp/download-history'                => self::DMP_DOWNLOAD_HISTORY,
        '/dms/dmp/audience'                        => self::DMP_AUDIENCE,
        '/dms/market/hour-overview'                => self::MARKET_HOUR_OVERVIEW,
        '/dms/dmp/media-account'                   => self::DMP_MEDIA_ACCOUNT,
        '/dms/dmp/file-list'                       => self::DMP_FILE_LIST,
        '/dms/twdblog/data-query'                  => self::TWDBLOG_DATA_QUERY,
        '/dms/market/simple-hour-overview'         => self::MARKET_SIMPLER_HOUR_OVERVIEW,
        '/dms/operation/simple-hour-overview'      => self::OPERATION_SIMPLER_HOUR_OVERVIEW,
        '/dms/individuation/game-warning'          => self::INDIVIDUATION_GAME_WARNING,
        '/dms/operation/recharge-rank'             => self::RECHARGE_RANK,
        '/dms/twdblog/data-config'                 => self::DATA_CONFIG,
        '/dms/twdblog/early-warning-standard'      => self::EARLY_WARNING_STANDARD,
        '/dms/operation/wastage'                   => self::WASTAGE,
        '/dms/twdblog/operation-profit'            => self::OPERATION_PROFIT,
        '/dms/operation/front-end-conversion'      => self::FRONT_END_CONVERSION,
        '/dms/operation/game-online'               => self::GAME_ONLINE,
        '/dms/market/pay-estimate'                 => self::PAY_ESTIMATE,
        '/dms/outer/member'                        => self::OUTER_MEMBER,
        '/dms/outer/site-config'                   => self::OUTER_SITE_CONFIG,
        '/dms/outer/unsettlement'                  => self::OUTER_UN_SETTLEMENT,
        '/dms/outer/settled'                       => self::OUTER_SETTLED,
        '/dms/operation/account-bind'              => self::ACCOUNT_BIND,
        '/dms/twdblog/cost-input'                  => self::COST_INPUT,
        '/dms/operation/server-ecology'            => self::SERVER_ECOLOGY,
        '/dms/sdk-step/layered-payment'            => self::SERVER_ECOLOGY_LAYERED_PAYMENT,
        '/dms/outer/overview'                      => self::OUTER_OVERVIEW,
        '/dms/operation/server-data-overview'      => self::SERVER_DATA_OVERVIEW,
        '/dms/operation/order-conversion'          => self::ORDER_CONVERSION,
        '/dms/market/flow-estimate'                => self::FLOW_ESTIMATE,
        '/dms/twdblog/audit-overview'              => self::AUDIT_OVERVIEW,
        '/dms/fm/finance-account'                  => self::FINANCE_ACCOUNT,
        '/dms/fm/rebate-input'                     => self::REBATE_INPUT,
        '/dms/fm/tax-write'                        => self::TAX_WRITE,
        '/dms/twdblog/agent-rebate '               => self::AGENT_REBATE,
        '/dms/market/recharge-rank'                => self::MARKET_RECHARGE_RANK,
        '/dms/market/abnormal-click'               => self::ABNORMAL_CLICK,
        '/dms/operation/website-recharge'          => self::OPERATION_WEBSITE_RECHARGE,
        '/dms/market/team-config'                  => self::TEAM_CONFIG_DATA,
        '/dms/market/payment-month'                => self::PAYMENT_MONTH,
        '/dms/operation/product-score'             => self::PRODUCT_SCORE,
        '/dms/twdblog/user-group'                  => self::USER_GROUP,
        '/dms/market/pay-estimate-month'           => self::PAY_ESTIMATE_MONTH,
        '/dms/operation/hour-overview'             => self::OPERATION_HOUR_OVERVIEW,
        '/dms/operation/internal-source-manage'    => self::INTERNAL_USER,
        '/dms/twdblog/share-cost'                  => self::COST_SHARE,
        '/dms/twdblog/agent-rebate'                => self::AGENT_REBATE,
        '/dms/twdblog/ad-inspire-cost'             => self::AD_INSPIRE_COST,
        '/dms/operation/action-retain'             => self::OPERATION_ACTION_RETAIN,
        '/dms/twdblog/profit-statement'             => self::PROFIT_STATEMENT,

        // ly
        '/ly/market/retain'                        => self::LY_MARKET_RETAIN,
        '/ly/market/payment'                       => self::LY_MARKET_PAYMENT,
        '/ly/market/overview'                      => self::LY_MARKET_OVERVIEW,
        '/ly/market/simple-hour-overview'          => self::LY_HOUR_OVERVIEW,
        '/ly/market/recharge-rank'                 => self::LY_RECHARGE_RANK,
        '/ly//permission/member'                   => self::LY_PERMISSION_MEMBER,
        '/ly/datahub/cost-input'                   => self::LY_COST_INPUT,
        '/ly/market/wastage'                       => self::LY_WASTAGE,
        '/ly/market/game-online'                   => self::LY_GAME_ONLINE,
        '/ly/datahub/data-config'                  => self::LY_DATA_CONFIG,
        '/ly/market/front-end-conversion'          => self::LY_FRONT_END_CONVERSION,
        '/ly/market/account-bind'                  => self::LY_ACCOUNT_BIND,
        '/ly/datahub/channel-profit'               => self::LY_CHANNEL_PROFIT,
        '/ly/datahub/enter-cost'                   => self::LY_ENTER_COST,
        '/ly/datahub/data-query'                   => self::LY_DATA_QUERY,

        // dsp
        '/dsp/adanalysis/report'                   => self::DSP_AD_ANALYSIS_REPORT,
        '/dsp/adanalysis/live-report'              => self::DSP_AD_LIVE_ANALYSIS_REPORT,
        '/dsp/adanalysis/live-audio-report'        => self::DSP_AD_LIVE_AUDIO_ANALYSIS_REPORT,
        '/dsp/adanalysis/live-audio-report-detail' => self::DSP_AD_LIVE_AUDIO_ANALYSIS_REPORT,
        '/dsp/adanalysis/enterprise-video-report'  => self::DSP_ENTERPRISE_VIDEO_ANALYSIS_REPORT,
        '/dsp/adanalysis/live-cost-report'         => self::DSP_AD_LIVE_COST_REPORT,
        '/dsp/adanalysis/live-settle-report'       => self::DSP_AD_LIVE_SETTLE_REPORT,
        '/dsp/ad-create/targeting'                 => self::DSP_AD_TARGETING,
        '/dsp/ad-create/word'                      => self::DSP_AD_WORD,
        '/dsp/ad-create/tag'                       => self::DSP_AD_TAG,
        '/dsp/ad-create/material'                  => self::DSP_AD_MATERIAL,
        '/dsp/ad-create/account'                   => self::DSP_AD_ACCOUNT,
        '/dsp/ad-create/setting'                   => self::DSP_AD_SETTING,
        '/dsp/ad-create/compose'                   => self::DSP_AD_COMPOSE,
        '/dsp/ad-create/add-compose'               => self::DSP_AD_COMPOSE,
        '/dsp/ad-create/task'                      => self::DSP_AD_TASK,
        '/dsp/ad-create/intelligent-compose'       => self::DSP_AD_INTELLIGENT_COMPOSE,
        '/dsp/ad-create/dimension-monitor-robot'   => self::DSP_AD_DIMENSION_MONITOR_ROBOT,
        '/dsp/material/effect'                     => self::DSP_MATERIAL_EFFECT,
        '/dsp/material/tencent-effect'             => self::DSP_TENCENT_MATERIAL_EFFECT,
        '/dsp/material/author-effect'              => self::DSP_MATERIAL_AUTHOR_EFFECT,
        '/dsp/material/share-rule'                 => self::DSP_MATERIAL_SHARE_RULE,
        '/dsp/advertising/media-account'           => self::DSP_MEDIA_ACCOUNT,
        '/dsp/advertising/agent'                   => self::DSP_ADVERTISING_AGENT,
        '/dsp/advertising/ldy'                     => self::DSP_ADVERTISING_LDY,
        '/dsp/advertising/site'                    => self::DSP_ADVERTISING_SITE,
        '/dsp/advertising/sdk'                     => self::DSP_ADVERTISING_SDK,
        '/dsp/advertising/game-relation'           => self::DSP_ADVERTISING_GAME_RELATION,
        '/dsp/advertising/live-user'               => self::DSP_LIVE_USER,
        '/dsp/material/store'                      => self::DSP_MATERIAL_STORE,
        '/dsp/material/file'                       => self::DSP_MATERIAL_FILE,
        '/dsp/finance/fund'                        => self::DSP_FINANCE_FUND,
        '/dsp/finance/report'                      => self::DSP_FINANCE_REPORT,
        '/dsp/finance/screenshot-mission'          => self::DSP_FINANCE_SCREENSHOT_MISSION,
        '/dsp/permission/member'                   => self::DSP_PERMISSION_MEMBER,
        '/dsp/comment/shielded-word'               => self::DSP_SHIELDED_WORD,
        '/dsp/comment/shielded-user'               => self::DSP_SHIELDED_USER,
        '/dsp/finance/transaction-check'           => self::DSP_FINANCE_TRANSACTION_CHECK,
        '/dsp/finance/return-goods-record'         => self::DSP_FINANCE_RETURN_GOODS_RECORD,
        '/dsp/finance/mail'                        => self::DSP_FINANCE_MAIL,
        '/dsp/finance/account_center'              => self::DSP_FINANCE_ACCOUNT_CENTER,
        '/dsp/ad-asset/toutiao-star-add-compose'   => self::DSP_TOUTIAO_STAR_COMPOSE_LIST,
        '/dsp/ad-asset/toutiao-star-compose-list'  => self::DSP_TOUTIAO_STAR_COMPOSE_LIST,
        '/dsp/ad-asset/toutiao-star-task-list'     => self::DSP_TOUTIAO_STAR_TASK_LIST,
        '/dsp/material-tool/auto-classification'   => self::DSP_MATERIAL_TOOL_AUTO_CLASSIFY,
        '/dsp/material-tool/expand'                => self::DSP_MATERIAL_TOOL_EXPAND,
        '/dsp/material-tool/rename'                => self::DSP_MATERIAL_TOOL_RENAME,
        '/dsp/material-tool/cutting-align'         => self::DSP_MATERIAL_TOOL_CUTTING_ALIGN,
        '/dsp/material-tool/image-convert-gif'     => self::DSP_MATERIAL_TOOL_IMAGE_CONVERT_GIF,
        '/dsp/material-tool/text-to-audio'         => self::DSP_MATERIAL_TOOL_TEXT_TO_AUDIO,

        // fa
        '/fa/permission/member'                    => self::FA_MEMBER,
        '/fa/manage/staff'                         => self::FA_STAFF,
        '/fa/manage/sbu-staff'                     => self::FA_SBU_STAFF,
        '/fa/data/data-config'                     => self::FA_DATA_CONFIG,
        '/fa/manage/resume'                        => self::FA_RESUME,
        '/fa/manage/exam-question'                 => self::FA_EXAM_QUESTION,
        '/fa/manage/exam-record'                   => self::FA_EXAM_RECORD,
        '/fa/basic/salary'                         => self::FA_BASIC_SALARY,
        '/fa/basic/cost'                           => self::FA_BASIC_COST,
        '/fa/share/salary'                         => self::FA_SHARE_SALARY,
        '/fa/share/cost'                           => self::FA_SHARE_COST,
        '/fa/visualization/profit'                 => self::FA_PROFIT,
        '/fa/manage/user-count'                    => self::FA_USER_COUNT,
        '/fa/attendance-rule/list'                 => self::FA_ATTENDANCE_RULE,
        '/fa/attendance-config/list'               => self::FA_ATTENDANCE_CONFIG,
        '/fa/attendance-record/list'               => self::FA_ATTENDANCE_RECORD_LIST,
        '/fa/attendance-record/detail'             => self::FA_ATTENDANCE_RECORD_DETAIL,
        '/fa/attendance-record/personal'           => self::FA_ATTENDANCE_RECORD_PERSONAL,
        '/fa/attendance-record/personal-history'   => self::FA_ATTENDANCE_RECORD_PERSONAL_HISTORY,
        '/fa/attendance-task/run'                  => self::FA_ATTENDANCE_TASK,
        '/fa/attendance-config/manager'            => self::FA_ATTENDANCE_MANAGER,
        '/fa/workflow/project'                     => self::FA_WORKFLOW_PROJECT,
        '/fa/workflow/approval-pending'            => self::FA_WORKFLOW_APPROVAL_PENDING,
        '/fa/workflow/approval-finished'           => self::FA_WORKFLOW_APPROVAL_FININSHED,
        '/fa/workflow/approval-rejected'           => self::FA_WORKFLOW_APPROVAL_REJECTED,
        '/fa/workflow/work-hour'                   => self::FA_WORKFLOW_WORK_HOUR,
        '/fa/workflow/project-config'              => self::FA_WORKFLOW_PROJECT_CONFIG,
        '/fa/workflow/permission'                  => self::FA_WORKFLOW_PERMISSION,
        '/fa/attendance-record/warn'               => self::FA_ATTENDANCE_RECORD_WARN,
        '/fa/workflow/warn'                        => self::FA_WORKFLOW_WARN,
        '/fa/attendance-config/ot-config'          => self::FA_ATTENDANCE_OT_CONFIG,
        '/fa/attendance-config/annual-leave'       => self::FA_ATTENDANCE_ANNUAL_LEAVE,

        // erp
        '/erp/finance/stock'                       => self::ERP_FINANCE_STOCK,
        '/erp/finance/product'                     => self::ERP_FINANCE_PRODUCT,
        '/erp/finance/stockAddress'                => self::ERP_FINANCE_STOCK_ADDRESS,
        '/erp/finance/customer'                    => self::ERP_FINANCE_CUSTOMER,
        '/erp/finance/supplier'                    => self::ERP_FINANCE_SUPPLIER,
        '/erp/finance/combination'                 => self::ERP_FINANCE_COMBINATION,
        '/erp/finance/consumables'                 => self::ERP_FINANCE_CONSUMABLES,

        // other
        '/admin/log'                               => self::ADMIN_LOG,
        '/admin/es-log'                            => self::ADMIN_ES_LOG,
        '/admin/datahub-error-log'                 => self::DATAHUB_ERROR_LOG,
        '/admin/leader-group'                      => self::LEADER_GROUP,
        '/admin/media-type'                        => self::MEDIA_TYPE,
        '/notice/alert-pay-data'                   => self::ALERT_PAY_DATA,
    ];

}
