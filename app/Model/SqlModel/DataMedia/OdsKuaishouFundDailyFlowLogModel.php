<?php

namespace App\Model\SqlModel\DataMedia;



use App\Constant\MediaType;
use App\Param\FinanceListParam;
use Illuminate\Database\Query\JoinClause;

class OdsKuaishouFundDailyFlowLogModel extends AbstractDataMediaSqlModel
{
    protected $table = 'ods_kuaishou_fund_daily_flow_log';

    /**
     * @param FinanceListParam $param
     * @return array
     */
    public function getList(FinanceListParam $param)
    {
        $builder = $this->builder;
        $data_log_builder = $this->builder;
        $day_log_builder = $this->builder;

        $data_log_builder->from('ods_kuaishou_creative_hour_data_log as data_log');
        $data_log_builder->selectRaw('platform, account_id, sum(ori_cost) ad_cost');
        if ($param->aggregation_time === '分月') {
            $data_log_builder->selectRaw("DATE_FORMAT(cost_date,'%Y%m') as date");
        } else {
            $data_log_builder->selectRaw("DATE_FORMAT(cost_date,'%Y-%m-%d') as date");
        }
        if ($param->platform) {
            $data_log_builder->whereIn('platform', $param->platform);
        }
        if ($param->account_id) {
            $data_log_builder->whereIn('account_id', $param->account_id);
        }
        if ($param->date) {
            $cost_date[0] = $param->date[0] . ' 00:00:00';
            $cost_date[1] = $param->date[1] . ' 23:59:59';
            $data_log_builder->whereBetween('cost_date', $cost_date);
        }
        $data_log_builder->where('ori_cost', '>', 0);
        $data_log_builder->groupBy(['account_id', 'date']);

        if ($param->platform) {
            $day_log_builder->whereIn('platform', $param->platform);
        }
        if ($param->account_id) {
            $day_log_builder->whereIn('account_id', $param->account_id);
        }
        if ($param->date) {
            $cost_date[0] = $param->date[0] . ' 00:00:00';
            $cost_date[1] = $param->date[1] . ' 23:59:59';
            $day_log_builder->whereBetween('tdate', $cost_date);
        }

//        $day_log_sql = (new OdsBaiduAccountDayDataLogModel)->dayLogBuilderSql($param, MediaType::KUAISHOU);
//
//        $day_log_builder->fromSub($day_log_sql, 't');
//
//        $day_log_builder->where('media_type', MediaType::KUAISHOU);

        $builder->from($this->table . ' as daily_log');

        if ($param->platform) {
            $builder->whereIn('daily_log.platform', $param->platform);
        }

        if ($param->account_id) {
            $builder->whereIn('daily_log.account_id', $param->account_id);
        }


        $builder->Join('ods_kuaishou_account_log as account_log', function (JoinClause $join) {
            $join->on("account_log.platform", '=', 'daily_log.platform');
            $join->on("account_log.account_id", '=', 'daily_log.account_id');
        });

        // 负责人分组
        $builder->leftJoin('tanwan_datahub.v2_dim_agent_leader_group as alg', function (JoinClause $join) {
            $join->on("account_log.account_leader", '=', 'alg.agent_leader');
        });

        if ($param->agent_leader) {
            $builder->whereIn('account_log.account_leader', $param->agent_leader);
        }
        if ($param->company) {
            $builder->whereIn('account_log.company', $param->company);
        }
        if ($param->creator) {
            $builder->whereIn('account_log.creator', $param->creator);
        }

        $builder->leftJoin('ods_media_account_agency_change_log as agency_change', function (JoinClause $join) {
            $join->on("account_log.account_id", '=', 'agency_change.account_id');
            $join->whereRaw("daily_log.date BETWEEN agency_change.start_date and agency_change.end_date");
            $join->on("account_log.platform", '=', 'agency_change.platform');
            $join->where("agency_change.media_type", '=', MediaType::KUAISHOU);
        });
        if ($param->agency_name) {
            $builder->whereIn('agency_change.agency_full_name', $param->agency_name);
        }
        if ($param->settle_company) {
            $builder->whereIn('agency_change.settle_company', $param->settle_company);
        }
        if ($param->agency_short_name) {
            $builder->whereIn('agency_change.agency_short_name', $param->agency_short_name);
        }

        if ($param->date) {
            $builder->whereBetween('daily_log.date', $param->date);
        }

        $this->injectAgentLeaderPermission($builder, $param->agent_permission, 'creator', 'account_log');

        /* day_log */

        if ($param->aggregation_time === '分月') {

            $sub_balance_builder = $this->builder;
            $balance_builder = $this->builder;
            $sub_balance_builder->from('ods_kuaishou_fund_daily_flow_log');
            $sub_balance_builder->selectRaw("platform, account_id, date, first_value ( balance ) over ( PARTITION BY account_id, YEAR_MONTH ( date ) ORDER BY date desc ) AS balance");
            $sub_balance_builder->whereBetween('date', $param->date);
            $balance_builder->fromSub($sub_balance_builder, '');
            $balance_builder->selectRaw('platform,account_id,balance,YEAR_MONTH ( date ) AS m_date');
            $balance_builder->groupBy(['platform', 'account_id', 'm_date']);
            $balance_builder->orderByDesc('account_id')
                ->orderBy('m_date');

            $main_builder = $this->builder;

            $main_builder = (new OdsBaiduAccountDayDataLogModel())->dayLogBuilderNew($main_builder, $param, MediaType::KUAISHOU);

            $builder->selectRaw('
                daily_log.platform,
                daily_log.account_id,
                YEAR_MONTH(daily_log.date) as m_date,
                account_log.user_name as account_name,
                IFNULL(account_log.creator, "") as creator, IFNULL(account_log.company, "") as company, IFNULL(agency_change.agency_full_name, "") as agency_name, 
                IFNULL(agency_change.settle_company, "") as settle_company, IFNULL(agency_change.agency_short_name, "") as agency_short_name,
                IFNULL(agency_change.project_team, "") as project_team,
                IFNULL(account_log.account_leader, "") as agent_leader,
                alg.agent_leader_group_name
            ');
            $builder->selectRaw('SUM(daily_log.daily_charge) as daily_charge, SUM(daily_log.real_charged) as real_charged, 
                SUM(daily_log.contract_rebate_real_charged) as contract_rebate_real_charged, SUM(daily_log.direct_rebate_real_charged) as direct_rebate_real_charged, 
                SUM(daily_log.daily_transfer_in) as daily_transfer_in, SUM(daily_log.daily_transfer_out) as daily_transfer_out, 
                SUM(daily_log.real_recharged) as real_recharged, SUM(daily_log.contract_rebate_real_recharged) as contract_rebate_real_recharged,
                SUM(daily_log.direct_rebate_real_recharged) as direct_rebate_real_recharged, SUM( daily_log.order_total_charged ) AS order_total_charged,
                SUM( daily_log.order_real_charged ) AS order_real_charged, SUM( daily_log.order_contract_charged ) AS order_contract_charged,
                SUM( daily_log.order_direct_charged ) AS order_direct_charged, MAX( daily_log.insert_time ) as insert_time');
            $builder->selectRaw("
                SUM( share_credit_charge ) AS share_credit_charge,
                SUM( share_real_charge ) AS share_real_charge,
                SUM( daily_share_charge ) AS daily_share_charge
            ");
            $builder->groupBy(['daily_log.platform', 'daily_log.account_id', 'm_date']);
            $main_builder->withExpression('daily_log', $builder);

            $main_builder->from('daily_log');
            $main_builder->selectRaw('
                daily_log.*,
                IFNULL(balance_log.balance, 0) as balance, 
                IFNULL(data_log.ad_cost, 0) as ad_cost, 
                IFNULL(ABS(daily_log.daily_charge - data_log.ad_cost), 0) as diff,
                IFNULL(day_log.ad_cost, 0) AS day_cost, 
                IFNULL(ABS( daily_log.daily_charge - day_log.ad_cost ), 0) AS day_cost_diff,
                IFNULL( day_log.contract_game_name, "" ) AS contract_game_name,
                IFNULL( day_log.clique_game, "" ) AS clique_game,
                IFNULL( day_log.root_game, "" ) AS root_game,
                IFNULL( day_log.main_game, "" ) AS main_game,
                IFNULL( day_log.max_payway_company, "" ) AS max_payway_company,
                IFNULL( day_log.other_payway_company, "" ) AS other_payway_company,
                IFNULL( daily_log.agent_leader_group_name, "" ) AS agent_leader_group_name
            ');
            $main_builder->withExpression('data_log', $data_log_builder);
            $main_builder->leftJoin('data_log', function (JoinClause $join)  {
                $join->on('data_log.account_id', '=', 'daily_log.account_id');
                $join->on('data_log.date', '=', "daily_log.m_date");
                $join->on('data_log.platform', '=', "daily_log.platform");
            });
            $main_builder->withExpression('balance_log', $balance_builder);
            $main_builder->leftJoin('balance_log', function (JoinClause $join)  {
                $join->on('balance_log.account_id', '=', 'daily_log.account_id');
                $join->on('balance_log.m_date', '=', "daily_log.m_date");
                $join->on('balance_log.platform', '=', "daily_log.platform");
            });
            $main_builder->leftJoin('day_log', function (JoinClause $join)  {
                $join->on('day_log.account_id', '=', 'daily_log.account_id');
                $join->on('day_log.m_date', '=', "daily_log.m_date");
                $join->on('day_log.platform', '=', "daily_log.platform");
            });

            if ($param->search_time) {
                $main_builder->where('daily_log.insert_time', '<', $param->search_time);
                $all = $main_builder
                    ->orderByDesc('daily_log.account_id')
                    ->orderByDesc('daily_log.m_date')
                    ->orderByDesc('balance')
                    ->get();
            } else {
                $all = $main_builder
                    ->orderByDesc('diff')
                    ->orderByDesc('daily_charge')
                    ->get();
            }

            $count = $all->count();

            return [
                'total' => $count,
                'sql' => $this->getSql($main_builder),
                'list' => $all->forPage($param->page, $param->rows)->values(),
                'all' => $all
            ];
        } elseif ($param->aggregation_time === '分日') {
            $builder = (new OdsBaiduAccountDayDataLogModel())->dayLogBuilderNew($builder, $param, MediaType::KUAISHOU);

            $builder->withExpression('data_log', $data_log_builder);
            $builder->leftJoin('data_log', function (JoinClause $join)  {
                $join->on('data_log.account_id', '=', 'daily_log.account_id');
                $join->on('data_log.date', '=', 'daily_log.date');
            });

            $builder->leftJoin('day_log', function (JoinClause $join)  {
                $join->on('day_log.account_id', '=', 'daily_log.account_id');
                $join->on('day_log.date', '=', "daily_log.date");
            });


            $builder->selectRaw('daily_log.platform, daily_log.account_id, daily_log.date, account_log.user_name as account_name, 
                account_log.creator as creator, account_log.company as company, agency_change.agency_full_name as agency_name, 
                agency_change.settle_company as settle_company, agency_change.agency_short_name, agency_change.project_team as project_team,
                IFNULL(account_log.account_leader, "") as agent_leader
            ');
            $builder->selectRaw('daily_log.balance, SUM(daily_log.daily_charge) as daily_charge, SUM(daily_log.real_charged) as real_charged, 
                SUM(daily_log.contract_rebate_real_charged) as contract_rebate_real_charged, SUM(daily_log.direct_rebate_real_charged) as direct_rebate_real_charged, 
                SUM(daily_log.daily_transfer_in) as daily_transfer_in, SUM(daily_log.daily_transfer_out) as daily_transfer_out, 
                SUM(daily_log.real_recharged) as real_recharged, SUM(daily_log.contract_rebate_real_recharged) as contract_rebate_real_recharged,
                SUM(daily_log.direct_rebate_real_recharged) as direct_rebate_real_recharged, SUM( daily_log.order_total_charged ) AS order_total_charged,
                SUM( daily_log.order_real_charged ) AS order_real_charged, SUM( daily_log.order_contract_charged ) AS order_contract_charged,
                SUM( daily_log.order_direct_charged ) AS order_direct_charged');
            $builder->selectRaw("
                SUM( share_credit_charge ) AS share_credit_charge,
                SUM( share_real_charge ) AS share_real_charge,
                SUM( daily_share_charge ) AS daily_share_charge
            ");
            $builder->selectRaw('
                IFNULL( day_log.contract_game_name, "" ) AS contract_game_name,
                IFNULL( day_log.clique_game, "" ) AS clique_game,
                IFNULL( day_log.root_game, "" ) AS root_game,
                IFNULL( day_log.main_game, "" ) AS main_game,
                IFNULL( day_log.max_payway_company, "" ) AS max_payway_company,
                IFNULL( day_log.other_payway_company, "" ) AS other_payway_company,
                IFNULL( alg.agent_leader_group_name, "" ) AS agent_leader_group_name
            ');
            $builder->selectRaw('data_log.ad_cost as ad_cost, ABS(SUM(daily_log.daily_charge) - data_log.ad_cost) as diff, 
                day_log.ad_cost AS day_cost, ABS( SUM( daily_log.daily_charge ) - day_log.ad_cost ) AS day_cost_diff');
            $builder->groupBy(['daily_log.platform', 'daily_log.account_id', 'daily_log.date']);

            $all = $builder->orderByDesc('diff')
                ->orderByDesc('daily_charge')->get();
            $count = $all->count();

            return [
                'total' => $count,
                'sql' => $this->getSql($builder),
                'list' => $all->forPage($param->page, $param->rows)->values(),
                'all' => $all
            ];
        } else {
            $builder->withExpression('data_log', $data_log_builder);
            $builder->leftJoin('data_log', function (JoinClause $join)  {
                $join->on('data_log.account_id', '=', 'daily_log.account_id');
            });

            $builder->selectRaw('daily_log.*, account_log.user_name as account_name, account_log.creator as creator, 
                account_log.company as company, agency_change.agency_full_name as agency_name, 
                agency_change.settle_company as settle_company, agency_change.agency_short_name, agency_change.project_team as project_team,
                IFNULL(account_log.account_leader, "") as agent_leader,
                IFNULL( alg.agent_leader_group_name, "" ) AS agent_leader_group_name
            ');
            $builder->selectRaw('data_log.ad_cost as ad_cost, ABS(SUM(daily_log.daily_charge) - data_log.ad_cost) as diff');
            $count = $builder->count();

            $builder->orderByDesc('diff')
                ->orderByDesc('daily_charge');

            return [
                'total' => $count,
                'sql' => $this->getSql($builder),
                'list' => $builder->forPage($param->page, $param->rows)
                    ->get(),
            ];
        }
    }

    /**
     * @param $start_date
     * @param $end_date
     * @param $platform
     * @return \Illuminate\Support\Collection
     */
    public function getInAndOut($start_date, $end_date, $platform)
    {
        $builder = $this->builder;
        if (!empty($platform)) {
            $builder->whereIn('platform', $platform);
        }
        return $builder->selectRaw('*,3 as media_type')
            ->whereBetween('date', [$start_date, $end_date])
            ->get();
    }
}
