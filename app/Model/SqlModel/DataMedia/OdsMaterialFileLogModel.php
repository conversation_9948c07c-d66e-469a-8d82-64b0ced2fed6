<?php

namespace App\Model\SqlModel\DataMedia;

use App\Model\HttpModel\UC\Report\FileModel;
use App\Model\SqlModel\Zeda\MaterialFileModel;
use App\Param\Material\MaterialAprovedRateListParam;
use App\Param\Material\MaterialFileListParam;
use Illuminate\Database\Query\Builder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Collection;

class OdsMaterialFileLogModel extends AbstractDataMediaSqlModel
{
    /**
     * 素材文件类型
     */
    const FILE_TYPE_IMAGE = 1;
    const FILE_TYPE_VIDEO = 2;
    const FILE_TYPE_COVER = 3;
    const FILE_TYPE_ICON = 4;

    protected $table = 'ods_material_file_log';

    /**
     * 增加素材文件
     * @param $data
     * @return int
     */
    public function add($data)
    {
        if (!is_array(reset($data))) {
            $data = [$data];
        }
        foreach ($data as &$material_file_info) {
            unset($material_file_info['update_time']);
        }
        return $this->builder->insert($data);
    }

    /**
     * 更新素材文件信息
     * @param $data
     * @param $ids
     * @return bool
     */
    public function update($data, $ids)
    {
        unset($data['update_time']);
        $builder = $this->builder;
        return $builder->whereIn('id', $ids)->update($data);
    }

    public function getListByIdOrMD5($id_list, $md5_list)
    {
        $builder = $this->builder;
        $id_list && $builder->whereIn('id', $id_list);
        $md5_list && $builder->orWhereIn('signature', $md5_list);

        return $builder->get();
    }

    public function getToutiaoGiudeVideoAccountOption($user_list, $material_permission)
    {
        $builder = $this->builder;
        $builder->selectRaw('ods_material_file_log.*');
        $builder->leftJoin('ods_material_log', function (JoinClause $join) {
            $join->on('ods_material_file_log.platform', '=', 'ods_material_log.platform');
            $join->on('ods_material_file_log.material_id', '=', 'ods_material_log.material_id');
        });

        $this->setOdsMaterialLogAlias('ods_material_log');
        $this->injectMaterialPermission($builder, $user_list, $material_permission);

        $builder->where(function ($query) {
            /* @var  Builder $query */
            $query->orWhere(function ($sub_query) {
                //TW-小兵
                $sub_query->whereBetween('ods_material_log.theme_id', [264000, 264999]);
            });
            $query->orWhere(function ($sub_query) {
                //GRBB
                $sub_query->whereIn('ods_material_log.theme_id', [45010, 71009, 45012]);
            });
        });
        $builder->where('ods_material_file_log.file_type', 2);

        $builder->limit(50);

        return $builder->get();
    }

    /**
     * 根据素材id更新多个素材文件信息
     * @param $data
     * @param $material_id
     * @param $platform
     * @return bool
     */
    public function updateByMaterialId($data, $material_id, $platform)
    {
        $builder = $this->builder;
        return $builder->where(['material_id' => $material_id, 'platform' => $platform])->update($data);
    }

    public function getDataByMd5AndUploader($md5, $uploader, $platform = '')
    {
        $builder = $this->builder;
        $platform && $builder->where(['ods_material_file_log.platform' => $platform]);
        $builder->where(['ods_material_file_log.signature' => $md5]);
        $builder->where(['ods_material_file_log.uploader' => $uploader]);
        return $builder->first();
    }

    /**
     * 根据素材id获取素材文件列表
     * @param MaterialFileListParam $param
     * @return array
     */
    public function getListByMaterialId(MaterialFileListParam $param)
    {
        $builder = $this->builder;
        $builder->where(['ods_material_file_log.is_del' => 0]);
        $builder->where('ods_material_file_log.file_type', '!=', self::FILE_TYPE_COVER);
        $builder->where(['ods_material_file_log.material_id' => $param->material_id, 'ods_material_file_log.platform' => $param->platform]);

        $builder->select(['ods_material_file_log.*', 'ods_material_file_log.insert_time as create_time', 'ods_material_log.name'])
            ->leftJoin('ods_material_log', function (JoinClause $join) {
                $join->on('ods_material_file_log.platform', '=', 'ods_material_log.platform');
                $join->on('ods_material_file_log.material_id', '=', 'ods_material_log.material_id');
            });

        if ($param->filename) {
            $builder->where('ods_material_file_log.filename', 'like', "%$param->filename%");
        }

        if ($param->insert_time) {
            $builder->whereBetween('ods_material_file_log.insert_time', $param->insert_time);
        }

        if (isset($param->is_ext)) {
            $builder->where('ods_material_file_log.is_ext', $param->is_ext);
        }


        $builder->whereNotIn('ods_material_file_log.file_type', [MaterialFileModel::FILE_TYPE_AEP, MaterialFileModel::FILE_TYPE_TXT]);

        $total = $builder->count();
        if ($total === 0) {
            return [
                'total' => $total,
                'list' => collect()
            ];
        }

        return [
            'total' => $total,
            'list' => $builder->orderBy('ods_material_file_log.id', 'desc')->forPage($param->page, $param->rows)->get()
        ];
    }

    /**
     *
     * @param MaterialAprovedRateListParam $param
     * @return array
     */
    public function getAprovedRateList(MaterialAprovedRateListParam $param)
    {
        $builder = $this->builder;
        $builder->select(['ods_material_file_log.platform', 'ods_material_file_log.material_id', 'ods_material_file_log.id', 'ods_material_file_log.signature',
            'ad3.media_type', 'ad3.account_id', 'account_log.account_name', 'account_log.account_leader', 'ad3.ad2_id', 'ad3.ad3_id',
            'ad3.reject_message'])
            ->Join('dwd_media_ad3_common_log as ad3', function (JoinClause $join) {
                $join->on('ods_material_file_log.signature', '=', 'ad3.signature');
            })
            ->leftJoin('dwd_media_account_common_log as account_log', function (JoinClause $join) {
                $join->on('ad3.platform', '=', 'account_log.platform');
                $join->on('ad3.account_id', '=', 'account_log.account_id');
            });
        $builder->where(['ods_material_file_log.platform' => $param->platform, 'ods_material_file_log.material_id' => $param->material_id]);
        if ($param->media_type) {
            $builder->where(['ad3.media_type' => $param->media_type]);
        }
        $builder->whereRaw("ad3.reject_message NOT IN ('', '[]', '账户余额不足', '投放中', '广告组已暂停', '广告计划已暂停', '已暂停', '广告组投放到期下线', 
            '广告计划超预算', '已删除', '因腾讯广告原生推广页升级，暂不支持此页面投放，请使用新页面创建广告', '广告组审核中')");

        $total = $builder->count();
        if ($total === 0) {
            return [
                'total' => $total,
                'list' => collect()
            ];
        }

        return [
            'total' => $total,
            'list' => $builder->forPage($param->page, $param->rows)->get()
        ];
    }

    /**
     * 获取列表
     * @param MaterialFileListParam $param
     * @param $user_list
     * @param $material_permission
     * @param array $media_width_height
     * @return array
     */
    public function getListByCondition(MaterialFileListParam $param, $user_list, $material_permission, $media_width_height = [])
    {
        $builder = $this->builder;
        $builder->select([
            'ods_material_file_log.*',
            'ods_material_file_log.insert_time as create_time',
            'material.cost as cost',
            'material.name as material_name',
            'material.original as original',
            'material.effect_grade7 as effect_grade7',
            'material.is_effect_grade7 as is_effect_grade7',
            'material.effect_grade30 as effect_grade30',
            'material.is_effect_grade30 as is_effect_grade30'
        ]);
        $this->injectMaterialPermissionAndJoinMaterial($builder, $user_list, $material_permission, $this->table);
        $builder->where('material.is_del', '=', 0);
        $builder->where('ods_material_file_log.is_del', '=', 0);
        $builder->where("ods_material_file_log.signature", '!=', '');
        if ($param->order_by && $param->order_by['order']) {
            $builder->orderBy($param->order_by['col'], $param->order_by['order']);
        } else {
            if ($param->create_time) {
                if (is_numeric($param->create_time[0]) && is_numeric($param->create_time[1])) {
                    $param->create_time[0] = date('Y-m-d H:i:s', $param->create_time[0]);
                    $param->create_time[1] = date('Y-m-d H:i:s', $param->create_time[1]);
                }
                $builder->whereBetween('ods_material_file_log.insert_time', $param->create_time);
            }
        }

        // 根据媒体类型控制素材宽高
        if ($media_width_height) {
            $builder->where(function ($query1) use ($media_width_height) {
                foreach ($media_width_height as $key => [$width, $height]) {
                    /* @var  Builder $query1 */
                    $query1->orWhere(function ($query2) use ($width, $height) {
                        /* @var  Builder $query2 */
                        $query2->where("{$this->table}.width", '=', $width);
                        $query2->where("{$this->table}.height", '=', $height);
                    });
                }
            });
        }

        if ($param->width_height_group) {
            $builder->where(function ($query1) use ($param) {
                foreach ($param->width_height_group as $key => [$width, $height]) {
                    /* @var  Builder $query1 */
                    $query1->orWhere(function ($query2) use ($width, $height) {
                        /* @var  Builder $query2 */
                        $query2->where("{$this->table}.width", '=', $width);
                        $query2->where("{$this->table}.height", '=', $height);
                    });
                }
            });
        }

        if (is_numeric($param->is_ext)) {
            $builder->where('ods_material_file_log.is_ext', $param->is_ext);
        }

        if ($param->no_creative) {
            $builder->whereRaw('material.cost = 0');
        }

        if ($param->effect_grade7) {
            $builder->where('material.effect_grade7', '=', $param->effect_grade7);
        }

        if ($param->effect_grade30) {
            $builder->where('material.effect_grade30', '=', $param->effect_grade30);
        }

        if ($param->max_size) {
            $builder->where('ods_material_file_log.size', '<=', $param->max_size * 1024);
        }

        if ($param->min_duration) {
            $builder->where('ods_material_file_log.duration', '>=', $param->min_duration);
        }

        if ($param->theme_ids) {
            $builder->whereIn('material.theme_id', $param->theme_ids);
        }

        if ($param->id) {
            $builder->where(['ods_material_file_log.id' => $param->id]);
        }

        if ($param->ids) {
            $builder->whereIn('ods_material_file_log.id', $param->ids);
        }

        if ($param->material_id) {
            $builder->where('ods_material_file_log.material_id', "=", $param->material_id);
        }

        if ($param->material_ids) {
            $builder->whereIn('ods_material_file_log.material_id', $param->material_ids);
        }

        if ($param->file_type) {
            $builder->whereIn('ods_material_file_log.file_type', [1, 2, 3, 4, 5, 6, 7, 9, 10, 11]);
            $builder->where(['ods_material_file_log.file_type' => $param->file_type]);
        } else {
            $builder->whereIn('ods_material_file_log.file_type', [1, 2]);
        }

        if ($param->platform) {
            $builder->where(['ods_material_file_log.platform' => $param->platform]);
        }

        if ($param->name) {
            $builder->where('material.name', 'LIKE', "%{$param->name}%");
        }

        if ($param->filename) {
            $builder->where('ods_material_file_log.filename', 'LIKE', "%{$param->filename}%");
        }

        if ($param->height) {
            $builder->where(['ods_material_file_log.height' => $param->height]);
        }

        if ($param->width) {
            $builder->where(['ods_material_file_log.width' => $param->width]);
        }

        if ($param->scale) {
            $builder->where(['ods_material_file_log.scale' => $param->scale]);
        }

        if ($param->is_vertical) {
            $builder->where('ods_material_file_log.scale', '<', 1);
        }

        if ($param->is_horizontal) {
            $builder->where('ods_material_file_log.scale', '>', 1);
        }

        if ($param->uploader) {
            $builder->where('ods_material_file_log.uploader', '=', $param->uploader);
        }

        if ($param->original) {
            $builder->where('material.original', '=', $param->original);
        }

        $total = $builder->count();
        if ($total === 0) {
            return [
                'total' => $total,
                'list' => collect()
            ];
        } else {
            return [
                'total' => $total,
                'list' => $builder->orderBy('ods_material_file_log.id', 'desc')->forPage($param->page, $param->rows)->get()
            ];
        }
    }

    /**
     * 按字段搜列表
     * @param $keyword
     * @param $options
     * @param $user_list
     * @param $material_permission
     * @return Collection|array
     */
    public function getListLikeFile($keyword, $options, $user_list, $material_permission)
    {
        $builder = $this->builder->select("{$this->table}.platform", "{$this->table}.id", "{$this->table}.filename");
        $this->injectMaterialPermissionAndJoinMaterial($builder, $user_list, $material_permission, $this->table);
        if ($keyword) {
            $builder->where(function (Builder $query) use ($keyword) {
                $file_ids = explode(',', $keyword);
                if (is_array($file_ids) && count($file_ids) > 1) {
                    $query->whereIn("{$this->table}.id", $file_ids);
                } else {
                    $query->orWhere("{$this->table}.filename", 'like', "%{$keyword}%");
                    $query->orWhere("{$this->table}.id", '=', "{$keyword}");
                }
            });
        }
        if (!empty($options)) {
            foreach ($options as [$condition, $value]) {
                $builder->WhereRaw($condition, $value);
            }
        }
        $builder
            ->where("{$this->table}.file_type", '<', self::FILE_TYPE_COVER)
            ->orderByDesc("{$this->table}.insert_time")
            ->limit(50);
        return $builder->get();
    }

    /**
     * 根据素材md5获取素材文件列表
     * @param array $signature
     * @return Collection
     */
    public function getListBySignature(array $signature)
    {
        $builder = $this->builder;
        $builder->select(['ods_material_file_log.*', 'ods_material_file_log.insert_time as create_time']);
        $builder->where(['ods_material_file_log.is_del' => 0]);
        $builder->whereIn('ods_material_file_log.signature', $signature);

        return $builder->get();
    }

    /**
     * 根据素材md5获取素材文件列表
     * @param array $url_list
     * @return Collection
     */
    public function getListByUrl(array $url_list)
    {
        $builder = $this->builder;
        $builder->select(['ods_material_file_log.*', 'ods_material_file_log.insert_time as create_time']);
        $builder->where(['ods_material_file_log.is_del' => 0]);
        $builder->whereIn('ods_material_file_log.url', $url_list);

        return $builder->get();
    }

    /**
     * 根据素材md5获取素材文件列表
     * @param array $id
     * @return Collection
     */
    public function getListByID(array $id)
    {
        $builder = $this->builder;
        $builder->select(['ods_material_file_log.*', 'ods_material_file_log.insert_time as create_time']);
        $builder->where(['ods_material_file_log.is_del' => 0]);
        $builder->whereIn('ods_material_file_log.id', $id);

        return $builder->get();
    }

    public function getDataForAwemeVideoByItemId($item_id)
    {
        return $this->builder
            ->where(['file_type' => MaterialFileModel::FILE_TYPE_AWEME_VIDEO])
            ->where(['video_hash_0' => $item_id])
            ->first();
    }

    /**
     * 根据素材ID获取视频解构结果
     * @param array $ids
     * @return Collection
     */
    public function getDeconstructInfoByIds(array $ids)
    {
        $builder = $this->builder;
        $builder->from("{$this->table} as file");
        $builder->join("tanwan_datamedia_test.ods_signature_gpt_result_log as gpt", "file.signature", "=", "gpt.signature");
        $builder->join("tanwan_datamedia.ods_material_volcengine_speech_recognize_log as speech", "file.signature", "=", "speech.signature");
        $builder->selectRaw("
            file.id,
            file.material_id,
            file.filename,
            file.url,
            file.signature,
            gpt.speech_texts as video_speech_text,
            gpt.gpt_result as video_deconstruct,
            speech.speech_speaker_split_text as video_speech_split_text
        ");
        $builder->whereIn("file.id", $ids);

        return $builder->get();
    }

    /**
     * 根据md5返回列表，有注入权限的
     * @param $signatures
     * @param $user_list
     * @param $material_permission
     * @return Collection
     */
    public function getListBySignatureWithPermission($signatures, $user_list, $material_permission)
    {
        $material_file_builder = $this->builder;
        $material_file_builder->from("{$this->table} as material_file");
        $material_file_builder->selectRaw('
            material.name,
            material.platform,
            material.author,
            material.c_author,
            material.a_author,
            material.e_author,
            material.system_author,
            material.m1_author,
            material.m2_author,
            material.m3_author,
            material.m4_author,
            material.m5_author,
            material.actor,
            material.shoot,
            material_file.material_id,
            material_file.signature,
            material_file.id AS material_file_id,
            material_file.filename,
            material_file.url,
            material_file.width,
            material_file.height,
            ROW_NUMBER() over ( PARTITION BY material_file.signature ORDER BY material_file.id, material.is_del ASC ) AS row_rank
        ');

        $this->injectMaterialPermissionAndJoinMaterial($material_file_builder, $user_list, $material_permission, "material_file");

        $builder = $this->builder->from('material_file');
        $builder->withExpression("material_file", $material_file_builder);
        $builder->select(['material_file.*']);
        $builder->whereIn('material_file.signature', $signatures);
        $builder->where('material_file.row_rank', 1);

        return $builder->get();
    }

    /**
     * 根据md5返回列表，有注入权限的
     * @param $ids
     * @param $user_list
     * @param $material_permission
     * @return Collection
     */
    public function getListByIdWithPermission($ids, $user_list, $material_permission)
    {
        $material_file_builder = $this->builder;
        $material_file_builder->from("{$this->table} as material_file");
        $material_file_builder->selectRaw('
            material.name,
            material.platform,
            material.author,
            material.c_author,
            material.a_author,
            material.e_author,
            material.system_author,
            material.m1_author,
            material.m2_author,
            material.m3_author,
            material.m4_author,
            material.m5_author,
            material.actor,
            material.shoot,
            material_file.material_id,
            material_file.signature,
            material_file.id,
            material_file.filename,
            material_file.url,
            material_file.width,
            material_file.height          
        ');

        $this->injectMaterialPermissionAndJoinMaterial($material_file_builder, $user_list, $material_permission, "material_file");

        $material_file_builder->whereIn('material_file.id', $ids);

        return $material_file_builder->get();
    }

    /**
     * 根据素材文件ID获取分析后的视频脚本
     * @param array $ids
     * @return Collection
     */
    public function getScriptContentByIds(array $ids)
    {
        $ana_req_sub_builder = $this->builder->from("tanwan_datamedia_test.ods_signature_story_analyse_log");
        $ana_req_sub_builder->selectRaw("
            *, 
            rank () over ( PARTITION BY signature ORDER BY insert_time DESC ) ranks
        ");

        $ana_req_builder = $this->builder->fromSub($ana_req_sub_builder, "ana_rank");
        $ana_req_builder->selectRaw("
            DISTINCT request_id
        ");
        $ana_req_builder->where("ranks", 1);

        $ana_builder = $this->builder->from("ana_req");
        $ana_builder->select(["ana.*"]);
        $ana_builder->join("tanwan_datamedia_test.ods_signature_story_analyse_log as ana", "ana_req.request_id", "=", "ana.request_id");

        $builder = $this->builder;
        $builder
            ->withExpression("ana_req", $ana_req_builder)
            ->withExpression("ana", $ana_builder);

        $builder->from("{$this->table} as file");
        $builder->join("ana", function (JoinClause $join) {
            $join->on("file.signature", "=", "ana.signature");
        });

        $builder->selectRaw("
            file.id as material_file_id,
            ana.*
        ");

        $builder->whereIn("file.id", $ids);
        $builder->orderBy("ana.signature")->orderBy("ana.story_id");

        return $builder->get();
    }

    /**
     * 根据md5返回md5，有注入权限的
     * @param $signature_list
     * @param $user_list
     * @param $material_permission
     * @return Collection
     */
    public function getSigantureListWithPermission($signature_list, $user_list, $material_permission)
    {
        $material_file_builder = $this->builder;
        $material_file_builder->from("{$this->table} as material_file");
        $material_file_builder->selectRaw('
            material_file.signature
        ');

        $this->injectMaterialPermissionAndJoinMaterial($material_file_builder, $user_list, $material_permission, "material_file");

        $material_file_builder->whereIn('material_file.signature', $signature_list);

        return $material_file_builder->get();
    }
}
