<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/04/16
 * Time: 10:24
 */

namespace App\Model\SqlModel\DataMedia;

use App\Constant\ADComposeFilterSqlMap;
use App\Constant\MediaType;
use App\Exception\AppException;
use App\Model\SqlModel\Zeda\ADMaterialPacketModel;
use App\Param\ADServing\ADComposeFilterParam;
use Common\EnvConfig;
use Illuminate\Database\Query\Builder;
use Illuminate\Database\Query\JoinClause;

/**
 * Class ADComposeFilterModel
 * @package App\Model\SqlModel\DataMedia
 */
class ADComposeFilterModel extends AbstractDataMediaSqlModel
{
    /**
     * 账号报表筛选
     * @param ADComposeFilterParam $param
     * @param int $agent_permission
     * @param int $game_permission
     * @return array
     */
    public function getMediaAccountFilter(ADComposeFilterParam $param, $agent_permission = -1, $game_permission = -1)
    {
        $table = ADComposeFilterSqlMap::TABLE;

        $driver_builder = $this->builder->from($table['account_common_log']);
        $ad2_common_log = $this->builder->from($table['ad2_common_log']);
        $hour_data_log = $this->builder->from($table['hour_data_log']);
        $overview_log = $this->builder->from($table['overview_log']);
        $overview_log->where("{$table['overview_log']}.ad2_id", '>', 0);
        $reg_log = $this->builder->from($table['reg_log']);
        $reg_log->where("{$table['reg_log']}.action_id", '=', 2);
        $reg_log->where("{$table['reg_log']}.adgroup_id ", '!=', '');
        $main_builder = $this->builder->from('driver');

        // 组装驱动表
        $this->injectAgentLeaderPermission($driver_builder, $agent_permission, 'account_leader');
        $driver_builder->selectRaw("{$table['account_common_log']}.account_id");
        $driver_builder->selectRaw("{$table['account_common_log']}.account_name");
        $driver_builder->selectRaw("{$table['account_common_log']}.account_leader");
        $driver_builder->selectRaw("{$table['account_common_log']}.platform");

        $driver_builder->where(["{$table['account_common_log']}.platform" => $param->platform]);
        $driver_builder->where(["{$table['account_common_log']}.media_type" => $param->media_type]);
        $driver_builder->where(["{$table['account_common_log']}.company" => $param->company]);

        $driver_builder->groupBy("{$table['account_common_log']}.account_id");

        if ($param->filter_mode === 0) {
            // 物料筛选模式
            foreach ($param->account_common_log_filter as [$condition, $value]) {
                if (strpos($condition, 'agent_leader') !== false) {
                    $driver_builder->whereRaw($condition, $value);
                }
                if (strpos($condition, 'account_id') !== false) {
                    $driver_builder->whereIn("{$table['account_common_log']}.account_id", $value);
                }
            }
        } else {
            // 全数据筛选模式
            if ($param->need_join_ad2_common_log['account_common_log'] ?? false) {
                $driver_builder->leftJoin($table['ad2_common_log'], function (JoinClause $join) use ($param, $table) {
                    $join->on("{$table['account_common_log']}.account_id", '=', "{$table['ad2_common_log']}.account_id");
                    $join->on("{$table['account_common_log']}.media_type", '=', "{$table['ad2_common_log']}.media_type");
                    $join->on("{$table['account_common_log']}.platform", '=', "{$table['ad2_common_log']}.platform");

                    $join->where(["{$table['ad2_common_log']}.platform" => $param->platform]);
                    $join->where(["{$table['ad2_common_log']}.media_type" => $param->media_type]);
                    $param->ad2_create_time && $join->whereBetween("{$table['ad2_common_log']}.ad2_create_time", $param->ad2_create_time);
                });
            }

            if ($param->need_join_game_log['account_common_log'] ?? false) {
                $this->injectAgentPermissionAndJoinAgentSite($driver_builder, $agent_permission, $table['ad2_common_log']);
                $this->injectGamePermissionAndJoinSiteGame($driver_builder, $game_permission, $table['ad2_common_log']);
            }

            if ($param->need_join_ad3_common_log['account_common_log'] ?? false) {
                $driver_builder->leftJoin($table['ad3_common_log'], function (JoinClause $join) use ($param, $table) {
                    $join->on("{$table['account_common_log']}.account_id", '=', "{$table['ad3_common_log']}.account_id");
                    $join->on("{$table['account_common_log']}.media_type", '=', "{$table['ad3_common_log']}.media_type");
                    $join->on("{$table['account_common_log']}.platform", '=', "{$table['ad3_common_log']}.platform");

                    $join->where(["{$table['ad3_common_log']}.platform" => $param->platform]);
                    $join->where(["{$table['ad3_common_log']}.media_type" => $param->media_type]);
                });
            }

            if ($param->need_join_material_file_log['account_common_log'] ?? false) {
                $driver_builder->leftJoin($table['material_file_log'], function (JoinClause $join) use ($param, $table) {
                    $join->on("{$table['material_file_log']}.signature", '=', "{$table['ad3_common_log']}.signature");
                });
            }

            foreach ($param->account_common_log_filter as [$condition, $value]) {
                $driver_builder->whereRaw($condition, $value);
            }
        }

        $main_builder->withExpression('driver', $driver_builder);

        // 组装计数项
        if ($param->ad2_common_log_target) {

            $ad2_common_log->selectRaw("{$table['ad2_common_log']}.account_id");

            foreach ($param->ad2_common_log_target as $target) {
                $ad2_common_log->selectRaw($target);
            }

            $this->injectAgentPermissionAndJoinAgentSite($ad2_common_log, $agent_permission, $table['ad2_common_log']);
            $this->injectGamePermissionAndJoinSiteGame($ad2_common_log, $game_permission, $table['ad2_common_log']);

            if ($param->need_join_ad3_common_log['ad2_common_log'] ?? false) {
                $ad2_common_log->join($table['ad3_common_log'], function (JoinClause $join) use ($param, $table) {
                    $join->on("{$table['ad2_common_log']}.ad2_id", '=', "{$table['ad3_common_log']}.ad2_id");
                    $join->on("{$table['ad2_common_log']}.media_type", '=', "{$table['ad3_common_log']}.media_type");
                    $join->on("{$table['ad2_common_log']}.platform", '=', "{$table['ad3_common_log']}.platform");

                    $join->where(["{$table['ad2_common_log']}.platform" => $param->platform]);
                    $join->where(["{$table['ad2_common_log']}.media_type" => $param->media_type]);
                });
            } else {
                $ad2_common_log->where(["{$table['ad2_common_log']}.platform" => $param->platform]);
                $ad2_common_log->where(["{$table['ad2_common_log']}.media_type" => $param->media_type]);
            }

            if ($param->need_join_material_file_log['ad2_common_log'] ?? false) {
                $ad2_common_log->leftJoin($table['material_file_log'], function (JoinClause $join) use ($param, $table) {
                    $join->on("{$table['material_file_log']}.signature", '=', "{$table['ad3_common_log']}.signature");
                });
            }

            $param->ad2_create_time && $ad2_common_log->whereBetween("{$table['ad2_common_log']}.ad2_create_time", $param->ad2_create_time);

            foreach ($param->ad2_common_log_filter as [$condition, $value]) {
                $ad2_common_log->whereRaw($condition, $value);
            }

            $ad2_common_log->groupBy(["{$table['ad2_common_log']}.account_id"]);

            $main_builder->withExpression('ad2_common_log', $ad2_common_log);

            $main_builder->leftJoin('ad2_common_log', function (JoinClause $join) use ($param) {
                $join->on('driver.account_id', '=', 'ad2_common_log.account_id');
            });
        }

        // 组装小时表
        if (!empty($param->hour_data_log_target)) {

            $param->hour_data_log_target[] = ADComposeFilterSqlMap::FORMULA['hour_data_log']['sum_cost'];
            $param->main_target[] = 'sum_cost';
            $param->hour_data_log_target = array_unique($param->hour_data_log_target);
            $param->main_target = array_unique($param->main_target);

            if ($param->need_join_ad3_common_log['hour_data_log'] ?? false) {
                $hour_data_log->selectRaw("{$table['ad3_common_log']}.account_id");
            } else {
                $hour_data_log->selectRaw("{$table['ad2_common_log']}.account_id");
            }

            foreach ($param->hour_data_log_target as $key => $value) {
                $hour_data_log->selectRaw($value);
            }

            $this->injectAgentPermissionAndJoinAgentSite($hour_data_log, $agent_permission, $table['hour_data_log']);
            $this->injectGamePermissionAndJoinGame($hour_data_log, $game_permission, $table['hour_data_log']);

            if ($param->need_join_ad3_common_log['hour_data_log'] ?? false) {
                $hour_data_log->join($table['ad3_common_log'], function (JoinClause $join) use ($param, $table) {
                    $join->on("{$table['hour_data_log']}.ad4_id", '=', "{$table['ad3_common_log']}.ad4_id");
                    $join->on("{$table['hour_data_log']}.media_type", '=', "{$table['ad3_common_log']}.media_type");
                    $join->on("{$table['hour_data_log']}.platform", '=', "{$table['ad3_common_log']}.platform");

                    $join->where(["{$table['ad3_common_log']}.platform" => $param->platform]);
                    $join->where(["{$table['ad3_common_log']}.media_type" => $param->media_type]);
                });
                if ($param->ad2_create_time || $param->need_join_ad2_common_log['hour_data_log'] ?? false) {
                    $hour_data_log->join($table['ad2_common_log'], function (JoinClause $join) use ($param, $table) {
                        $join->on("{$table['hour_data_log']}.ad2_id", '=', "{$table['ad2_common_log']}.ad2_id");
                        $join->on("{$table['hour_data_log']}.media_type", '=', "{$table['ad2_common_log']}.media_type");
                        $join->on("{$table['hour_data_log']}.platform", '=', "{$table['ad2_common_log']}.platform");

                        $param->ad2_create_time && $join->whereBetween("{$table['ad2_common_log']}.ad2_create_time", $param->ad2_create_time);
                    });
                }
            } else {
                $hour_data_log->join($table['ad2_common_log'], function (JoinClause $join) use ($param, $table) {
                    $join->on("{$table['hour_data_log']}.ad2_id", '=', "{$table['ad2_common_log']}.ad2_id");
                    $join->on("{$table['hour_data_log']}.media_type", '=', "{$table['ad2_common_log']}.media_type");
                    $join->on("{$table['hour_data_log']}.platform", '=', "{$table['ad2_common_log']}.platform");

                    $join->where(["{$table['ad2_common_log']}.platform" => $param->platform]);
                    $join->where(["{$table['ad2_common_log']}.media_type" => $param->media_type]);
                    $param->ad2_create_time && $join->whereBetween("{$table['ad2_common_log']}.ad2_create_time", $param->ad2_create_time);
                });
            }

            if ($param->need_join_material_file_log['hour_data_log'] ?? false) {
                $hour_data_log->leftJoin($table['material_file_log'], function (JoinClause $join) use ($param, $table) {
                    $join->on("{$table['material_file_log']}.signature", '=', "{$table['ad3_common_log']}.signature");
                });
            }

            $hour_data_log->whereBetween("cost_date", $param->cost_date);

            foreach ($param->hour_data_log_filter as [$condition, $value]) {
                $hour_data_log->whereRaw($condition, $value);
            }

            if ($param->need_join_ad3_common_log['hour_data_log'] ?? false) {
                $hour_data_log->groupBy(["{$table['ad3_common_log']}.account_id"]);
            } else {
                $hour_data_log->groupBy(["{$table['ad2_common_log']}.account_id"]);

            }

            $main_builder->withExpression('hour_data_log', $hour_data_log);

            $main_builder->leftJoin('hour_data_log', function (JoinClause $join) use ($param) {
                $join->on('driver.account_id', '=', 'hour_data_log.account_id');
            });

            $main_builder->orderBy('sum_cost', 'desc');
        }

        // 组装业务数据表
        if (!empty($param->overview_log_target)) {

            if ($param->need_join_ad3_common_log['overview_log'] ?? false) {
                $overview_log->selectRaw("{$table['ad3_common_log']}.account_id");
            } else {
                $overview_log->selectRaw("{$table['ad2_common_log']}.account_id");
            }

            foreach ($param->overview_log_target as $key => $value) {
                $overview_log->selectRaw($value);
            }

            $this->injectAgentPermissionAndJoinAgentSite($overview_log, $agent_permission, $table['overview_log']);
            $this->injectGamePermissionAndJoinGame($overview_log, $game_permission, $table['overview_log']);

            if ($param->need_join_ad3_common_log['overview_log'] ?? false) {
                $overview_log->join($table['ad3_common_log'], function (JoinClause $join) use ($param, $table) {
                    $join->on("{$table['overview_log']}.ad4_id", '=', "{$table['ad3_common_log']}.ad4_id");
//                    $join->on("{$table['overview_log']}.media_type", '=', "{$table['ad3_common_log']}.media_type");
                    $join->on("{$this->v2_dim_agent_site_id_alias}.media_type_id", '=', "{$table['ad3_common_log']}.media_type");
                    $join->on("{$table['overview_log']}.platform", '=', "{$table['ad3_common_log']}.platform");

                    $join->where(["{$table['ad3_common_log']}.platform" => $param->platform]);
                    $join->where(["{$table['ad3_common_log']}.media_type" => $param->media_type]);
                });
                if ($param->ad2_create_time || $param->need_join_ad2_common_log['overview_log'] ?? false) {
                    $overview_log->join($table['ad2_common_log'], function (JoinClause $join) use ($param, $table) {
                        $join->on("{$table['overview_log']}.ad2_id", '=', "{$table['ad2_common_log']}.ad2_id");
//                        $join->on("{$table['overview_log']}.media_type", '=', "{$table['ad2_common_log']}.media_type");
                        $join->on("{$this->v2_dim_agent_site_id_alias}.media_type_id", '=', "{$table['ad2_common_log']}.media_type");
                        $join->on("{$table['overview_log']}.platform", '=', "{$table['ad2_common_log']}.platform");

                        $param->ad2_create_time && $join->whereBetween("{$table['ad2_common_log']}.ad2_create_time", $param->ad2_create_time);
                    });
                }
            } else {
                $overview_log->join($table['ad2_common_log'], function (JoinClause $join) use ($param, $table) {
                    $join->on("{$table['overview_log']}.ad2_id", '=', "{$table['ad2_common_log']}.ad2_id");
//                    $join->on("{$table['overview_log']}.media_type", '=', "{$table['ad2_common_log']}.media_type");
                    $join->on("{$this->v2_dim_agent_site_id_alias}.media_type_id", '=', "{$table['ad2_common_log']}.media_type");
                    $join->on("{$table['overview_log']}.platform", '=', "{$table['ad2_common_log']}.platform");

                    $join->where("{$table['ad2_common_log']}.media_type", '=', $param->media_type);
                    $join->where("{$table['ad2_common_log']}.platform", '=', $param->platform);
                    $param->ad2_create_time && $join->whereBetween("{$table['ad2_common_log']}.ad2_create_time", $param->ad2_create_time);
                });
            }

            if ($param->need_join_material_file_log['overview_log'] ?? false) {
                $overview_log->leftJoin($table['material_file_log'], function (JoinClause $join) use ($param, $table) {
                    $join->on("{$table['material_file_log']}.signature", '=', "{$table['ad3_common_log']}.signature");
                });
            }

            $overview_log->whereBetween("log_date", $param->cost_date);

            foreach ($param->overview_log_filter as [$condition, $value]) {
                $overview_log->whereRaw($condition, $value);
            }

            if ($param->need_join_ad3_common_log['overview_log'] ?? false) {
                $overview_log->groupBy(["{$table['ad3_common_log']}.account_id"]);
            } else {
                $overview_log->groupBy(["{$table['ad2_common_log']}.account_id"]);
            }

            $main_builder->withExpression('overview_log', $overview_log);

            $main_builder->leftJoin('overview_log', function (JoinClause $join) use ($param) {
                $join->on('driver.account_id', '=', 'overview_log.account_id');
            });
        }

        // 组装用户注册表
        if (!empty($param->reg_log_target)) {

            $param->reg_log_target[] = ADComposeFilterSqlMap::FORMULA['reg_log']['reg_uid_count'];
            $param->main_target[] = 'reg_uid_count';
            $param->reg_log_target = array_unique($param->reg_log_target);
            $param->main_target = array_unique($param->main_target);

            if ($param->need_join_ad3_common_log['reg_log'] ?? false) {
                $reg_log->selectRaw("{$table['ad3_common_log']}.account_id");
            } else {
                $reg_log->selectRaw("{$table['ad2_common_log']}.account_id");
            }

            foreach ($param->reg_log_target as $key => $value) {
                $reg_log->selectRaw($value);
            }

            $this->injectAgentPermissionAndJoinAgentSite($reg_log, $agent_permission, $table['reg_log']);
            $this->injectGamePermissionAndJoinGame($reg_log, $game_permission, $table['reg_log']);

            if ($param->need_join_ad3_common_log['reg_log'] ?? false) {
                $reg_log->join($table['ad3_common_log'], function (JoinClause $join) use ($param, $table) {
                    $join->on("{$table['reg_log']}.ad4_id", '=', "{$table['ad3_common_log']}.ad4_id");
                    $join->on("{$table['reg_log']}.platform", '=', "{$table['ad3_common_log']}.platform");
//                    $join->on("{$table['reg_log']}.ad_platform_id", '=', "{$table['ad3_common_log']}.media_type");
                    $join->on("{$this->v2_dim_agent_site_id_alias}.media_type_id", '=', "{$table['ad3_common_log']}.media_type");

                    $join->where("{$table['ad3_common_log']}.media_type", '=', $param->media_type);
                    $join->where("{$table['ad3_common_log']}.platform", '=', $param->platform);
                });
                if ($param->ad2_create_time || $param->need_join_ad2_common_log['reg_log'] ?? false) {
                    $reg_log->join($table['ad2_common_log'], function (JoinClause $join) use ($param, $table) {
                        $join->on("{$table['reg_log']}.adgroup_id", '=', "{$table['ad2_common_log']}.ad2_id");
                        $join->on("{$table['reg_log']}.platform", '=', "{$table['ad2_common_log']}.platform");
//                        $join->on("{$table['reg_log']}.ad_platform_id", '=', "{$table['ad2_common_log']}.media_type");
                        $join->on("{$this->v2_dim_agent_site_id_alias}.media_type_id", '=', "{$table['ad2_common_log']}.media_type");

                        $join->where("{$table['ad2_common_log']}.media_type", '=', $param->media_type);
                        $join->where("{$table['ad2_common_log']}.platform", '=', $param->platform);
                        $param->ad2_create_time && $join->whereBetween("{$table['ad2_common_log']}.ad2_create_time", $param->ad2_create_time);
                    });
                }
            } else {
                $reg_log->join($table['ad2_common_log'], function (JoinClause $join) use ($param, $table) {
                    $join->on("{$table['reg_log']}.adgroup_id", '=', "{$table['ad2_common_log']}.ad2_id");
                    $join->on("{$table['reg_log']}.platform", '=', "{$table['ad2_common_log']}.platform");
//                    $join->on("{$table['reg_log']}.ad_platform_id", '=', "{$table['ad2_common_log']}.media_type");
                    $join->on("{$this->v2_dim_agent_site_id_alias}.media_type_id", '=', "{$table['ad2_common_log']}.media_type");

                    $join->where("{$table['ad2_common_log']}.media_type", '=', $param->media_type);
                    $join->where("{$table['ad2_common_log']}.platform", '=', $param->platform);
                    $param->ad2_create_time && $join->whereBetween("{$table['ad2_common_log']}.ad2_create_time", $param->ad2_create_time);
                });
            }

            if ($param->need_join_material_file_log['reg_log'] ?? false) {
                $reg_log->leftJoin($table['material_file_log'], function (JoinClause $join) use ($param, $table) {
                    $join->on("{$table['material_file_log']}.signature", '=', "{$table['ad3_common_log']}.signature");
                });
            }

            $reg_log->whereBetween("game_reg_time ", $param->cost_date);

            foreach ($param->reg_log_filter as [$condition, $value]) {
                $reg_log->whereRaw($condition, $value);
            }

            if ($param->need_join_ad3_common_log['reg_log'] ?? false) {
                $reg_log->groupBy(["{$table['ad3_common_log']}.account_id"]);
            } else {
                $reg_log->groupBy(["{$table['ad2_common_log']}.account_id"]);
            }

            $main_builder->withExpression('reg_log', $reg_log);

            $main_builder->leftJoin('reg_log', function (JoinClause $join) use ($param) {
                $join->on('driver.account_id', '=', 'reg_log.account_id');
            });

            $main_builder->orderBy('reg_uid_count', 'desc');
        }

        // 组装特定媒体特定字段
        $media_hour_data_log = $this->getMediaHourDataLogTargetAndFilter($param);
        if ($media_hour_data_log) {
            $media_hour_data_log_alias = $media_hour_data_log['alias'];
            $media_hour_data_log_table = $media_hour_data_log['table'];
            $media_hour_data_log_builder = $media_hour_data_log['builder'];
            $media_hour_data_log_target = $media_hour_data_log['target'];
            $media_hour_data_log_filter = $media_hour_data_log['filter'];
            if (!empty($media_hour_data_log_target)) {

                $media_hour_data_log_builder->selectRaw("{$media_hour_data_log_table}.account_id");

                foreach ($media_hour_data_log_target as $key => $value) {
                    $media_hour_data_log_builder->selectRaw($value);
                }

                $this->injectAgentPermissionAndJoinAgentSite($media_hour_data_log_builder, $agent_permission, $media_hour_data_log_table);
                $this->injectGamePermissionAndJoinGame($media_hour_data_log_builder, $game_permission, $media_hour_data_log_table);

                if ($param->need_join_ad3_common_log[$media_hour_data_log_alias] ?? false) {
                    $media_hour_data_log_builder->join($table['ad3_common_log'], function (JoinClause $join) use ($param, $table, $media_hour_data_log_table) {
                        $join->on("{$media_hour_data_log_table}.creative_id", '=', "{$table['ad3_common_log']}.ad3_id");
                        $join->on("{$media_hour_data_log_table}.platform", '=', "{$table['ad3_common_log']}.platform");

                        $join->where(["{$table['ad3_common_log']}.platform" => $param->platform]);
                        $join->where(["{$table['ad3_common_log']}.media_type" => $param->media_type]);
                    });
                    if ($param->ad2_create_time || $param->need_join_ad2_common_log[$media_hour_data_log_alias] ?? false) {
                        $media_hour_data_log_builder->join($table['ad2_common_log'], function (JoinClause $join) use ($param, $table, $media_hour_data_log_table) {
                            $join->on("{$media_hour_data_log_table}.ad_id", '=', "{$table['ad2_common_log']}.ad2_id");
                            $join->on("{$media_hour_data_log_table}.platform", '=', "{$table['ad2_common_log']}.platform");

                            $param->ad2_create_time && $join->whereBetween("{$table['ad2_common_log']}.ad2_create_time", $param->ad2_create_time);
                        });
                    }
                } elseif ($param->ad2_create_time || $param->need_join_ad2_common_log[$media_hour_data_log_alias] ?? false) {
                    $media_hour_data_log_builder->join($table['ad2_common_log'], function (JoinClause $join) use ($param, $table, $media_hour_data_log_table) {
                        $join->on("{$media_hour_data_log_table}.ad_id", '=', "{$table['ad2_common_log']}.ad2_id");
                        $join->on("{$media_hour_data_log_table}.platform", '=', "{$table['ad2_common_log']}.platform");

                        $join->where(["{$table['ad2_common_log']}.platform" => $param->platform]);
                        $join->where(["{$table['ad2_common_log']}.media_type" => $param->media_type]);
                        $param->ad2_create_time && $join->whereBetween("{$table['ad2_common_log']}.ad2_create_time", $param->ad2_create_time);
                    });
                } else {
                    $media_hour_data_log_builder->where(["{$media_hour_data_log_table}.platform" => $param->platform]);
                }

                if ($param->need_join_material_file_log[$media_hour_data_log_alias] ?? false) {
                    $media_hour_data_log_builder->leftJoin($table['material_file_log'], function (JoinClause $join) use ($param, $table) {
                        $join->on("{$table['material_file_log']}.signature", '=', "{$table['ad3_common_log']}.signature");
                    });
                }

                $media_hour_data_log_builder->whereBetween("cost_date", $param->cost_date);

                foreach ($media_hour_data_log_filter as [$condition, $value]) {
                    $media_hour_data_log_builder->whereRaw($condition, $value);
                }

                $media_hour_data_log_builder->groupBy(["{$media_hour_data_log_table}.account_id"]);

                $main_builder->withExpression($media_hour_data_log_alias, $media_hour_data_log_builder);

                $main_builder->leftJoin($media_hour_data_log_alias, function (JoinClause $join) use ($param, $media_hour_data_log_alias) {
                    $join->on('driver.account_id', '=', "{$media_hour_data_log_alias}.account_id");
                });
            }
        }

        $main_builder->selectRaw("driver.account_id");
        $main_builder->selectRaw("driver.account_name");
        $main_builder->selectRaw("driver.account_leader");

        foreach ($param->main_target as $key => $value) {
            $main_builder->selectRaw($value);
        }

        foreach ($param->main_calc as [$condition, $value]) {
            !is_null($value) && $main_builder->havingRaw($condition, (array)$value, $param->condition);
        }

        $main_builder->limit($param->limit);

        return [
            'list' => $main_builder->get(),
            'sql' => $this->getSql($main_builder)
        ];
    }

    /**
     * 定向包报表筛选
     * @param ADComposeFilterParam $param
     * @param int $agent_permission
     * @param int $game_permission
     * @return array
     */
    public function getTargetingFilter(ADComposeFilterParam $param, $agent_permission = -1, $game_permission = -1)
    {
        $table = ADComposeFilterSqlMap::TABLE;

        $driver_builder = $this->builder->from($table['ad2_common_log']);
        $ad2_common_log = $this->builder->from($table['ad2_common_log']);
        $hour_data_log = $this->builder->from($table['hour_data_log']);
        $overview_log = $this->builder->from($table['overview_log']);
        $overview_log->where("{$table['overview_log']}.ad2_id", '>', 0);
        $reg_log = $this->builder->from($table['reg_log']);
        $reg_log->where("{$table['reg_log']}.action_id", '=', 2);
        $reg_log->where("{$table['reg_log']}.adgroup_id ", '!=', '');
        $main_builder = $this->builder->from('driver');

        // 组装驱动表
        $driver_builder->selectRaw("{$table['ad2_common_log']}.platform");
        $driver_builder->selectRaw("{$table['ad2_common_log']}.ad2_name as ad_name");
        $driver_builder->selectRaw("{$table['ad2_common_log']}.audience_md5");
        $driver_builder->selectRaw("{$table['ad2_common_log']}.web_creator");
        $driver_builder->selectRaw("min({$table['ad2_common_log']}.ad2_create_time) as ad_create_time");

        $driver_builder->join($table['account_common_log'], function (JoinClause $join) use ($param, $table) {
            $join->on("{$table['account_common_log']}.account_id", '=', "{$table['ad2_common_log']}.account_id");
            $join->on("{$table['account_common_log']}.platform", '=', "{$table['ad2_common_log']}.platform");

            $join->where(["{$table['account_common_log']}.platform" => $param->platform]);
        });

        $this->injectAgentPermissionAndJoinAgentSite($driver_builder, $agent_permission, $table['ad2_common_log']);
        $this->injectGamePermissionAndJoinSiteGame($driver_builder, $game_permission, $table['ad2_common_log']);

        $driver_builder->where("{$table['ad2_common_log']}.audience_md5", '!=', '');

        $driver_builder->where(function ($query1) use ($table, $param) {
            /* @var  Builder $query1 */
            $query1->where(function ($query2) use ($table) {
                /* @var  Builder $query2 */
                $query2->where("{$table['ad2_common_log']}.custom_audience_name", '=', '');
                $query2->where("{$table['ad2_common_log']}.excluded_custom_audience_name", '=', '');
            });
            $query1->orWhere("{$table['account_common_log']}.company", '=', $param->company);
        });

        if ($param->filter_mode === 0) {
            // 物料筛选模式
            foreach ($param->ad2_common_log_filter as [$condition, $value]) {
                if (strpos($condition, 'agent_leader') !== false) {
                    $driver_builder->where("{$table['ad2_common_log']}.web_creator", '=', $value);
                }
                if (strpos($condition, 'account_id') !== false) {
                    $driver_builder->whereIn("{$table['ad2_common_log']}.account_id", $value);
                }
                if (strpos($condition, 'ad2_id') !== false) {
                    $driver_builder->whereIn("{$table['ad2_common_log']}.ad2_id", $value);
                }
            }
        } else {
            // 全数据筛选模式
            if ($param->need_join_ad3_common_log['ad2_common_log'] ?? false) {
                $driver_builder->leftJoin($table['ad3_common_log'], function (JoinClause $join) use ($param, $table) {
                    $join->on("{$table['ad2_common_log']}.account_id", '=', "{$table['ad3_common_log']}.account_id");
                    $join->on("{$table['ad2_common_log']}.media_type", '=', "{$table['ad3_common_log']}.media_type");
                    $join->on("{$table['ad2_common_log']}.platform", '=', "{$table['ad3_common_log']}.platform");

                    $join->where(["{$table['ad2_common_log']}.platform" => $param->platform]);
                    $join->where(["{$table['ad2_common_log']}.media_type" => $param->media_type]);
                });
            }

            if ($param->need_join_material_file_log['ad2_common_log'] ?? false) {
                $driver_builder->leftJoin($table['material_file_log'], function (JoinClause $join) use ($param, $table) {
                    $join->on("{$table['material_file_log']}.signature", '=', "{$table['ad3_common_log']}.signature");
                });
            }

            foreach ($param->ad2_common_log_filter as [$condition, $value]) {
                $driver_builder->whereRaw($condition, $value);
            }
        }

        $driver_builder->groupBy("{$table['ad2_common_log']}.audience_md5");

        $main_builder->withExpression('driver', $driver_builder);

        $main_builder->orderBy('driver.ad_create_time', 'desc');

        // 组装计数项
        if ($param->ad2_common_log_target) {
            $ad2_common_log->selectRaw("{$table['ad2_common_log']}.audience_md5");

            foreach ($param->ad2_common_log_target as $target) {
                $ad2_common_log->selectRaw($target);
            }

            $this->injectAgentPermissionAndJoinAgentSite($ad2_common_log, $agent_permission, $table['ad2_common_log']);
            $this->injectGamePermissionAndJoinSiteGame($ad2_common_log, $game_permission, $table['ad2_common_log']);

            if ($param->need_join_ad3_common_log['ad2_common_log'] ?? false) {
                $ad2_common_log->join($table['ad3_common_log'], function (JoinClause $join) use ($param, $table) {
                    $join->on("{$table['ad2_common_log']}.ad2_id", '=', "{$table['ad3_common_log']}.ad2_id");
                    $join->on("{$table['ad2_common_log']}.media_type", '=', "{$table['ad3_common_log']}.media_type");
                    $join->on("{$table['ad2_common_log']}.platform", '=', "{$table['ad3_common_log']}.platform");

                    $join->where(["{$table['ad2_common_log']}.platform" => $param->platform]);
                    $join->where(["{$table['ad2_common_log']}.media_type" => $param->media_type]);
                });
            } else {
                $ad2_common_log->where(["{$table['ad2_common_log']}.platform" => $param->platform]);
                $ad2_common_log->where(["{$table['ad2_common_log']}.media_type" => $param->media_type]);
            }

            if ($param->need_join_material_file_log['ad2_common_log'] ?? false) {
                $ad2_common_log->join($table['material_file_log'], function (JoinClause $join) use ($param, $table) {
                    $join->on("{$table['material_file_log']}.signature", '=', "{$table['ad3_common_log']}.signature");
                });
            }

            $param->ad2_create_time && $ad2_common_log->whereBetween("{$table['ad2_common_log']}.ad2_create_time", $param->ad2_create_time);

            foreach ($param->ad2_common_log_filter as [$condition, $value]) {
                $ad2_common_log->whereRaw($condition, $value);
            }

            $ad2_common_log->groupBy(["audience_md5"]);

            $main_builder->withExpression('ad2_common_log', $ad2_common_log);

            $main_builder->leftJoin('ad2_common_log', function (JoinClause $join) use ($param) {
                $join->on("driver.audience_md5", '=', "ad2_common_log.audience_md5");
            });
        }

        // 组装小时表
        if (!empty($param->hour_data_log_target)) {

            $param->hour_data_log_target[] = ADComposeFilterSqlMap::FORMULA['hour_data_log']['sum_cost'];
            $param->main_target[] = 'sum_cost';
            $param->hour_data_log_target = array_unique($param->hour_data_log_target);
            $param->main_target = array_unique($param->main_target);

            $hour_data_log->selectRaw("{$table['ad2_common_log']}.audience_md5");

            foreach ($param->hour_data_log_target as $key => $value) {
                $hour_data_log->selectRaw($value);
            }

            $this->injectAgentPermissionAndJoinAgentSite($hour_data_log, $agent_permission, $table['hour_data_log']);
            $this->injectGamePermissionAndJoinGame($hour_data_log, $game_permission, $table['hour_data_log']);

            $hour_data_log->join($table['ad2_common_log'], function (JoinClause $join) use ($param, $table) {
                $join->on("{$table['hour_data_log']}.ad2_id", '=', "{$table['ad2_common_log']}.ad2_id");
                $join->on("{$table['hour_data_log']}.media_type", '=', "{$table['ad2_common_log']}.media_type");
                $join->on("{$table['hour_data_log']}.platform", '=', "{$table['ad2_common_log']}.platform");

                $join->where(["{$table['ad2_common_log']}.platform" => $param->platform]);
                $join->where(["{$table['ad2_common_log']}.media_type" => $param->media_type]);
                $param->ad2_create_time && $join->whereBetween("{$table['ad2_common_log']}.ad2_create_time", $param->ad2_create_time);
            });

            if ($param->need_join_ad3_common_log['hour_data_log'] ?? false) {
                $hour_data_log->join($table['ad3_common_log'], function (JoinClause $join) use ($param, $table) {
                    $join->on("{$table['hour_data_log']}.ad4_id", '=', "{$table['ad3_common_log']}.ad4_id");
                    $join->on("{$table['hour_data_log']}.media_type", '=', "{$table['ad3_common_log']}.media_type");
                    $join->on("{$table['hour_data_log']}.platform", '=', "{$table['ad3_common_log']}.platform");
                });
            }
            if ($param->need_join_material_file_log['hour_data_log'] ?? false) {
                $hour_data_log->join($table['material_file_log'], function (JoinClause $join) use ($param, $table) {
                    $join->on("{$table['material_file_log']}.signature", '=', "{$table['ad3_common_log']}.signature");
                });
            }

            $hour_data_log->whereBetween("cost_date", $param->cost_date);

            foreach ($param->hour_data_log_filter as [$condition, $value]) {
                $hour_data_log->whereRaw($condition, $value);
            }

            $hour_data_log->groupBy(["audience_md5"]);

            $main_builder->withExpression('hour_data_log', $hour_data_log);

            $main_builder->leftJoin('hour_data_log', function (JoinClause $join) use ($param) {
                $join->on("driver.audience_md5", '=', "hour_data_log.audience_md5");
            });

            $main_builder->orderBy('sum_cost', 'desc');
        }

        // 组装业务数据表
        if (!empty($param->overview_log_target)) {

            $overview_log->selectRaw("{$table['ad2_common_log']}.audience_md5");

            foreach ($param->overview_log_target as $key => $value) {
                $overview_log->selectRaw($value);
            }

            $this->injectAgentPermissionAndJoinAgentSite($overview_log, $agent_permission, $table['overview_log']);
            $this->injectGamePermissionAndJoinGame($overview_log, $game_permission, $table['overview_log']);

            $overview_log->join($table['ad2_common_log'], function (JoinClause $join) use ($param, $table) {
                $join->on("{$table['overview_log']}.ad2_id", '=', "{$table['ad2_common_log']}.ad2_id");
//                $join->on("{$table['overview_log']}.media_type", '=', "{$table['ad2_common_log']}.media_type");
                $join->on("{$this->v2_dim_agent_site_id_alias}.media_type_id", '=', "{$table['ad2_common_log']}.media_type");
                $join->on("{$table['overview_log']}.platform", '=', "{$table['ad2_common_log']}.platform");

                $join->where(["{$table['ad2_common_log']}.platform" => $param->platform]);
                $join->where(["{$table['ad2_common_log']}.media_type" => $param->media_type]);
                $param->ad2_create_time && $join->whereBetween("{$table['ad2_common_log']}.ad2_create_time", $param->ad2_create_time);
            });

            if ($param->need_join_ad3_common_log['overview_log'] ?? false) {
                $overview_log->join($table['ad3_common_log'], function (JoinClause $join) use ($param, $table) {
                    $join->on("{$table['overview_log']}.ad4_id", '=', "{$table['ad3_common_log']}.ad4_id");
//                    $join->on("{$table['overview_log']}.media_type", '=', "{$table['ad3_common_log']}.media_type");
                    $join->on("{$this->v2_dim_agent_site_id_alias}.media_type_id", '=', "{$table['ad3_common_log']}.media_type");
                    $join->on("{$table['overview_log']}.platform", '=', "{$table['ad3_common_log']}.platform");
                });
            }

            if ($param->need_join_material_file_log['overview_log'] ?? false) {
                $overview_log->join($table['material_file_log'], function (JoinClause $join) use ($param, $table) {
                    $join->on("{$table['material_file_log']}.signature", '=', "{$table['ad3_common_log']}.signature");
                });
            }

            $overview_log->whereBetween("log_date", $param->cost_date);

            foreach ($param->overview_log_filter as [$condition, $value]) {
                $overview_log->whereRaw($condition, $value);
            }

            $overview_log->groupBy(["audience_md5"]);

            $main_builder->withExpression('overview_log', $overview_log);

            $main_builder->leftJoin('overview_log', function (JoinClause $join) use ($param) {
                $join->on("driver.audience_md5", '=', "overview_log.audience_md5");
            });
        }

        // 组装用户注册表
        if (!empty($param->reg_log_target)) {

            $param->reg_log_target[] = ADComposeFilterSqlMap::FORMULA['reg_log']['reg_uid_count'];
            $param->main_target[] = 'reg_uid_count';
            $param->reg_log_target = array_unique($param->reg_log_target);
            $param->main_target = array_unique($param->main_target);

            $reg_log->selectRaw("{$table['ad2_common_log']}.audience_md5");

            foreach ($param->reg_log_target as $key => $value) {
                $reg_log->selectRaw($value);
            }

            $this->injectAgentPermissionAndJoinAgentSite($reg_log, $agent_permission, $table['reg_log']);
            $this->injectGamePermissionAndJoinGame($reg_log, $game_permission, $table['reg_log']);

            $reg_log->join($table['ad2_common_log'], function (JoinClause $join) use ($param, $table) {
                $join->on("{$table['reg_log']}.adgroup_id", '=', "{$table['ad2_common_log']}.ad2_id");
                $join->on("{$table['reg_log']}.platform", '=', "{$table['ad2_common_log']}.platform");
//                $join->on("{$table['reg_log']}.ad_platform_id", '=', "{$table['ad2_common_log']}.media_type");
                $join->on("{$this->v2_dim_agent_site_id_alias}.media_type_id", '=', "{$table['ad2_common_log']}.media_type");

                $join->where("{$table['ad2_common_log']}.media_type", '=', $param->media_type);
                $join->where("{$table['ad2_common_log']}.platform", '=', $param->platform);
                $param->ad2_create_time && $join->whereBetween("{$table['ad2_common_log']}.ad2_create_time", $param->ad2_create_time);
            });

            if ($param->need_join_ad3_common_log['reg_log'] ?? false) {
                $reg_log->join($table['ad3_common_log'], function (JoinClause $join) use ($param, $table) {
                    $join->on("{$table['reg_log']}.ad4_id", '=', "{$table['ad3_common_log']}.ad4_id");
                    $join->on("{$table['reg_log']}.platform", '=', "{$table['ad3_common_log']}.platform");
//                    $join->on("{$table['reg_log']}.ad_platform_id", '=', "{$table['ad3_common_log']}.media_type");
                    $join->on("{$this->v2_dim_agent_site_id_alias}.media_type_id", '=', "{$table['ad3_common_log']}.media_type");

                    $join->where("{$table['ad3_common_log']}.media_type", '=', $param->media_type);
                    $join->where("{$table['ad3_common_log']}.platform", '=', $param->platform);
                });
            }
            if ($param->need_join_material_file_log['reg_log'] ?? false) {
                $reg_log->join($table['material_file_log'], function (JoinClause $join) use ($param, $table) {
                    $join->on("{$table['material_file_log']}.signature", '=', "{$table['ad3_common_log']}.signature");
                });
            }

            $reg_log->whereBetween("game_reg_time ", $param->cost_date);

            foreach ($param->reg_log_filter as [$condition, $value]) {
                $reg_log->whereRaw($condition, $value);
            }

            $reg_log->groupBy(["audience_md5"]);


            $main_builder->withExpression('reg_log', $reg_log);

            $main_builder->leftJoin('reg_log', function (JoinClause $join) use ($param) {
                $join->on("driver.audience_md5", '=', "reg_log.audience_md5");
            });

            $main_builder->orderBy('reg_uid_count', 'desc');
        }

        // 组装特定媒体特定字段
        $media_hour_data_log = $this->getMediaHourDataLogTargetAndFilter($param);
        if ($media_hour_data_log) {

            $media_hour_data_log_alias = $media_hour_data_log['alias'];
            $media_hour_data_log_table = $media_hour_data_log['table'];
            $media_hour_data_log_builder = $media_hour_data_log['builder'];
            $media_hour_data_log_target = $media_hour_data_log['target'];
            $media_hour_data_log_filter = $media_hour_data_log['filter'];

            if (!empty($media_hour_data_log_target)) {

                $media_hour_data_log_builder->selectRaw("{$table['ad2_common_log']}.audience_md5");

                foreach ($media_hour_data_log_target as $key => $value) {
                    $media_hour_data_log_builder->selectRaw($value);
                }

                $this->injectAgentPermissionAndJoinAgentSite($media_hour_data_log_builder, $agent_permission, $media_hour_data_log_table);
                $this->injectGamePermissionAndJoinGame($media_hour_data_log_builder, $game_permission, $media_hour_data_log_table);

                $media_hour_data_log_builder->join($table['ad2_common_log'], function (JoinClause $join) use ($param, $table, $media_hour_data_log_table) {
                    $join->on("{$media_hour_data_log_table}.ad_id", '=', "{$table['ad2_common_log']}.ad2_id");
                    $join->on("{$media_hour_data_log_table}.platform", '=', "{$table['ad2_common_log']}.platform");

                    $join->where(["{$table['ad2_common_log']}.platform" => $param->platform]);
                    $join->where(["{$table['ad2_common_log']}.media_type" => $param->media_type]);
                    $param->ad2_create_time && $join->whereBetween("{$table['ad2_common_log']}.ad2_create_time", $param->ad2_create_time);
                });

                if ($param->need_join_ad3_common_log[$media_hour_data_log_alias] ?? false) {
                    $media_hour_data_log_builder->join($table['ad3_common_log'], function (JoinClause $join) use ($param, $table, $media_hour_data_log_table) {
                        $join->on("{$media_hour_data_log_table}.creative_id", '=', "{$table['ad3_common_log']}.ad4_id");
                        $join->on("{$media_hour_data_log_table}.platform", '=', "{$table['ad3_common_log']}.platform");
                    });
                }
                if ($param->need_join_material_file_log[$media_hour_data_log_alias] ?? false) {
                    $media_hour_data_log_builder->join($table['material_file_log'], function (JoinClause $join) use ($param, $table) {
                        $join->on("{$table['material_file_log']}.signature", '=', "{$table['ad3_common_log']}.signature");
                    });
                }

                $media_hour_data_log_builder->whereBetween("cost_date", $param->cost_date);

                foreach ($media_hour_data_log_filter as [$condition, $value]) {
                    $media_hour_data_log_builder->whereRaw($condition, $value);
                }

                $media_hour_data_log_builder->groupBy(["audience_md5"]);

                $main_builder->withExpression($media_hour_data_log_alias, $media_hour_data_log_builder);

                $main_builder->leftJoin($media_hour_data_log_alias, function (JoinClause $join) use ($param, $media_hour_data_log_alias) {
                    $join->on("driver.audience_md5", '=', "{$media_hour_data_log_alias}.audience_md5");
                });
            }
        }

        $main_builder->selectRaw("driver.audience_md5 as audience_md5");
        $main_builder->selectRaw("driver.ad_name as ad_name");
        $main_builder->selectRaw("driver.web_creator as web_creator");
        $main_builder->selectRaw("driver.ad_create_time as ad_create_time");

        foreach ($param->main_target as $key => $value) {
            $main_builder->selectRaw($value);
        }

        foreach ($param->main_calc as [$condition, $value]) {
            !is_null($value) && $main_builder->havingRaw($condition, (array)$value, $param->condition);
        }

        $main_builder->limit($param->limit);

        return [
            'list' => $main_builder->get(),
            'sql' => $this->getSql($main_builder)
        ];
    }

    /**
     * 素材文件报表筛选
     * @param ADComposeFilterParam $param
     * @param $user_list
     * @param $material_permission
     * @param int $agent_permission
     * @param int $game_permission
     * @param array $media_width_height
     * @return array
     */
    public function getMaterialFileFilter(ADComposeFilterParam $param, $user_list, $material_permission, $agent_permission = -1, $game_permission = -1, $media_width_height = [])
    {
        $table = ADComposeFilterSqlMap::TABLE;

        $media_table = ADComposeFilterSqlMap::MEDIA_TABLE;

        if ($param->filter_game_mode == 'root_game') {
            $table['overview_log'] = 'tanwan_datahub.v3_ads_day_root_game_overview_log';
        }

        if ($param->filter_game_mode == 'reflux') {
            $table['overview_log'] = 'tanwan_datahub.v3_ads_day_root_game_back_overview_log';
        }


        $driver_builder = $this->builder->from($table['material_file_log']);
        $ad2_common_log = $this->builder->from($table['ad2_common_log']);
        $hour_data_log = $this->builder->from($table['hour_data_log']);
        $overview_log = $this->builder->from($table['overview_log']);
        $overview_log->where("{$table['overview_log']}.ad2_id", '>', 0);
        $reg_log = $this->builder->from($table['reg_log']);
        $reg_log->where("{$table['reg_log']}.action_id", '=', 2);
        $reg_log->where("{$table['reg_log']}.adgroup_id ", '!=', '');
        $main_builder = $this->builder->from('driver');

        // 组装驱动表
        $driver_builder->selectRaw("{$table['material_file_log']}.platform");
        $driver_builder->selectRaw("{$table['material_file_log']}.filename as filename");
        $driver_builder->selectRaw("{$table['material_file_log']}.id as material_file_id");
        $driver_builder->selectRaw("{$table['material_file_log']}.material_id as material_id");
        $driver_builder->selectRaw("{$table['material_file_log']}.file_type as file_type");
        $driver_builder->selectRaw("{$table['material_file_log']}.url as url");
        $driver_builder->selectRaw("{$table['material_file_log']}.width as width");
        $driver_builder->selectRaw("{$table['material_file_log']}.height as height");
        $driver_builder->selectRaw("{$table['material_file_log']}.uploader as uploader");
        $driver_builder->selectRaw("{$table['material_file_log']}.insert_time as insert_time");
        $driver_builder->selectRaw("{$table['material_file_log']}.duration as duration");
        $driver_builder->selectRaw("{$table['material_file_log']}.bitrate as bitrate");
        $driver_builder->selectRaw("{$table['material_file_log']}.size as size");
        $driver_builder->selectRaw("{$table['material_file_log']}.signature as signature");
        $driver_builder->selectRaw("{$table['material_file_log']}.notify as notify");

        $driver_builder->groupBy('material_file_id');

        $this->setOdsMaterialLogAlias('ods_material_log');
        $this->injectMaterialPermissionAndJoinMaterial($driver_builder, $user_list, $material_permission, $table['material_file_log']);

        foreach ($param->material_log_target as $target) {
            $driver_builder->selectRaw($target);
        }

        if (($param->need_join_ad3_common_log['material_file_log'] ?? false) || $param->tt_inefficient_material_log_target) {
            $driver_builder->leftJoin($table['ad3_common_log'], function (JoinClause $join) use ($param, $table) {
                $join->on("{$table['ad3_common_log']}.signature", '=', "{$table['material_file_log']}.signature");

                $join->where("{$table['ad3_common_log']}.media_type", '=', $param->media_type);
                $join->where("{$table['ad3_common_log']}.platform", '=', $param->platform);
            });
        }

        if (($param->need_join_tt_inefficient_material_log['material_file_log'] ?? false) || $param->tt_inefficient_material_log_target) {
            $driver_builder->leftJoin($media_table['tt_inefficient_material_log'], function (JoinClause $join) use ($param, $table, $media_table) {
                $join->on("{$table['ad3_common_log']}.media_material_id", '=', "{$media_table['tt_inefficient_material_log']}.material_id");
            });
        }

        foreach ($param->material_file_log_target as $target) {
            $driver_builder->selectRaw($target);
        }

        foreach ($param->material_log_extend_filter as $extend_target) {
            $driver_builder->whereIn("{$table['material_log']}.{$extend_target}", $param->$extend_target);
        }

        if ($param->filter_mode == 0) {
            foreach ($param->material_file_log_filter as [$condition, $value]) {
                if (strpos($condition, 'material_file_id') !== false) {
                    $driver_builder->whereRaw($condition, $value);
                }
                if (strpos($condition, 'material_filename') !== false) {
                    $driver_builder->whereRaw($condition, $value);
                }
                if (strpos($condition, 'material_id') !== false) {
                    $driver_builder->whereRaw($condition, $value);
                }
                if (strpos($condition, 'ods_material_log.name') !== false) {
                    $driver_builder->where(function ($query_sub) use ($value) {
                        foreach ($value as $v) {
                            /* @var  Builder $query_sub */
                            $query_sub->orWhere("tanwan_datamedia.ods_material_log.name", 'LIKE', "%$v%");
                        }
                    });
                }
                if (strpos($condition, 'theme_pid') !== false) {
                    $driver_builder->whereRaw($condition, $value);
                }
                if (strpos($condition, 'signature') !== false) {
                    $driver_builder->whereRaw($condition, $value);
                }
                if (strpos($condition, 'theme_id') !== false) {
                    $driver_builder->whereRaw($condition, $value);
                }
                if (strpos($condition, 'original') !== false) {
                    $driver_builder->whereRaw($condition, $value);
                }
                if (strpos($condition, 'effect_grade30') !== false) {
                    $driver_builder->whereRaw($condition, $value);
                }
                if (strpos($condition, 'effect_grade7') !== false) {
                    $driver_builder->whereRaw($condition, $value);
                }
                if (strpos($condition, 'is_priority') !== false) {
                    $driver_builder->whereRaw($condition, $value);
                }
            }
        } else {
            if ($param->need_join_ad2_common_log['material_file_log'] ?? false) {
                if (!($param->need_join_ad3_common_log['material_file_log'] ?? false)) {
                    $driver_builder->leftJoin($table['ad3_common_log'], function (JoinClause $join) use ($param, $table) {
                        $join->on("{$table['ad3_common_log']}.signature", '=', "{$table['material_file_log']}.signature");

                        $join->where("{$table['ad3_common_log']}.media_type", '=', $param->media_type);
                        $join->where("{$table['ad3_common_log']}.platform", '=', $param->platform);
                    });
                }

                $driver_builder->leftJoin($table['ad2_common_log'], function (JoinClause $join) use ($param, $table) {
                    $join->on("{$table['ad3_common_log']}.ad2_id", '=', "{$table['ad2_common_log']}.ad2_id");
                    $join->on("{$table['ad3_common_log']}.media_type", '=', "{$table['ad2_common_log']}.media_type");
                    $join->on("{$table['ad3_common_log']}.platform", '=', "{$table['ad2_common_log']}.platform");

                    $join->where("{$table['ad2_common_log']}.media_type", '=', $param->media_type);
                    $join->where("{$table['ad2_common_log']}.platform", '=', $param->platform);
                });
            }

            if ($param->need_join_game_log['ad2_common_log'] ?? false) {
                $this->injectAgentPermissionAndJoinAgentSite($driver_builder, $agent_permission, $table['ad2_common_log']);
                $this->injectGamePermissionAndJoinSiteGame($driver_builder, $game_permission, $table['ad2_common_log']);
            }

            foreach ($param->material_file_log_filter as [$condition, $value]) {

                if (strpos($condition, 'ods_material_log.name') !== false) {
                    $driver_builder->where(function ($query_sub) use ($value) {
                        foreach ($value as $v) {
                            /* @var  Builder $query_sub */
                            $query_sub->orWhere("tanwan_datamedia.ods_material_log.name", 'LIKE', "%$v%");
                        }
                    });
                } else {
                    $driver_builder->whereRaw($condition, $value);
                }

            }
        }

        // 根据媒体类型控制素材宽高
        if ($media_width_height) {
            $driver_builder->where(function ($query1) use ($media_width_height, $table) {
                foreach ($media_width_height as $key => [$width, $height]) {
                    /* @var  Builder $query1 */
                    $query1->orWhere(function ($query2) use ($width, $height, $table) {
                        /* @var  Builder $query2 */
                        $query2->where("{$table['material_file_log']}.width", '=', $width);
                        $query2->where("{$table['material_file_log']}.height", '=', $height);
                    });
                }
            });
        }

        $driver_builder->where("{$table['material_file_log']}.platform", '=', $param->material_create_platform);
        $driver_builder->where("{$table['material_file_log']}.file_type", '!=', '3');
        $driver_builder->where("{$table['material_file_log']}.is_ext", '!=', '2');
        if (is_numeric($param->expend_type)) {
            $driver_builder->where("{$table['material_file_log']}.is_ext", '=', $param->expend_type);
        }
        $driver_builder->where("{$table['material_file_log']}.is_del", '=', 0);
        $driver_builder->where("{$table['material_file_log']}.signature", '!=', '');
        $driver_builder->where("{$table['material_log']}.is_del", '=', 0);
        $param->file_type && $driver_builder->where("{$table['material_file_log']}.file_type", '=', $param->file_type);
        $param->width && $driver_builder->where("{$table['material_file_log']}.width", '=', $param->width);
        $param->height && $driver_builder->where("{$table['material_file_log']}.height", '=', $param->height);
        $param->material_create_time && $driver_builder->whereBetween("{$table['material_file_log']}.insert_time", $param->material_create_time);

        $main_builder->withExpression('driver', $driver_builder);

        $main_builder->orderBy('driver.insert_time', 'desc');

        // 组装计数项
        if ($param->ad2_common_log_target) {

            $ad2_common_log->selectRaw("{$table['material_file_log']}.id as material_file_id");

            foreach ($param->ad2_common_log_target as $target) {
                $ad2_common_log->selectRaw($target);
            }

            $ad2_common_log->join($table['ad3_common_log'], function (JoinClause $join) use ($param, $table) {
                $join->on("{$table['ad3_common_log']}.ad2_id", '=', "{$table['ad2_common_log']}.ad2_id");
                $join->on("{$table['ad3_common_log']}.media_type", '=', "{$table['ad2_common_log']}.media_type");
                $join->on("{$table['ad3_common_log']}.platform", '=', "{$table['ad2_common_log']}.platform");

                $join->where("{$table['ad3_common_log']}.media_type", '=', $param->media_type);
                $join->where("{$table['ad3_common_log']}.platform", '=', $param->platform);
                $param->ad2_create_time && $join->whereBetween("{$table['ad2_common_log']}.ad2_create_time", $param->ad2_create_time);
            });

            $ad2_common_log->join($table['material_file_log'], function (JoinClause $join) use ($param, $table) {
                $join->on("{$table['material_file_log']}.signature", '=', "{$table['ad3_common_log']}.signature");
            });

            if ($param->need_join_material_log['material_file_log'] ?? false) {
                $ad2_common_log->join($table['material_log'], function (JoinClause $join) use ($param, $table) {
                    $join->on("{$table['material_file_log']}.material_id", '=', "{$table['material_log']}.material_id");
                    $join->on("{$table['material_file_log']}.platform", '=', "{$table['material_log']}.platform");

                    $join->where("{$table['material_file_log']}.platform", '=', $param->material_create_platform);
                });
            }

            $this->injectAgentPermissionAndJoinAgentSite($ad2_common_log, $agent_permission, $table['ad2_common_log'], 'left');
            $this->injectGamePermissionAndJoinSiteGame($ad2_common_log, $game_permission, $table['ad2_common_log'], 'left');

            foreach ($param->ad2_common_log_filter as [$condition, $value]) {
                $ad2_common_log->whereRaw($condition, $value);
            }

            $ad2_common_log->groupBy(["material_file_id"]);

            $main_builder->withExpression('ad2_common_log', $ad2_common_log);
            $main_builder->leftJoin('ad2_common_log', function (JoinClause $join) use ($param) {
                $join->on('driver.material_file_id', '=', 'ad2_common_log.material_file_id');
            });
        }

        // 组装小时表
        if (!empty($param->hour_data_log_target)) {

            $param->hour_data_log_target[] = ADComposeFilterSqlMap::FORMULA['hour_data_log']['sum_cost'];
            $param->main_target[] = 'sum_cost';
            $param->hour_data_log_target = array_unique($param->hour_data_log_target);
            $param->main_target = array_unique($param->main_target);

            $hour_data_log->selectRaw("{$table['material_file_log']}.id as material_file_id");

            foreach ($param->hour_data_log_target as $key => $value) {
                $hour_data_log->selectRaw($value);
            }

            $this->injectAgentPermissionAndJoinAgentSite($hour_data_log, $agent_permission, $table['hour_data_log']);
            $this->injectGamePermissionAndJoinGame($hour_data_log, $game_permission, $table['hour_data_log']);

            $hour_data_log->join($table['ad3_common_log'], function (JoinClause $join) use ($param, $table) {
                $join->on("{$table['hour_data_log']}.ad4_id", '=', "{$table['ad3_common_log']}.ad4_id");

                $join->where("{$table['ad3_common_log']}.media_type", '=', $param->media_type);
                $join->where("{$table['ad3_common_log']}.platform", '=', $param->platform);
            });

            $hour_data_log->join($table['material_file_log'], function (JoinClause $join) use ($param, $table) {
                $join->on("{$table['ad3_common_log']}.signature", '=', "{$table['material_file_log']}.signature");
            });

            if ($param->need_join_material_log['material_file_log'] ?? false) {
                $hour_data_log->join($table['material_log'], function (JoinClause $join) use ($param, $table) {
                    $join->on("{$table['material_file_log']}.material_id", '=', "{$table['material_log']}.material_id");
                    $join->on("{$table['material_file_log']}.platform", '=', "{$table['material_log']}.platform");

                    $join->where("{$table['material_file_log']}.platform", '=', $param->material_create_platform);
                });
            }

            if ($param->ad2_create_time || ($param->need_join_ad2_common_log['material_file_log'] ?? false)) {
                $hour_data_log->join($table['ad2_common_log'], function (JoinClause $join) use ($param, $table) {
                    $join->on("{$table['ad3_common_log']}.ad2_id", '=', "{$table['ad2_common_log']}.ad2_id");
                    $join->on("{$table['ad3_common_log']}.media_type", '=', "{$table['ad2_common_log']}.media_type");
                    $join->on("{$table['ad3_common_log']}.platform", '=', "{$table['ad2_common_log']}.platform");

                    $param->ad2_create_time && $join->whereBetween("{$table['ad2_common_log']}.ad2_create_time", $param->ad2_create_time);
                });
            }


            $hour_data_log->whereBetween("cost_date", $param->cost_date);

            foreach ($param->hour_data_log_filter as [$condition, $value]) {
                $hour_data_log->whereRaw($condition, $value);
            }

            $hour_data_log->groupBy(["material_file_id"]);

            $main_builder->withExpression('hour_data_log', $hour_data_log);

            $main_builder->leftJoin('hour_data_log', function (JoinClause $join) use ($param) {
                $join->on('driver.material_file_id', '=', 'hour_data_log.material_file_id');
            });

            $main_builder->orderBy('sum_cost', 'desc');
        }

        // 组装业务总览表
        if (!empty($param->overview_log_target)) {

            $overview_log->selectRaw("{$table['material_file_log']}.id as material_file_id");

            foreach ($param->overview_log_target as $key => $value) {
                if ($param->filter_game_mode == 'root_game') {
                    $overview_log->selectRaw(str_replace('v3_ads_day_root_game_back_overview_log', 'v3_ads_day_root_game_overview_log', $value));
                } else if ($param->filter_game_mode == 'reflux') {
                    $overview_log->selectRaw(str_replace('v3_ads_day_root_game_back_overview_log', 'v3_ads_day_root_game_back_overview_log', $value));
                } else {
                    $overview_log->selectRaw($value);
                }
            }

            $this->injectAgentPermissionAndJoinAgentSite($overview_log, $agent_permission, $table['overview_log']);
            $this->injectGamePermissionAndJoinGame($overview_log, $game_permission, $table['overview_log']);

            $overview_log->join($table['ad3_common_log'], function (JoinClause $join) use ($param, $table) {
                $join->on("{$table['overview_log']}.ad4_id", '=', "{$table['ad3_common_log']}.ad4_id");
//                $join->on("{$table['overview_log']}.media_type", '=', "{$table['ad3_common_log']}.media_type");
                $join->on("{$this->v2_dim_agent_site_id_alias}.media_type_id", '=', "{$table['ad3_common_log']}.media_type");
                $join->on("{$table['overview_log']}.platform", '=', "{$table['ad3_common_log']}.platform");

                $join->where("{$table['ad3_common_log']}.media_type", '=', $param->media_type);
                $join->where("{$table['ad3_common_log']}.platform", '=', $param->platform);
                $join->where("{$table['ad3_common_log']}.ad4_id", '>', 0);
            });

            $overview_log->join($table['material_file_log'], function (JoinClause $join) use ($param, $table) {
                $join->on("{$table['ad3_common_log']}.signature", '=', "{$table['material_file_log']}.signature");
            });

            if ($param->need_join_material_log['material_file_log'] ?? false) {
                $overview_log->join($table['material_log'], function (JoinClause $join) use ($param, $table) {
                    $join->on("{$table['material_file_log']}.material_id", '=', "{$table['material_log']}.material_id");
                    $join->on("{$table['material_file_log']}.platform", '=', "{$table['material_log']}.platform");

                    $join->where("{$table['material_file_log']}.platform", '=', $param->material_create_platform);
                });
            }

            if ($param->ad2_create_time || $param->need_join_ad2_common_log) {
                $overview_log->join($table['ad2_common_log'], function (JoinClause $join) use ($param, $table) {
                    $join->on("{$table['ad3_common_log']}.ad2_id", '=', "{$table['ad2_common_log']}.ad2_id");
                    $join->on("{$table['ad3_common_log']}.media_type", '=', "{$table['ad2_common_log']}.media_type");
                    $join->on("{$table['ad3_common_log']}.platform", '=', "{$table['ad2_common_log']}.platform");

                    $param->ad2_create_time && $join->whereBetween("{$table['ad2_common_log']}.ad2_create_time", $param->ad2_create_time);
                });
            }

            $overview_log->whereBetween("log_date", $param->cost_date);

            foreach ($param->overview_log_filter as [$condition, $value]) {
                if ($param->filter_game_mode == 'root_game') {
                    $overview_log->whereRaw(str_replace('v3_ads_day_root_game_back_overview_log', 'v3_ads_day_root_game_overview_log', $condition), $value);
                } else if ($param->filter_game_mode == 'reflux') {
                    $overview_log->whereRaw(str_replace('v3_ads_day_root_game_back_overview_log', 'v3_ads_day_root_game_back_overview_log', $condition), $value);
                } else {
                    $overview_log->whereRaw($condition, $value);
                }
            }

            $overview_log->groupBy(["material_file_id"]);

            $main_builder->withExpression('overview_log', $overview_log);

            $main_builder->leftJoin('overview_log', function (JoinClause $join) use ($param) {
                $join->on('driver.material_file_id', '=', 'overview_log.material_file_id');
            });
        }

        // 组装用户注册表
        if (!empty($param->reg_log_target)) {

            $param->reg_log_target[] = ADComposeFilterSqlMap::FORMULA['reg_log']['reg_uid_count'];
            $param->main_target[] = 'reg_uid_count';
            $param->reg_log_target = array_unique($param->reg_log_target);
            $param->main_target = array_unique($param->main_target);

            $reg_log->selectRaw("{$table['material_file_log']}.id as material_file_id");

            foreach ($param->reg_log_target as $key => $value) {
                $reg_log->selectRaw($value);
            }

            $this->injectAgentPermissionAndJoinAgentSite($reg_log, $agent_permission, $table['reg_log']);
            $this->injectGamePermissionAndJoinGame($reg_log, $game_permission, $table['reg_log']);

            $reg_log->join($table['ad3_common_log'], function (JoinClause $join) use ($param, $table) {
                $join->on("{$table['reg_log']}.ad4_id", '=', "{$table['ad3_common_log']}.ad4_id");
                $join->on("{$table['reg_log']}.platform", '=', "{$table['ad3_common_log']}.platform");
//                $join->on("{$table['reg_log']}.ad_platform_id", '=', "{$table['ad3_common_log']}.media_type");
                $join->on("{$this->v2_dim_agent_site_id_alias}.media_type_id", '=', "{$table['ad3_common_log']}.media_type");

                $join->where("{$table['ad3_common_log']}.media_type", '=', $param->media_type);
                $join->where("{$table['ad3_common_log']}.platform", '=', $param->platform);
            });

            $reg_log->join($table['material_file_log'], function (JoinClause $join) use ($param, $table) {
                $join->on("{$table['ad3_common_log']}.signature", '=', "{$table['material_file_log']}.signature");
            });

            if ($param->need_join_material_log['material_file_log'] ?? false) {
                $reg_log->join($table['material_log'], function (JoinClause $join) use ($param, $table) {
                    $join->on("{$table['material_file_log']}.material_id", '=', "{$table['material_log']}.material_id");
                    $join->on("{$table['material_file_log']}.platform", '=', "{$table['material_log']}.platform");

                    $join->where("{$table['material_file_log']}.platform", '=', $param->material_create_platform);
                });
            }

            if ($param->ad2_create_time || $param->need_join_ad2_common_log) {
                $reg_log->join($table['ad2_common_log'], function (JoinClause $join) use ($param, $table) {
                    $join->on("{$table['ad3_common_log']}.ad2_id", '=', "{$table['ad2_common_log']}.ad2_id");
                    $join->on("{$table['ad3_common_log']}.media_type", '=', "{$table['ad2_common_log']}.media_type");
                    $join->on("{$table['ad3_common_log']}.platform", '=', "{$table['ad2_common_log']}.platform");

                    $param->ad2_create_time && $join->whereBetween("{$table['ad2_common_log']}.ad2_create_time", $param->ad2_create_time);
                });
            }

            $reg_log->whereBetween("game_reg_time", $param->cost_date);

            foreach ($param->reg_log_filter as [$condition, $value]) {
                $reg_log->whereRaw($condition, $value);
            }

            $reg_log->groupBy(["material_file_id"]);

            $main_builder->withExpression('reg_log', $reg_log);

            $main_builder->leftJoin('reg_log', function (JoinClause $join) use ($param) {
                $join->on('driver.material_file_id', '=', 'reg_log.material_file_id');
            });

            $main_builder->orderBy('reg_uid_count', 'desc');
        }

        // tx3.0
        if ($param->tx_component_day_data_log_target) {
            $tx_c_day_data_log_builder = $this->builder->from($media_table['tx_component_day_data_log']);
            $tx_c_day_data_log_builder->selectRaw("{$table['material_file_log']}.id as material_file_id");
            foreach ($param->tx_component_day_data_log_target as $value) {
                $tx_c_day_data_log_builder->selectRaw($value);
            }
            $this->injectAgentPermissionAndJoinAgentSite($tx_c_day_data_log_builder, $agent_permission, $media_table['tx_component_day_data_log']);
            $this->injectGamePermissionAndJoinGame($tx_c_day_data_log_builder, $game_permission, $media_table['tx_component_day_data_log']);


            $tx_c_day_data_log_builder->join($table['material_file_log'], function (JoinClause $join) use ($param, $table, $media_table) {
                $join->on("{$media_table['tx_component_day_data_log']}.signature", '=', "{$table['material_file_log']}.signature");
            });

            if ($param->need_join_material_log['material_file_log'] ?? false) {
                $tx_c_day_data_log_builder->join($table['material_log'], function (JoinClause $join) use ($param, $table) {
                    $join->on("{$table['material_file_log']}.material_id", '=', "{$table['material_log']}.material_id");
                    $join->on("{$table['material_file_log']}.platform", '=', "{$table['material_log']}.platform");

                    $join->where("{$table['material_file_log']}.platform", '=', $param->material_create_platform);
                });
            }

            if ($param->ad2_create_time || $param->need_join_ad2_common_log) {
                $tx_c_day_data_log_builder->join($table['ad2_common_log'], function (JoinClause $join) use ($media_table, $param, $table) {
                    $join->on("{$media_table['tx_component_day_data_log']}.adgroup_id", '=', "{$table['ad2_common_log']}.ad2_id");
                    $join->on("{$media_table['tx_component_day_data_log']}.platform", '=', "{$table['ad2_common_log']}.platform");

                    $param->ad2_create_time && $join->whereBetween("{$table['ad2_common_log']}.ad2_create_time", $param->ad2_create_time);
                });
            }

            $tx_c_day_data_log_builder->whereBetween("cost_date", $param->cost_date);

            foreach ($param->tx_component_day_data_log_filter as [$condition, $value]) {
                $tx_c_day_data_log_builder->whereRaw($condition, $value);
            }

            $tx_c_day_data_log_builder->groupBy(["material_file_id"]);

            $main_builder->withExpression('tx_component_day_data_log', $tx_c_day_data_log_builder);

            $main_builder->leftJoin('tx_component_day_data_log', function (JoinClause $join) use ($param) {
                $join->on("driver.material_file_id", '=', "tx_component_day_data_log.material_file_id");
            });

        }

        // 组装特定媒体特定字段
        $media_hour_data_log = $this->getMediaHourDataLogTargetAndFilter($param);
        if ($media_hour_data_log) {

            $media_hour_data_log_alias = $media_hour_data_log['alias'];
            $media_hour_data_log_table = $media_hour_data_log['table'];
            $media_hour_data_log_builder = $media_hour_data_log['builder'];
            $media_hour_data_log_target = $media_hour_data_log['target'];
            $media_hour_data_log_filter = $media_hour_data_log['filter'];

            if (!empty($media_hour_data_log_target)) {

                $media_hour_data_log_builder->selectRaw("{$table['material_file_log']}.id as material_file_id");

                foreach ($media_hour_data_log_target as $key => $value) {
                    $media_hour_data_log_builder->selectRaw($value);
                }

                $this->injectAgentPermissionAndJoinAgentSite($media_hour_data_log_builder, $agent_permission, $media_hour_data_log_table);
                $this->injectGamePermissionAndJoinGame($media_hour_data_log_builder, $game_permission, $media_hour_data_log_table);

                $media_hour_data_log_builder->join($table['ad3_common_log'], function (JoinClause $join) use ($param, $table, $media_hour_data_log_table) {
                    $join->on("{$media_hour_data_log_table}.creative_id", '=', "{$table['ad3_common_log']}.ad4_id");

                    $join->where("{$table['ad3_common_log']}.media_type", '=', $param->media_type);
                    $join->where("{$table['ad3_common_log']}.platform", '=', $param->platform);
                });

                $media_hour_data_log_builder->join($table['material_file_log'], function (JoinClause $join) use ($param, $table) {
                    $join->on("{$table['ad3_common_log']}.signature", '=', "{$table['material_file_log']}.signature");
                });

                if ($param->need_join_material_log['material_file_log'] ?? false) {
                    $media_hour_data_log_builder->join($table['material_log'], function (JoinClause $join) use ($param, $table) {
                        $join->on("{$table['material_file_log']}.material_id", '=', "{$table['material_log']}.material_id");
                        $join->on("{$table['material_file_log']}.platform", '=', "{$table['material_log']}.platform");

                        $join->where("{$table['material_file_log']}.platform", '=', $param->material_create_platform);
                    });
                }

                if ($param->ad2_create_time || $param->need_join_ad2_common_log) {
                    $media_hour_data_log_builder->join($table['ad2_common_log'], function (JoinClause $join) use ($param, $table) {
                        $join->on("{$table['ad3_common_log']}.ad2_id", '=', "{$table['ad2_common_log']}.ad2_id");
                        $join->on("{$table['ad3_common_log']}.media_type", '=', "{$table['ad2_common_log']}.media_type");
                        $join->on("{$table['ad3_common_log']}.platform", '=', "{$table['ad2_common_log']}.platform");

                        $param->ad2_create_time && $join->whereBetween("{$table['ad2_common_log']}.ad2_create_time", $param->ad2_create_time);
                    });
                }

                $media_hour_data_log_builder->whereBetween("cost_date", $param->cost_date);

                foreach ($media_hour_data_log_filter as [$condition, $value]) {
                    $media_hour_data_log_builder->whereRaw($condition, $value);
                }

                $media_hour_data_log_builder->groupBy(["material_file_id"]);

                $main_builder->withExpression($media_hour_data_log_alias, $media_hour_data_log_builder);

                $main_builder->leftJoin($media_hour_data_log_alias, function (JoinClause $join) use ($param, $media_hour_data_log_alias) {
                    $join->on("driver.material_file_id", '=', "{$media_hour_data_log_alias}.material_file_id");
                });
            }
        }

        $main_builder->selectRaw("driver.filename as filename");
        $main_builder->selectRaw("driver.material_file_id as id");
        $main_builder->selectRaw("driver.material_id as material_id");
        $main_builder->selectRaw("driver.platform as platform");
        $main_builder->selectRaw("driver.file_type as file_type");
        $main_builder->selectRaw("driver.url as url");
        $main_builder->selectRaw("driver.width as width");
        $main_builder->selectRaw("driver.height as height");
        $main_builder->selectRaw("driver.uploader as uploader");
        $main_builder->selectRaw("driver.insert_time as create_time");
        $main_builder->selectRaw("driver.duration as duration");
        $main_builder->selectRaw("driver.bitrate as bitrate");
        $main_builder->selectRaw("driver.size as size");
        $main_builder->selectRaw("driver.notify as notify");

        foreach ($param->main_target as $key => $value) {
            $main_builder->selectRaw($value);
        }

        foreach ($param->main_calc as [$condition, $value]) {
            !is_null($value) && $main_builder->havingRaw($condition, (array)$value, $param->condition);
        }

//        foreach ($param->order as $order_key => $order_value) {
//            $main_builder->orderBy($order_value['column'], $order_value['direction']);
//        }

        $main_builder->limit($param->limit);

        $list_data = $main_builder->get();
        foreach ($list_data as $file_info) {
            if ($file_info->notify == 1 && $file_info->file_type == 2) {
                $video_name = basename($file_info->url);
                $file_info->url = EnvConfig::DOMAIN . '/' . EnvConfig::UPLOAD_PATH . '/' . EnvConfig::MATERIAL_VIDEO_DIR_NAME . '/' . $file_info->platform . '/' . $file_info->material_id . '/' . $video_name;
            }
        }

        return [
            'list' => $list_data,
            'sql' => $this->getSql($main_builder)
        ];
    }

    /**
     * 文案报表筛选
     * @param ADComposeFilterParam $param
     * @param int $agent_permission
     * @param int $game_permission
     * @return array
     */
    public function getWordFilter(ADComposeFilterParam $param, $agent_permission = -1, $game_permission = -1)
    {
        $table = ADComposeFilterSqlMap::TABLE;

        $driver_builder = $this->builder->from($table['ad3_common_log']);
        $ad2_common_log = $this->builder->from($table['ad2_common_log']);
        $hour_data_log = $this->builder->from($table['hour_data_log']);
        $overview_log = $this->builder->from($table['overview_log']);
        $overview_log->where("{$table['overview_log']}.ad2_id", '>', 0);
        $reg_log = $this->builder->from($table['reg_log']);
        $reg_log->where("{$table['reg_log']}.action_id", '=', 2);
        $reg_log->where("{$table['reg_log']}.adgroup_id ", '!=', '');
        $main_builder = $this->builder->from('driver');

        // 组装驱动表
        $driver_builder->selectRaw("{$table['ad3_common_log']}.platform");
        $driver_builder->selectRaw("dwd_media_ad3_common_log.title as title");
        $driver_builder->selectRaw("dwd_media_ad3_common_log.creative_create_time");

        $driver_builder->join($table['ad2_common_log'], function (JoinClause $join) use ($param, $table) {
            $join->on("{$table['ad3_common_log']}.ad2_id", '=', "{$table['ad2_common_log']}.ad2_id");
            $join->on("{$table['ad3_common_log']}.media_type", '=', "{$table['ad2_common_log']}.media_type");
            $join->on("{$table['ad3_common_log']}.platform", '=', "{$table['ad2_common_log']}.platform");

            $join->where("{$table['ad3_common_log']}.media_type", '=', $param->media_type);
            $join->where("{$table['ad3_common_log']}.platform", '=', $param->platform);
        });

        $driver_builder->join('tanwan_datamedia.dwd_media_ad3_common_log', function (JoinClause $join) use ($param, $table) {
            $join->on("{$table['ad3_common_log']}.ad3_id", '=', "tanwan_datamedia.dwd_media_ad3_common_log.ad3_id");
            $join->on("{$table['ad3_common_log']}.media_type", '=', "tanwan_datamedia.dwd_media_ad3_common_log.media_type");
            $join->on("{$table['ad3_common_log']}.platform", '=', "tanwan_datamedia.dwd_media_ad3_common_log.platform");

        });

        $this->injectAgentPermissionAndJoinAgentSite($driver_builder, $agent_permission, $table['ad2_common_log']);
        $this->injectGamePermissionAndJoinSiteGame($driver_builder, $game_permission, $table['ad2_common_log']);

        if ($param->filter_mode == 0) {
            // 物料筛选模式
            foreach ($param->ad3_common_log_filter as [$condition, $value]) {
                if (strpos($condition, 'title') !== false) {
                    $driver_builder->whereRaw($condition, $value);
                }
            }
        } else {
            // 全数据筛选模式
            if ($param->need_join_material_file_log['ad3_common_log'] ?? false) {
                $driver_builder->leftJoin($table['material_file_log'], function (JoinClause $join) use ($param, $table) {
                    $join->on("{$table['material_file_log']}.signature", '=', "{$table['ad3_common_log']}.signature");
                });
            }

            foreach ($param->ad3_common_log_filter as [$condition, $value]) {
                $driver_builder->whereRaw($condition, $value);
            }
        }

        $driver_builder->groupBy("dwd_media_ad3_common_log.title");

        $main_builder->withExpression('driver', $driver_builder);

//        $main_builder->orderBy('driver.creative_create_time', 'desc');

        // 组装计数项
        if ($param->ad2_common_log_target) {

            $ad2_common_log->selectRaw("dwd_media_ad3_common_log.title as title");

            foreach ($param->ad2_common_log_target as $target) {
                $ad2_common_log->selectRaw($target);
            }

            $ad2_common_log->join($table['ad3_common_log'], function (JoinClause $join) use ($param, $table) {
                $join->on("{$table['ad3_common_log']}.ad2_id", '=', "{$table['ad2_common_log']}.ad2_id");
                $join->on("{$table['ad3_common_log']}.media_type", '=', "{$table['ad2_common_log']}.media_type");
                $join->on("{$table['ad3_common_log']}.platform", '=', "{$table['ad2_common_log']}.platform");

                $join->where("{$table['ad3_common_log']}.media_type", '=', $param->media_type);
                $join->where("{$table['ad3_common_log']}.platform", '=', $param->platform);
            });

            $ad2_common_log->join('tanwan_datamedia.dwd_media_ad3_common_log', function (JoinClause $join) use ($param, $table) {
                $join->on("{$table['ad3_common_log']}.ad3_id", '=', "tanwan_datamedia.dwd_media_ad3_common_log.ad3_id");
                $join->on("{$table['ad3_common_log']}.media_type", '=', "tanwan_datamedia.dwd_media_ad3_common_log.media_type");
                $join->on("{$table['ad3_common_log']}.platform", '=', "tanwan_datamedia.dwd_media_ad3_common_log.platform");

            });

            if ($param->need_join_material_file_log['ad3_common_log'] ?? false) {
                $ad2_common_log->join($table['material_file_log'], function (JoinClause $join) use ($param, $table) {
                    $join->on("{$table['ad3_common_log']}.signature", '=', "{$table['material_file_log']}.signature");
                });
            }

            $this->injectAgentPermissionAndJoinAgentSite($ad2_common_log, $agent_permission, $table['ad2_common_log']);
            $this->injectGamePermissionAndJoinSiteGame($ad2_common_log, $game_permission, $table['ad2_common_log']);

            $param->ad2_create_time && $ad2_common_log->whereBetween("{$table['ad2_common_log']}.ad2_create_time", $param->ad2_create_time);

            foreach ($param->ad2_common_log_filter as [$condition, $value]) {
                $ad2_common_log->whereRaw($condition, $value);
            }

            $ad2_common_log->groupBy(["title"]);

            $main_builder->withExpression('ad2_common_log', $ad2_common_log);

            $main_builder->leftJoin('ad2_common_log', function (JoinClause $join) use ($param) {
                $join->on('driver.title', '=', 'ad2_common_log.title');
            });
        }

        // 组装小时表
        if (!empty($param->hour_data_log_target)) {

            $param->hour_data_log_target[] = ADComposeFilterSqlMap::FORMULA['hour_data_log']['sum_cost'];
            $param->main_target[] = 'sum_cost';
            $param->hour_data_log_target = array_unique($param->hour_data_log_target);
            $param->main_target = array_unique($param->main_target);

            $hour_data_log->selectRaw("dwd_media_ad3_common_log.title as title");

            foreach ($param->hour_data_log_target as $key => $value) {
                $hour_data_log->selectRaw($value);
            }

            $this->injectAgentPermissionAndJoinAgentSite($hour_data_log, $agent_permission, $table['hour_data_log']);
            $this->injectGamePermissionAndJoinGame($hour_data_log, $game_permission, $table['hour_data_log']);

            $hour_data_log->join($table['ad3_common_log'], function (JoinClause $join) use ($param, $table) {
                $join->on("{$table['ad3_common_log']}.ad4_id", '=', "{$table['hour_data_log']}.ad4_id");
                $join->on("{$table['ad3_common_log']}.media_type", '=', "{$table['hour_data_log']}.media_type");
                $join->on("{$table['ad3_common_log']}.platform", '=', "{$table['hour_data_log']}.platform");


                $join->where("{$table['ad3_common_log']}.media_type", '=', $param->media_type);
                $join->where("{$table['ad3_common_log']}.platform", '=', $param->platform);
            });

            $hour_data_log->join('tanwan_datamedia.dwd_media_ad3_common_log', function (JoinClause $join) use ($param, $table) {
                $join->on("{$table['ad3_common_log']}.ad3_id", '=', "tanwan_datamedia.dwd_media_ad3_common_log.ad3_id");
                $join->on("{$table['ad3_common_log']}.media_type", '=', "tanwan_datamedia.dwd_media_ad3_common_log.media_type");
                $join->on("{$table['ad3_common_log']}.platform", '=', "tanwan_datamedia.dwd_media_ad3_common_log.platform");

            });

            if ($param->need_join_material_file_log['ad3_common_log'] ?? false) {
                $hour_data_log->join($table['material_file_log'], function (JoinClause $join) use ($param, $table) {
                    $join->on("{$table['ad3_common_log']}.signature", '=', "{$table['material_file_log']}.signature");
                });
            }

            if ($param->ad2_create_time || ($param->need_join_ad2_common_log['ad3_common_log'] ?? false)) {
                $hour_data_log->join($table['ad2_common_log'], function (JoinClause $join) use ($param, $table) {
                    $join->on("{$table['ad3_common_log']}.ad2_id", '=', "{$table['ad2_common_log']}.ad2_id");
                    $join->on("{$table['ad3_common_log']}.media_type", '=', "{$table['ad2_common_log']}.media_type");
                    $join->on("{$table['ad3_common_log']}.platform", '=', "{$table['ad2_common_log']}.platform");

                    $param->ad2_create_time && $join->whereBetween("{$table['ad2_common_log']}.ad2_create_time", $param->ad2_create_time);
                });
            }

            $hour_data_log->whereBetween("cost_date", $param->cost_date);

            foreach ($param->hour_data_log_filter as [$condition, $value]) {
                $hour_data_log->whereRaw($condition, $value);
            }

            $hour_data_log->groupBy(["title"]);

            $main_builder->withExpression('hour_data_log', $hour_data_log);

            $main_builder->leftJoin('hour_data_log', function (JoinClause $join) use ($param) {
                $join->on('driver.title', '=', 'hour_data_log.title');
            });

            $main_builder->orderBy('sum_cost', 'desc');
        }

        // 组装业务总览表
        if (!empty($param->overview_log_target)) {

            $overview_log->selectRaw("dwd_media_ad3_common_log.title as title");

            foreach ($param->overview_log_target as $key => $value) {
                $overview_log->selectRaw($value);
            }
            $this->injectAgentPermissionAndJoinAgentSite($overview_log, $agent_permission, $table['overview_log']);
            $this->injectGamePermissionAndJoinGame($overview_log, $game_permission, $table['overview_log']);

            $overview_log->join($table['ad3_common_log'], function (JoinClause $join) use ($param, $table) {
                $join->on("{$table['overview_log']}.ad4_id", '=', "{$table['ad3_common_log']}.ad4_id");
//                $join->on("{$table['overview_log']}.media_type", '=', "{$table['ad3_common_log']}.media_type");
                $join->on("{$this->v2_dim_agent_site_id_alias}.media_type_id", '=', "{$table['ad3_common_log']}.media_type");
                $join->on("{$table['overview_log']}.platform", '=', "{$table['ad3_common_log']}.platform");

                $join->where("{$table['ad3_common_log']}.media_type", '=', $param->media_type);
                $join->where("{$table['ad3_common_log']}.platform", '=', $param->platform);
            });

            $overview_log->join('tanwan_datamedia.dwd_media_ad3_common_log', function (JoinClause $join) use ($param, $table) {
                $join->on("{$table['ad3_common_log']}.ad3_id", '=', "tanwan_datamedia.dwd_media_ad3_common_log.ad3_id");
                $join->on("{$table['ad3_common_log']}.media_type", '=', "tanwan_datamedia.dwd_media_ad3_common_log.media_type");
                $join->on("{$table['ad3_common_log']}.platform", '=', "tanwan_datamedia.dwd_media_ad3_common_log.platform");

            });

            if ($param->need_join_material_file_log['ad3_common_log'] ?? false) {
                $overview_log->join($table['material_file_log'], function (JoinClause $join) use ($param, $table) {
                    $join->on("{$table['ad3_common_log']}.signature", '=', "{$table['material_file_log']}.signature");
                });
            }

            if ($param->ad2_create_time || ($param->need_join_ad2_common_log['overview_log'] ?? false)) {
                $overview_log->join($table['ad2_common_log'], function (JoinClause $join) use ($param, $table) {
                    $join->on("{$table['ad3_common_log']}.ad2_id", '=', "{$table['ad2_common_log']}.ad2_id");
                    $join->on("{$table['ad3_common_log']}.media_type", '=', "{$table['ad2_common_log']}.media_type");
                    $join->on("{$table['ad3_common_log']}.platform", '=', "{$table['ad2_common_log']}.platform");

                    $param->ad2_create_time && $join->whereBetween("{$table['ad2_common_log']}.ad2_create_time", $param->ad2_create_time);
                });
            }

            $overview_log->whereBetween("log_date", $param->cost_date);

            foreach ($param->overview_log_filter as [$condition, $value]) {
                $overview_log->whereRaw($condition, $value);
            }

            $overview_log->groupBy(["title"]);

            $main_builder->withExpression('overview_log', $overview_log);

            $main_builder->leftJoin('overview_log', function (JoinClause $join) use ($param) {
                $join->on('driver.title', '=', 'overview_log.title');
            });
        }

        // 组装用户注册表
        if (!empty($param->reg_log_target)) {

            $param->reg_log_target[] = ADComposeFilterSqlMap::FORMULA['reg_log']['reg_uid_count'];
            $param->main_target[] = 'reg_uid_count';
            $param->reg_log_target = array_unique($param->reg_log_target);
            $param->main_target = array_unique($param->main_target);

            $reg_log->selectRaw("dwd_media_ad3_common_log.title as title");

            foreach ($param->reg_log_target as $key => $value) {
                $reg_log->selectRaw($value);
            }

            $this->injectAgentPermissionAndJoinAgentSite($reg_log, $agent_permission, $table['reg_log']);
            $this->injectGamePermissionAndJoinGame($reg_log, $game_permission, $table['reg_log']);

            $reg_log->join($table['ad3_common_log'], function (JoinClause $join) use ($param, $table) {
                $join->on("{$table['reg_log']}.ad4_id", '=', "{$table['ad3_common_log']}.ad4_id");
                $join->on("{$table['reg_log']}.platform", '=', "{$table['ad3_common_log']}.platform");
//                $join->on("{$table['reg_log']}.ad_platform_id", '=', "{$table['ad3_common_log']}.platform");
                $join->on("{$this->v2_dim_agent_site_id_alias}.media_type_id", '=', "{$table['ad3_common_log']}.media_type");

                $join->where("{$table['ad3_common_log']}.media_type", '=', $param->media_type);
                $join->where("{$table['ad3_common_log']}.platform", '=', $param->platform);
            });

            $reg_log->join('tanwan_datamedia.dwd_media_ad3_common_log', function (JoinClause $join) use ($param, $table) {
                $join->on("{$table['ad3_common_log']}.ad3_id", '=', "tanwan_datamedia.dwd_media_ad3_common_log.ad3_id");
                $join->on("{$table['ad3_common_log']}.media_type", '=', "tanwan_datamedia.dwd_media_ad3_common_log.media_type");
                $join->on("{$table['ad3_common_log']}.platform", '=', "tanwan_datamedia.dwd_media_ad3_common_log.platform");

            });

            if ($param->need_join_material_file_log['ad3_common_log'] ?? false) {
                $reg_log->join($table['material_file_log'], function (JoinClause $join) use ($param, $table) {
                    $join->on("{$table['ad3_common_log']}.signature", '=', "{$table['material_file_log']}.signature");
                });
            }

            if ($param->ad2_create_time || ($param->need_join_ad2_common_log['reg_log'] ?? false)) {
                $reg_log->join($table['ad2_common_log'], function (JoinClause $join) use ($param, $table) {
                    $join->on("{$table['ad3_common_log']}.ad2_id", '=', "{$table['ad2_common_log']}.ad2_id");
                    $join->on("{$table['ad3_common_log']}.media_type", '=', "{$table['ad2_common_log']}.media_type");
                    $join->on("{$table['ad3_common_log']}.platform", '=', "{$table['ad2_common_log']}.platform");

                    $param->ad2_create_time && $join->whereBetween("{$table['ad2_common_log']}.ad2_create_time", $param->ad2_create_time);
                });
            }

            $reg_log->whereBetween("game_reg_time ", $param->cost_date);

            foreach ($param->reg_log_filter as [$condition, $value]) {
                $reg_log->whereRaw($condition, $value);
            }

            $reg_log->groupBy(['title']);

            $main_builder->withExpression('reg_log', $reg_log);

            $main_builder->leftJoin('reg_log', function (JoinClause $join) use ($param) {
                $join->on('driver.title', '=', 'reg_log.title');
            });

            $main_builder->orderBy('reg_uid_count', 'desc');
        }

        // 组装特定媒体特定字段
        $media_hour_data_log = $this->getMediaHourDataLogTargetAndFilter($param);
        if ($media_hour_data_log) {

            $media_hour_data_log_alias = $media_hour_data_log['alias'];
            $media_hour_data_log_table = $media_hour_data_log['table'];
            $media_hour_data_log_builder = $media_hour_data_log['builder'];
            $media_hour_data_log_target = $media_hour_data_log['target'];
            $media_hour_data_log_filter = $media_hour_data_log['filter'];

            if (!empty($media_hour_data_log_target)) {

                $media_hour_data_log_builder->selectRaw("dwd_media_ad3_common_log.title as title");

                foreach ($media_hour_data_log_target as $key => $value) {
                    $media_hour_data_log_builder->selectRaw($value);
                }

                $this->injectAgentPermissionAndJoinAgentSite($media_hour_data_log_builder, $agent_permission, $media_hour_data_log_table);
                $this->injectGamePermissionAndJoinGame($media_hour_data_log_builder, $game_permission, $media_hour_data_log_table);

                $media_hour_data_log_builder->join($table['ad3_common_log'], function (JoinClause $join) use ($param, $table, $media_hour_data_log_table) {
                    $join->on("{$table['ad3_common_log']}.ad4_id", '=', "{$media_hour_data_log_table}.creative_id");
                    $join->on("{$table['ad3_common_log']}.platform", '=', "{$media_hour_data_log_table}.platform");


                    $join->where("{$table['ad3_common_log']}.media_type", '=', $param->media_type);
                    $join->where("{$table['ad3_common_log']}.platform", '=', $param->platform);
                });

                $media_hour_data_log_builder->join('tanwan_datamedia.dwd_media_ad3_common_log', function (JoinClause $join) use ($param, $table) {
                    $join->on("{$table['ad3_common_log']}.ad3_id", '=', "tanwan_datamedia.dwd_media_ad3_common_log.ad3_id");
                    $join->on("{$table['ad3_common_log']}.media_type", '=', "tanwan_datamedia.dwd_media_ad3_common_log.media_type");
                    $join->on("{$table['ad3_common_log']}.platform", '=', "tanwan_datamedia.dwd_media_ad3_common_log.platform");

                });

                if ($param->need_join_material_file_log['ad3_common_log'] ?? false) {
                    $media_hour_data_log_builder->join($table['material_file_log'], function (JoinClause $join) use ($param, $table) {
                        $join->on("{$table['ad3_common_log']}.signature", '=', "{$table['material_file_log']}.signature");
                    });
                }


                if ($param->ad2_create_time || ($param->need_join_ad2_common_log['ad3_common_log'] ?? false)) {
                    $media_hour_data_log_builder->join($table['ad2_common_log'], function (JoinClause $join) use ($param, $table) {
                        $join->on("{$table['ad3_common_log']}.ad2_id", '=', "{$table['ad2_common_log']}.ad2_id");
                        $join->on("{$table['ad3_common_log']}.media_type", '=', "{$table['ad2_common_log']}.media_type");
                        $join->on("{$table['ad3_common_log']}.platform", '=', "{$table['ad2_common_log']}.platform");

                        $param->ad2_create_time && $join->whereBetween("{$table['ad2_common_log']}.ad2_create_time", $param->ad2_create_time);
                    });
                }

                $media_hour_data_log_builder->whereBetween("cost_date", $param->cost_date);

                foreach ($media_hour_data_log_filter as [$condition, $value]) {
                    $media_hour_data_log_builder->whereRaw($condition, $value);
                }

                $media_hour_data_log_builder->groupBy(["title"]);

                $main_builder->withExpression($media_hour_data_log_alias, $media_hour_data_log_builder);

                $main_builder->leftJoin($media_hour_data_log_alias, function (JoinClause $join) use ($param, $media_hour_data_log_alias) {
                    $join->on("driver.title", '=', "{$media_hour_data_log_alias}.title");
                });
            }
        }

        $main_builder->selectRaw("driver.title as title");

        foreach ($param->main_target as $key => $value) {
            $main_builder->selectRaw($value);
        }

        foreach ($param->main_calc as [$condition, $value]) {
            !is_null($value) && $main_builder->havingRaw($condition, (array)$value, $param->condition);
        }

//        foreach ($param->order as $order_key => $order_value) {
//            $main_builder->orderBy($order_value['column'], $order_value['direction']);
//        }

        $main_builder->limit($param->limit);

        return [
            'list' => $main_builder->get(),
            'sql' => $this->getSql($main_builder)
        ];
    }


    /**
     * @param ADComposeFilterParam $param
     * @return array
     */
    private function getMediaHourDataLogTargetAndFilter(ADComposeFilterParam $param)
    {
        $media_table = ADComposeFilterSqlMap::MEDIA_TABLE;
        switch ($param->media_type) {
            case MediaType::TOUTIAO:
                $media_hour_data_log = $this->builder->from($media_table['tt_hour_data_log']);
                $media_hour_data_log_target = $param->tt_hour_data_log_target;
                $media_hour_data_log_filter = $param->tt_hour_data_log_filter;
                return [
                    'alias' => 'tt_hour_data_log',
                    'table' => $media_table['tt_hour_data_log'],
                    'builder' => $media_hour_data_log,
                    'target' => $media_hour_data_log_target,
                    'filter' => $media_hour_data_log_filter,
                ];
            default:
                return [];
        }
    }

    /**
     * 获取定向包驱动表
     * @param $media_type
     * @return array
     */
    private function getTargetingDriverTableDataIndex($media_type)
    {
        $media_table = ADComposeFilterSqlMap::MEDIA_TABLE;
        switch ($media_type) {
            case MediaType::TOUTIAO:
                return [
                    'alias' => 'tt_ad_log',
                    'table' => $media_table['tt_ad_log'],
                    'include_col' => 'retargeting_tags_include',
                    'exclude_col' => 'retargeting_tags_exclude',
                ];
            default:
                throw new AppException('找不到媒体驱动表');
        }
    }

    /**
     * @param $filter_content
     * @param $data_media_type_list
     * @param $root_game_id_list
     * @param $clique_game_id_list
     * @param $platform
     * @param $user_list
     * @param $material_permission
     * @param $agent_permission
     * @param $game_permission
     * @param $material_normal_list
     * @param array $material_file_ids
     * @return array
     */
    public function getIntelligentComposeMaterialFilter(
        $filter_content,
        $data_media_type_list, $root_game_id_list, $clique_game_id_list,
        $platform, $user_list, $material_permission,
        $agent_permission, $game_permission,
        $material_normal_list,
        array $material_file_ids = []
    ): array
    {
        $date_time_range = [
            date('Y-m-d 00:00:00', strtotime("-{$filter_content['filter_time']} days")),
            ($filter_content['filter_time_now'] ?? 0) ? date('Y-m-d 23:59:59', strtotime("-{$filter_content['filter_time_now']} days")) : date('Y-m-d 23:59:59')
        ];


        $table = ADComposeFilterSqlMap::TABLE;
        $driver_builder = $this->builder->from($table['material_file_log']);
        $hour_data_log = $this->builder->from($table['hour_data_log']);
        $overview_log = $this->builder->from($table['overview_log']);

        // driver
        $driver_builder->selectRaw("{$table['material_file_log']}.filename as filename");
        $driver_builder->selectRaw("{$table['material_file_log']}.id as material_file_id");
        $driver_builder->selectRaw("{$table['material_file_log']}.material_id as material_id");
        $driver_builder->selectRaw("{$table['material_file_log']}.file_type as file_type");
        $driver_builder->selectRaw("{$table['material_file_log']}.url as url");
        $driver_builder->selectRaw("{$table['material_log']}.theme_id as theme_id");
        $driver_builder->selectRaw("{$table['material_file_log']}.width as width");
        $driver_builder->selectRaw("{$table['material_file_log']}.height as height");
        $driver_builder->selectRaw("{$table['material_file_log']}.size as size");
        $driver_builder->selectRaw("{$table['material_file_log']}.bitrate as bitrate");
        $driver_builder->selectRaw("{$table['material_file_log']}.duration as duration");
        $driver_builder->selectRaw("{$table['material_file_log']}.signature as signature");
        $driver_builder->selectRaw("{$table['material_file_log']}.insert_time as create_time");
        $driver_builder->groupBy('material_file_id');
        $this->setOdsMaterialLogAlias('ods_material_log');
        $this->injectMaterialPermissionAndJoinMaterial($driver_builder, $user_list, $material_permission, $table['material_file_log']);
        $driver_builder->where("{$table['material_file_log']}.platform", "=", $platform);
        $driver_builder->where("{$table['material_file_log']}.file_type", "!=", 3);
        $driver_builder->where('is_ext', "!=", 2);
        $driver_builder->where('ods_material_log.is_del', "=", 0);
        $driver_builder->where("{$table['material_file_log']}.is_del", "=", 0);
        $driver_builder->where('signature', "!=", '');
        $driver_builder->where('ods_material_log.is_public', "=", '1');
        $driver_builder->groupBy('material_file_id');

        // hour_data
        $hour_data_log->selectRaw("{$table['material_file_log']}.id as material_file_id");
        $hour_data_log->selectRaw("SUM(tanwan_datamedia.dwd_media_ad4_common_day_data_log.COST) AS sum_cost");
        $hour_data_log->selectRaw("count(distinct tanwan_datamedia.dwd_media_ad4_common_day_data_log.ad3_id) as make_creative_num");
        $this->injectAgentPermissionAndJoinAgentSite($hour_data_log, $agent_permission, $table['hour_data_log']);
        $this->injectGamePermissionAndJoinGame($hour_data_log, $game_permission, $table['hour_data_log']);
        $hour_data_log->join($table['ad3_common_log'], function (JoinClause $join) use ($table, $data_media_type_list, $platform) {
            $join->on("{$table['hour_data_log']}.ad4_id", '=', "{$table['ad3_common_log']}.ad4_id");
            $join->whereIn("{$table['ad3_common_log']}.media_type", $data_media_type_list);
            $join->where("{$table['ad3_common_log']}.platform", '=', $platform);
        });
        $hour_data_log->join($table['material_file_log'], function (JoinClause $join) use ($table) {
            $join->on("{$table['ad3_common_log']}.signature", '=', "{$table['material_file_log']}.signature");
        });
        if ($filter_content['relate_site_ids'] ?? '') {
            $hour_data_log->whereRaw("agent_site.site_id in ({$filter_content['relate_site_ids']})");
        }

        if ($root_game_id_list) {
            $root_game_id_list_string = implode(',', $root_game_id_list);
            $hour_data_log->whereRaw("game.root_game_id in ($root_game_id_list_string)");
        }

        if ($clique_game_id_list) {
            $clique_game_id_list_string = implode(',', $clique_game_id_list);
            $hour_data_log->whereRaw("game.clique_id in ($clique_game_id_list_string)");
        }


        $hour_data_log->whereBetween("cost_date", $date_time_range);
        $hour_data_log->groupBy(["material_file_id"]);

        // overview
        $overview_log->selectRaw("{$table['material_file_log']}.id as material_file_id");
        $overview_log->selectRaw("SUM(day_first_day_pay_times) AS sum_day_first_day_pay_times");
        $overview_log->selectRaw("SUM(tanwan_datahub.v3_ads_day_root_game_back_overview_log.day_reg_uid_count) AS sum_reg_uid_count");
        $overview_log->selectRaw("SUM(tanwan_datahub.v3_ads_day_root_game_back_overview_log.day_first_day_pay_money) AS sum_first_day_pay_money");
        $overview_log->selectRaw("SUM(day_third_day_pay_money) AS sum_third_day_pay_money");
        $overview_log->selectRaw("SUM(day_seventh_day_pay_money) AS sum_seventh_day_pay_money");
        $overview_log->selectRaw("SUM(day_fifteenth_day_pay_money) AS sum_fifteenth_day_pay_money");
        $overview_log->selectRaw("SUM(tanwan_datahub.v3_ads_day_root_game_back_overview_log.day_first_day_pay_count) AS sum_first_day_pay_count");
        $overview_log->selectRaw("CAST(COUNT(IF(tanwan_datamedia.dwd_media_ad3_common_log.reject_message = '' or tanwan_datamedia.dwd_media_ad3_common_log.reject_message = '[]', 1, NULL)) / COUNT(*) AS DECIMAL(12, 4)) AS reject_rate");
        $this->injectAgentPermissionAndJoinAgentSite($overview_log, $agent_permission, $table['overview_log']);
        $this->injectGamePermissionAndJoinGame($overview_log, $game_permission, $table['overview_log']);
        $overview_log->join($table['ad3_common_log'], function (JoinClause $join) use ($data_media_type_list, $platform, $table) {
            $join->on("{$table['overview_log']}.ad4_id", '=', "{$table['ad3_common_log']}.ad4_id");
            $join->on("$this->v2_dim_agent_site_id_alias.media_type_id", '=', "{$table['ad3_common_log']}.media_type");
            $join->on("{$table['overview_log']}.platform", '=', "{$table['ad3_common_log']}.platform");

            $join->whereIn("{$table['ad3_common_log']}.media_type", $data_media_type_list);
            $join->where("{$table['ad3_common_log']}.platform", '=', $platform);
            $join->where("{$table['ad3_common_log']}.ad4_id", '>', 0);
        });
        $overview_log->join($table['material_file_log'], function (JoinClause $join) use ($table) {
            $join->on("{$table['ad3_common_log']}.signature", '=', "{$table['material_file_log']}.signature");
        });
        $overview_log->join('tanwan_datamedia.dwd_media_ad3_common_log', function (JoinClause $join) use ($table) {
            $join->on("{$table['ad3_common_log']}.ad3_id", '=', "tanwan_datamedia.dwd_media_ad3_common_log.ad3_id");
            $join->on("{$table['ad3_common_log']}.media_type", '=', "tanwan_datamedia.dwd_media_ad3_common_log.media_type");
            $join->on("{$table['ad3_common_log']}.platform", '=', "tanwan_datamedia.dwd_media_ad3_common_log.platform");

        });
        $overview_log->whereRaw("tanwan_datahub.v3_ads_day_root_game_back_overview_log.ad2_id > 0");
        if ($filter_content['relate_site_ids'] ?? '') {
            $overview_log->whereRaw("agent_site.site_id in ({$filter_content['relate_site_ids']})");
        }
        $overview_log->whereBetween("log_date", $date_time_range);
        if ($root_game_id_list) {
            $root_game_id_list_string = implode(',', $root_game_id_list);
            $overview_log->whereRaw("game.root_game_id in ($root_game_id_list_string)");
        }

        if ($clique_game_id_list) {
            $clique_game_id_list_string = implode(',', $clique_game_id_list);
            $overview_log->whereRaw("game.clique_id in ($clique_game_id_list_string)");
        }


        $overview_log->groupBy(["material_file_id"]);

        // main
        $main_builder = $this->builder->from('driver');
        $main_builder->withExpression('driver', $driver_builder);
        $main_builder->withExpression('overview_log', $overview_log);
        $main_builder->withExpression('hour_data_log', $hour_data_log);

        $main_builder->selectRaw('driver.*');
        $main_builder->selectRaw('IFNULL(hour_data_log.make_creative_num, 0) AS make_creative_num');
        $main_builder->selectRaw('IFNULL(hour_data_log.sum_cost, 0) AS cost');
        $main_builder->selectRaw('IFNULL(CAST((hour_data_log.sum_cost / overview_log.sum_reg_uid_count) AS DECIMAL(12, 4)), 0) AS cost_per_reg');
        $main_builder->selectRaw('IFNULL(overview_log.sum_day_first_day_pay_times, 0) AS day_first_day_pay_times');
        $main_builder->selectRaw('IFNULL(CAST(overview_log.sum_first_day_pay_count / overview_log.sum_reg_uid_count AS DECIMAL ( 12, 4 ) ), 0 ) AS first_day_pay_rate');
        $main_builder->selectRaw('sum_reg_uid_count as reg_uid_count');
        $main_builder->selectRaw('IFNULL(CAST((overview_log.sum_first_day_pay_money / hour_data_log.sum_cost) AS DECIMAL(12, 4)), 0) AS first_day_roi');
        $main_builder->selectRaw('IFNULL(CAST((overview_log.sum_first_day_pay_money / overview_log.sum_first_day_pay_count) AS DECIMAL(12, 4)), 0) AS first_day_arppu');
        $main_builder->selectRaw('sum_cost');
        $main_builder->selectRaw('sum_first_day_pay_money');
        $main_builder->selectRaw('reject_rate');
        $main_builder->selectRaw('IFNULL(CAST((overview_log.sum_third_day_pay_money / hour_data_log.sum_cost) AS DECIMAL(12, 4)), 0) AS rate_day_roi_3');
        $main_builder->selectRaw('IFNULL(CAST((overview_log.sum_third_day_pay_money / overview_log.sum_first_day_pay_money) AS DECIMAL(12, 4)), 0) AS grow_rate_day_3');
        $main_builder->selectRaw('sum_third_day_pay_money');
        $main_builder->selectRaw('IFNULL(CAST((overview_log.sum_seventh_day_pay_money / hour_data_log.sum_cost) AS DECIMAL(12, 4)), 0) AS rate_day_roi_7');
        $main_builder->selectRaw('IFNULL(CAST((overview_log.sum_seventh_day_pay_money / overview_log.sum_first_day_pay_money) AS DECIMAL(12, 4)), 0) AS grow_rate_day_7');
        $main_builder->selectRaw('sum_seventh_day_pay_money');
        $main_builder->selectRaw('IFNULL(CAST((overview_log.sum_fifteenth_day_pay_money / hour_data_log.sum_cost) AS DECIMAL(12, 4)),0) AS rate_day_roi_15');
        $main_builder->selectRaw('IFNULL(CAST((overview_log.sum_fifteenth_day_pay_money / overview_log.sum_first_day_pay_money) AS DECIMAL(12, 4)), 0) AS grow_rate_day_15');
        $main_builder->selectRaw('sum_fifteenth_day_pay_money');
        $main_builder->selectRaw('IFNULL(CAST(hour_data_log.sum_cost / overview_log.sum_first_day_pay_count AS DECIMAL(12, 4)), 0) AS cost_per_first_day_pay');
        $main_builder->selectRaw('sum_first_day_pay_count');

        $main_builder->leftJoin('hour_data_log', function (JoinClause $join) {
            $join->on('driver.material_file_id', '=', 'hour_data_log.material_file_id');
        });
        $main_builder->leftJoin('overview_log', function (JoinClause $join) {
            $join->on('driver.material_file_id', '=', 'overview_log.material_file_id');
        });

        // condition
        $condition_builder = $this->builder->fromSub($main_builder, 'condition');

        $mode_config = ['1' => 'image', '2' => 'video'];

        if ($filter_content['filter_file_type']) {
            $condition_builder->where(function (Builder $mode_type_query) use ($filter_content, $mode_config) {
                foreach ($filter_content['filter_file_type'] as $file_type) {
                    $mode_list = [];
                    if (isset($mode_config[$file_type])) {
                        $mode_list = $filter_content['filter_mode'][$mode_config[$file_type]] ?? [];
                    }
                    if ($mode_list) {
                        $mode_type_query->orWhere(function (Builder $mode_query) use ($mode_list, $file_type) {
                            foreach ($mode_list as $mode) {
                                $mode_query->orWhere(function (Builder $mode_sub_query) use ($mode, $file_type) {
                                    $mode_array = explode('*', $mode);
                                    $width = $mode_array[0];
                                    $height = $mode_array[1];
                                    $mode_sub_query->where('width', $width);
                                    $mode_sub_query->where('height', $height);
                                    $mode_sub_query->where('file_type', $file_type);
                                });
                            }
                        });
                    } else {
                        $mode_type_query->orWhere('file_type', $file_type);
                    }
                }
            });
        }

        $condition_builder->where(function (Builder $filter_query) use ($filter_content) {
            foreach ($filter_content['filter_list'] as $item) {
                if ($item['key'] == 'create_time_exact') {
                    $item['key'] = 'create_time';
                }
                if ($item['key'] && $item['condition'] && ($item['value'] || is_numeric($item['value']))) {
                    if (!in_array($item['condition'], ['before', 'notlike'])) {
                        if ($filter_content['filter_type'] == 'and') {
                            $filter_query->where($item['key'], $item['condition'], $item['value']);
                        } else {
                            $filter_query->orWhere($item['key'], $item['condition'], $item['value']);
                        }
                    } elseif ($item['condition'] == 'before') {
                        if ($filter_content['filter_type'] == 'and') {
                            $filter_query->whereBetween($item['key'], [date('Y-m-d 00:00:00', strtotime("-{$item['value']} days")), date('Y-m-d 23:59:59')]);
                        } else {
                            $filter_query->orWhereBetween($item['key'], [date('Y-m-d 00:00:00', strtotime("-{$item['value']} days")), date('Y-m-d 23:59:59')]);
                        }
                    } else {
                        if ($filter_content['filter_type'] == 'and') {
                            $filter_query->whereRaw($item['key'] . ' not like ' . "'%{$item['value']}%'");
                        } else {
                            $filter_query->orWhereRaw($item['key'] . ' not like ' . "'%{$item['value']}%'");
                        }
                    }
                }
            }
        });


        foreach ($material_normal_list as $item) {
            if ($item[0] != '_') {
                $condition_builder->where($item[0], $item[1], $item[2]);
            } else {
                $condition_builder->where(function (Builder $query) use ($item) {
                    foreach ($item[2] as $cond) {
                        $query->where(function (Builder $sub_query) use ($cond) {
                            foreach ($cond as $k => $v) {
                                if (is_array($v)) {
                                    $sub_query->whereBetween($k, $v);
                                } else {
                                    $sub_query->where($k, $v);
                                }
                            }
                        }, null, null, $item[1]);

                    }
                });

            }
        }

        $filter_content['material_theme_ids'] && $condition_builder->whereIn('theme_id', array_map(function ($ele) {
            return $ele[1];
        }, $filter_content['material_theme_ids']));

        if ($filter_content['back_material_ids'] ?? false) {
            $back_list_material_ids = array_values(array_filter(explode(',', $filter_content['back_material_ids'])));
            if ($back_list_material_ids) {
                $condition_builder->whereNotIn('material_id', $back_list_material_ids);
            }
        }

        if ($filter_content['exclude_material_packet_list'] ?? []) {
            $exclude_material_info_list = (new ADMaterialPacketModel())->getListById($filter_content['exclude_material_packet_list']);

            if ($exclude_material_info_list) {
                $back_list_material_file_ids = [];
                $exclude_material_key = [
                    'video_vertical_list', 'video_list', 'group_image_list', 'group_video_list', 'small_image_list',
                    'large_image_list', 'large_vertical_image_list',
                ];
                foreach ($exclude_material_info_list as $exclude_material_info) {
                    foreach ($exclude_material_key as $emk) {
                        $excl_m_key_list = json_decode($exclude_material_info->{$emk}, true);
                        if ($excl_m_key_list) {
                            foreach ($excl_m_key_list as $e_file) {
                                $back_list_material_file_ids[] = $e_file['id'];
                            }
                        }
                    }
                }

                $back_list_material_file_ids = array_values(array_unique(array_filter($back_list_material_file_ids)));

                if ($back_list_material_file_ids) {
                    $condition_builder->whereNotIn('material_file_id', $back_list_material_file_ids);
                }
            }
        }


        if ($material_file_ids) {
            $condition_builder->whereIn("material_file_id", $material_file_ids);
        }

        $condition_builder->limit(500);

        return [
            'sql' => $this->getSql($condition_builder),
            'data' => $condition_builder->get()->unique('signature')
        ];
    }
}
