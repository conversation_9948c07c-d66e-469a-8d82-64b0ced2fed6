<?php

namespace App\Model\SqlModel\DataMedia;

use Illuminate\Support\Collection;

class DwdLiveAnchorOrderCostCalcModel extends AbstractDataMediaSqlModel
{
    protected $table = 'dwd_live_anchor_order_cost_calc';

    /**
     * 查询逻辑是郭嘉辉给的
     * @return Collection
     */
    public function getSiteIdOrders(): \Illuminate\Support\Collection
    {
        $builder = $this->builder;
        $builder->selectRaw("
            platform,
            android_site_id as site_id,
            DATE( first_live_time ) AS first_live_date,
            GROUP_CONCAT( live_order_id SEPARATOR '|' ) AS live_order_ids,
            GROUP_CONCAT(trade_no SEPARATOR ' ') as trade_nos
        ");
        $builder->where('first_live_time', '>=', '2025-05-01 00:00:00');
        $builder->whereIn('price_type', [5, 6]);
        $builder->groupBy([
            'platform',
            'site_id',
            'first_live_date'
        ]);

        return $builder->get();
    }

    /**
     * @return Collection
     */
    public function getLiveAgentLeaderList($keyword)
    {
        return $this->builder->selectRaw("DISTINCT agent_leader")->where('agent_leader', 'like', "%$keyword%")->get();
    }

    /**
     * @return Collection
     */
    public function getOrderTypeList()
    {
        return $this->builder->select(['order_type_label'])->distinct()->get();
    }
}
