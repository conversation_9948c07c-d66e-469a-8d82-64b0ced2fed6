<?php

namespace App\Model\SqlModel\DataMedia;



use App\Constant\MediaType;
use App\Constant\PlatId;
use App\Model\SqlModel\Database\ZDBuilder;
use App\Param\FinanceListParam;
use Illuminate\Database\Query\Builder;
use Illuminate\Database\Query\JoinClause;

class OdsBaiduAccountDayDataLogModel extends AbstractDataMediaSqlModel
{
    protected $table = 'ods_baidu_account_day_data_log';


    public function getList(FinanceListParam $param)
    {
        $builder = $this->builder;
        $builder->from($this->table . ' as record_log');

        $sub_builder = $this->builder;
        $day_log_builder = $this->builder;


        if ($param->platform) {
            $day_log_builder->whereIn('platform', $param->platform);
            $builder->whereIn('record_log.platform', $param->platform);
        }
        if ($param->account_id) {
            $day_log_builder->whereIn('account_id', $param->account_id);
            $builder->whereIn('record_log.account_id', $param->account_id);
        }
        if ($param->date) {
            $cost_date[0] = $param->date[0] . ' 00:00:00';
            $cost_date[1] = $param->date[1] . ' 23:59:59';
            $day_log_builder->whereBetween('tdate', $cost_date);
            $builder->whereBetween('record_log.cost_date', $param->date);
        }

        $sub_builder->fromRaw("tanwan_datamedia.ods_baidu_creative_hour_data_log");
        $sub_builder->selectRaw("SUM(ori_cost) AS ori_cost, account_id AS account_id, cost_date AS cost_date");
        $sub_builder->whereBetween('cost_date', $param->date);
        $sub_builder->groupBy(['account_id','cost_date']);

        $builder->joinSub($sub_builder, 'sub', function (JoinClause $joinClause) {
            $joinClause->on('record_log.account_id', 'sub.account_id');
            $joinClause->on('record_log.cost_date', 'sub.cost_date');
        },null,null,'FULL OUTER');

        $day_log_sql = $this->dayLogBuilderSql($param, MediaType::BAIDU);

        $day_log_builder->fromSub($day_log_sql, 't');

        if ($param->aggregation_time === '分月') {
            $day_log_builder->selectRaw('
                tdate_1, 
                sum( cost_money ) AS cost_money, 
                sum( ori_money ) AS ori_money, 
                platform,
                account_id,
                contract_game_name,
                clique_game,
                root_game,
                main_game,
                tdate AS date,
                payway_company
            ');

            $day_log_builder->groupBy([
                'account_id', 'date', 'agent_id', 'contract_game_name', 'clique_game', 'root_game', 'main_game'
            ]);

            $day_log_month_builder = $this->builder->fromSub($day_log_builder, 't');
            $day_log_month_builder->selectRaw("
                sum( cost_money ) AS cost_money,
                sum( ori_money ) AS ad_cost, 
                platform,
                account_id,
                group_concat(DISTINCT contract_game_name) as contract_game_name,
                group_concat(DISTINCT clique_game) as clique_game,
                group_concat(DISTINCT root_game) as root_game,
                group_concat(DISTINCT main_game) as main_game,
                YEAR_MONTH(date) AS m_date,
                payway_company
            ");

            $day_log_month_builder->groupBy(['account_id', 'm_date']);

            $builder->withExpression('day_log', $day_log_month_builder);
            $builder->leftJoin('day_log', function (JoinClause $join)  {
                $join->on('day_log.account_id', '=', 'record_log.account_id');
                $join->whereRaw('day_log.m_date = YEAR_MONTH(record_log.cost_date)');
            });

            $builder->selectRaw('record_log.platform, IFNULL(sub.account_id, record_log.account_id) AS account_id, 
                YEAR_MONTH(IFNULL(record_log.cost_date, sub.cost_date)) AS m_date, 
                SUM(IFNULL(record_log.ori_cost, 0)) AS ori_cost_account,
                SUM(IFNULL(sub.ori_cost,0)) AS ori_cost_creative,
                SUM(IFNULL(sub.ori_cost,0)) - SUM(IFNULL(record_log.ori_cost,0)) AS cost_difference,
                account_log.account_name, account_log.creator as creator, account_log.account_leader as agent_leader, account_log.company as company,
                account_log.majordomo_name as majordomo_name, agency_change.agency_short_name, 
                agency_change.agency_full_name as agency_name,  agency_change.settle_company as settle_company, agency_change.project_team as project_team,
                IFNULL(day_log.ad_cost, 0) AS day_cost, IFNULL(ABS( SUM( record_log.ori_cost ) - day_log.ad_cost ), 0) AS day_cost_diff,
                account_log.majordomo_account_id, majordomo_agency_change.agency_full_name as majordomo_agency_name, 
                majordomo_agency_change.settle_company as majordomo_settle_company, majordomo_agency_change.agency_short_name as majordomo_agency_short_name,
                majordomo_agency_change.project_team as majordomo_project_team,
                IFNULL( GROUP_CONCAT( DISTINCT day_log.contract_game_name), "" ) AS contract_game_name,
                IFNULL( GROUP_CONCAT( DISTINCT day_log.clique_game), "" ) AS clique_game,
                IFNULL( GROUP_CONCAT( DISTINCT day_log.root_game), "" ) AS root_game,
                IFNULL( GROUP_CONCAT( DISTINCT day_log.main_game), "" ) AS main_game,
                IFNULL( day_log.payway_company, "" ) AS payway_company
                ');
            if ($param->ori_cost_account) {
                if ($param->ori_cost_account[0]) {
                    $builder->havingRaw("SUM(IFNULL(record_log.ori_cost, 0)) >= ? ", [$param->ori_cost_account[0]]);
                }
                if ($param->ori_cost_account[1]) {
                    $builder->havingRaw("SUM(IFNULL(record_log.ori_cost, 0)) <= ? ", [$param->ori_cost_account[1]]);
                }
            }
            if ($param->ori_cost_creative) {
                if ($param->ori_cost_creative[0]) {
                    $builder->havingRaw("SUM(IFNULL(sub.ori_cost,0)) >= ? ", [$param->ori_cost_creative[0]]);
                }
                if ($param->ori_cost_creative[1]) {
                    $builder->havingRaw("SUM(IFNULL(sub.ori_cost,0)) <= ? ", [$param->ori_cost_creative[1]]);
                }
            }
            if ($param->cost_difference) {
                if ($param->cost_difference[0]) {
                    $builder->havingRaw("SUM(IFNULL(sub.ori_cost,0)) - SUM(IFNULL(record_log.ori_cost,0)) >= ? ", [$param->cost_difference[0]]);
                }
                if ($param->cost_difference[1]) {
                    $builder->havingRaw("SUM(IFNULL(sub.ori_cost,0)) - SUM(IFNULL(record_log.ori_cost,0)) <= ? ", [$param->cost_difference[1]]);
                }
            }

            $builder->groupBy(['record_log.platform', 'record_log.account_id', 'm_date']);

        } elseif ($param->aggregation_time === '分日') {
            $day_log_builder->groupBy(['account_id', 'date']);
            $day_log_builder->selectRaw('
                tdate_1, 
                sum( cost_money ) AS cost_money, 
                sum( ori_money ) AS ad_cost, 
                platform,
                account_id,
                group_concat(DISTINCT contract_game_name) as contract_game_name,
                group_concat(DISTINCT clique_game) as clique_game,
                group_concat(DISTINCT root_game) as root_game,
                group_concat(DISTINCT main_game) as main_game,
                tdate AS date,
                payway_company
            ');

            $builder->withExpression('day_log', $day_log_builder);

            $builder->leftJoin('day_log', function (JoinClause $join)  {
                $join->on('day_log.account_id', '=', 'record_log.account_id');
                $join->on('day_log.date', '=', "record_log.cost_date");
            });

            $builder->selectRaw('record_log.platform, IFNULL(sub.account_id, record_log.account_id) AS account_id, 
                IFNULL(record_log.cost_date, sub.cost_date) AS date, 
                IFNULL(record_log.ori_cost, 0) AS ori_cost_account,
                IFNULL(sub.ori_cost,0) AS ori_cost_creative,
                IFNULL(sub.ori_cost,0) - IFNULL(record_log.ori_cost,0) AS cost_difference,
                account_log.account_name, account_log.creator as creator, account_log.account_leader as agent_leader, account_log.company as company,
                account_log.majordomo_name as majordomo_name,
                agency_change.agency_full_name as agency_name,  agency_change.settle_company as settle_company, agency_change.agency_short_name, 
                agency_change.project_team as project_team,
                day_log.ad_cost AS day_cost, ABS(  record_log.ori_cost  - day_log.ad_cost ) AS day_cost_diff,
                IFNULL( day_log.contract_game_name, "" ) AS contract_game_name,
                IFNULL( day_log.clique_game, "" ) AS clique_game,
                IFNULL( day_log.root_game, "" ) AS root_game,
                IFNULL( day_log.main_game, "" ) AS main_game,
                IFNULL( day_log.payway_company, "" ) AS payway_company
            ');

            if ($param->ori_cost_account) {
                if ($param->ori_cost_account[0]) {
                    $builder->whereRaw("IFNULL(record_log.ori_cost, 0) >= ? ", [$param->ori_cost_account[0]]);
                }
                if ($param->ori_cost_account[1]) {
                    $builder->whereRaw("IFNULL(record_log.ori_cost, 0) <= ? ", [$param->ori_cost_account[1]]);
                }
            }
            if ($param->ori_cost_creative) {
                if ($param->ori_cost_creative[0]) {
                    $builder->whereRaw("IFNULL(sub.ori_cost,0) >= ? ", [$param->ori_cost_creative[0]]);
                }
                if ($param->ori_cost_creative[1]) {
                    $builder->whereRaw("IFNULL(sub.ori_cost,0) <= ? ", [$param->ori_cost_creative[1]]);
                }
            }
            if ($param->cost_difference) {
                if ($param->cost_difference[0]) {
                    $builder->whereRaw("IFNULL(sub.ori_cost,0) - IFNULL(record_log.ori_cost,0) >= ? ", [$param->cost_difference[0]]);
                }
                if ($param->cost_difference[1]) {
                    $builder->whereRaw("IFNULL(sub.ori_cost,0) - IFNULL(record_log.ori_cost,0) <= ? ", [$param->cost_difference[1]]);
                }
            }
        }

        $builder->Join('ods_baidu_account_log as account_log', function (JoinClause $join) {
            $join->on("account_log.platform", '=', 'record_log.platform');
            $join->on("account_log.account_id", '=', 'record_log.account_id');
        });

        if ($param->agent_leader) {
            $builder->whereIn('account_log.account_leader', $param->agent_leader);
        }
        if ($param->company) {
            $builder->whereIn('account_log.company', $param->company);
        }
        if ($param->creator) {
            $builder->whereIn('account_log.creator', $param->creator);
        }
        if ($param->majordomo_name) {
            $builder->whereIn('account_log.majordomo_name', $param->majordomo_name);
        }

        $builder->leftJoin('ods_media_account_agency_change_log as agency_change', function (JoinClause $join) {
            $join->on("account_log.account_id", '=', 'agency_change.account_id');
            $join->whereRaw("record_log.cost_date BETWEEN agency_change.start_date and agency_change.end_date");
            $join->on("account_log.platform", '=', 'agency_change.platform');
            $join->where("agency_change.media_type", '=', MediaType::BAIDU);
        });
        if ($param->agency_name) {
            $builder->whereIn('agency_change.agency_full_name', $param->agency_name);
        }
        if ($param->settle_company) {
            $builder->whereIn('agency_change.settle_company', $param->settle_company);
        }
        if ($param->agency_short_name) {
            $builder->whereIn('agency_change.agency_short_name', $param->agency_short_name);
        }

        // 获取管家代理信息
        $builder->leftJoin('ods_media_account_agency_change_log as majordomo_agency_change', function (JoinClause $join) {
            $join->on("account_log.majordomo_account_id", '=', 'majordomo_agency_change.account_id');
            $join->whereRaw("record_log.cost_date BETWEEN majordomo_agency_change.start_date and majordomo_agency_change.end_date");
            $join->on("account_log.platform", '=', 'majordomo_agency_change.platform');
            $join->where("majordomo_agency_change.media_type", '=', MediaType::BAIDU);
        });

        if ($param->majordomo_agency_name) {
            $builder->whereIn('majordomo_agency_change.agency_full_name', $param->majordomo_agency_name);
        }
        if ($param->majordomo_settle_company) {
            $builder->whereIn('majordomo_agency_change.settle_company', $param->majordomo_settle_company);
        }
        if ($param->majordomo_agency_short_name) {
            $builder->whereIn('majordomo_agency_change.agency_short_name', $param->majordomo_agency_short_name);
        }

        $this->injectAgentLeaderPermission($builder, $param->agent_permission, 'creator', 'account_log');

        if ($param->search_time) {
            $builder->where('record_log.insert_time', '<', $param->search_time);
            $all = $builder
                ->orderByDesc('record_log.account_id')
                ->orderByDesc('m_date')
                ->get();
        } else {
            $all = $builder->orderByDesc('record_log.insert_time')->get();
        }

        $count = $all->count();

        return [
            'total' => $count,
            'sql' => $this->getSql($builder),
            'list' => $all->forPage($param->page, $param->rows)->values(),
            'all' => $all
        ];
    }


    public function dayLogBuilderSql(FinanceListParam $param, $media_type) {
        $day_log_sub_one_builder = $this->builder;
        $day_log_sub_two_builder = $this->builder;

        if ($param->platform && $param->account_id) {
            $day_log_sub_one_builder->where(function (Builder $query) use ($param) {
                $query->where(function (Builder $sub_query) use ($param) {
                    $sub_query->whereRaw("agent.platform = '0'");
                    $sub_query->whereIn('agent.account_id', $param->account_id);
                });
                $query->orWhere(function (Builder $sub_query) use ($param) {
                    $sub_query->whereIn('agent.platform', $param->platform);
                    $sub_query->whereIn('agent.account_id', $param->account_id);
                });
            });
            $day_log_sub_two_builder->where(function (Builder $query) use ($param) {
                $query->where(function (Builder $sub_query) use ($param) {
                    $sub_query->whereRaw("agent.platform = '0'");
                    $sub_query->whereIn('agent.account_id', $param->account_id);
                });
                $query->orWhere(function (Builder $sub_query) use ($param) {
                    $sub_query->whereIn('agent.platform', $param->platform);
                    $sub_query->whereIn('agent.account_id', $param->account_id);
                });
            });
        } else {
            if ($param->platform) {
                $day_log_sub_one_builder->where(function (Builder $query) use ($param) {
                    $query->whereRaw("agent.platform = '0'");
                    $query->orWhereIn('agent.platform', $param->platform);
                });
                $day_log_sub_two_builder->where(function (Builder $query) use ($param) {
                    $query->whereRaw("agent.platform = '0'");
                    $query->orWhereIn('agent.platform', $param->platform);
                });
            }
            if ($param->account_id) {
                $day_log_sub_one_builder->whereIn('agent.account_id', $param->account_id);
                $day_log_sub_two_builder->whereIn('agent.account_id', $param->account_id);
            }
        }
        if ($param->date) {
            $day_log_sub_one_builder->whereBetween('tdate', $param->date);
            $day_log_sub_two_builder->whereBetween('t.cost_date', $param->date);
        }

        // 收款主体builder
        $pay_builder = $this->builder;
        $pay_builder->from('tanwan_datahub.v2_ods_pay_order_log as pay');
        $pay_builder->join('tanwan_datahub.v2_dim_pay_way_id as pw', function (JoinClause $join) {
            $join->on('pay.platform', '=', 'pw.platform');
            $join->on('pay.pay_way_id', '=', 'pw.pay_way_id');
        });
        $pay_builder
            ->select(['pw.platform', 'game_id'])
            ->selectRaw('sum(pay_money) as pay_money')
            ->selectRaw('YEAR_MONTH(pay_time) as ym')
            ->selectRaw("CASE WHEN pw.company = '上饶新新' THEN '江西贪玩' ELSE pw.company END AS payway_company")
            ->where('pay_time', '>', '2024-12-01')
            ->where('order_status_id', 1)
            ->whereNotIn('pw.company', ['', '上海德寒'])
            ->groupBy(['pw.platform', 'game_id', 'payway_company', 'ym']);

        $pw_builder = $this->builder->fromSub($pay_builder, 'pw');
        $pw_builder
            ->selectRaw("
                platform, 
                game_id, 
                ym, 
                GROUP_CONCAT(DISTINCT concat(payway_company, '-', pay_money)) as payway_company
            ")
            ->groupBy(['pw.platform', 'game_id', 'ym']);

        $day_log_sub_one_builder->from('tanwan_datahub.v2_dwd_day_cost_log as t');
        $day_log_sub_one_builder->Join('tanwan_datahub.v2_dim_game_id as game', function (JoinClause $join) {
            $join->on('t.platform', '=', 'game.platform');
            $join->on('t.game_id', '=', 'game.game_id');
        });
        $day_log_sub_one_builder->Join('tanwan_datahub.v2_dim_agent_id as agent', function (JoinClause $join) {
            $join->on('t.platform', '=', 'agent.platform');
            $join->on('t.agent_id', '=', 'agent.agent_id');
            $join->whereRaw('t.tdate BETWEEN agent.agent_leader_start_time AND agent.agent_leader_end_time');
        });
        $day_log_sub_one_builder->leftJoinSub($pw_builder, 'pw', function (JoinClause $join) {
            $join->on('pw.platform', '=', 't.platform')
                ->on('pw.game_id', '=', 't.game_id')
                ->whereRaw("YEAR_MONTH(t.tdate) = pw.ym");
        });

        $plat_id_sql = PlatId::getMiniPlatIDSql();
        $day_log_sub_one_builder->whereRaw("
            ( money != 0 OR ori_money != 0 ) 
			AND (game.plat_id NOT IN $plat_id_sql OR agent.media_type_id NOT IN (1, 3, 4) OR t.tdate >= CURRENT_DATE OR t.money_type != 1 OR t.add_type IN ( 11, 12, 13 ))
		");

        $day_log_sub_one_builder->selectRaw(" tdate AS tdate_1,tdate as tdate,t.media_type_id as media_type,
				sum( money ) AS cost_money,
				sum( ori_money ) AS ori_money,
				t.platform,
				agent.agent_leader,
				agent.agent_group_id,
				agent.agent_group_name,
				agent.agent_id,
				agent.agent_name,
				agent.account_id,
				game.game_id,
				game.contract_game_name,
				concat(game.clique_id, '-', game.clique_name) as clique_game,
				concat(game.root_game_id, '-', game.root_game_name) as root_game,
				concat(game.main_game_id, '-', game.main_game_name) as main_game,
				concat(game.game_id, '-', game.game_name) as game,
				pw.payway_company as payway_company
        ");
        $day_log_sub_one_builder->groupBy(['tdate_1','t.platform','agent.agent_leader','agent.agent_group_id','agent.agent_id','agent.account_id','game.game_id','pw.payway_company']);

        $day_log_sub_two_builder->from('tanwan_datamedia.dwd_media_ad2_common_day_data_log as t');
        $day_log_sub_two_builder->Join('tanwan_datahub.v2_dim_game_id as game', function (JoinClause $join) {
            $join->on('t.platform', '=', 'game.platform');
            $join->on('t.game_id', '=', 'game.game_id');
        });
        $day_log_sub_two_builder->Join('tanwan_datahub.v2_dim_site_id as site', function (JoinClause $join) {
            $join->on('t.platform', '=', 'site.platform');
            $join->on('t.site_id', '=', 'site.site_id');
        });
        $day_log_sub_two_builder->Join('tanwan_datahub.v2_dim_agent_id as agent', function (JoinClause $join) {
            $join->on('t.platform', '=', 'agent.platform');
            $join->on('site.agent_id', '=', 'agent.agent_id');
            $join->whereRaw('t.cost_date BETWEEN agent.agent_leader_start_time 
				AND agent.agent_leader_end_time');
        });
        $day_log_sub_two_builder->leftJoinSub($pw_builder, 'pw', function (JoinClause $join) {
            $join->on('pw.platform', '=', 't.platform')
                ->on('pw.game_id', '=', 't.game_id')
                ->whereRaw("YEAR_MONTH(t.cost_date) = pw.ym");
        });

        $day_log_sub_two_builder->selectRaw("t.cost_date  AS tdate_1,	t.cost_date AS tdate,t.media_type,
				sum( cost ) AS cost_money,
				sum( ori_cost ) AS ori_money,
				t.platform,
				agent.agent_leader,
				agent.agent_group_id,
				agent.agent_group_name,
				agent.agent_id,
				agent.agent_name,
				agent.account_id,
				game.game_id,
				game.contract_game_name,
				concat(game.clique_id, '-', game.clique_name) as clique_game,
				concat(game.root_game_id, '-', game.root_game_name) as root_game,
				concat(game.main_game_id, '-', game.main_game_name) as main_game,
				concat(game.game_id, '-', game.game_name) as game,
				pw.payway_company as payway_company        
        ");

        // 处理游戏筛选
        if ($param->game_filter) {
            foreach ($param->game_filter as [$condition, $value]) {
                $day_log_sub_one_builder->whereRaw($condition, $value);
                $day_log_sub_two_builder->whereRaw($condition, $value);
            }
        }

        if ($param->clique_id) {
            $day_log_sub_one_builder->whereIn('game.clique_id', $param->clique_id);
            $day_log_sub_two_builder->whereIn('game.clique_id', $param->clique_id);
        }

        $day_log_sub_two_builder->whereRaw("( cost != 0 OR ori_cost != 0 )  AND (`game`.`plat_id` IN $plat_id_sql AND `agent`.`media_type_id` IN (1, 3, 4))");
        $day_log_sub_two_builder->groupBy(['tdate_1','t.platform','agent.agent_leader','agent.agent_group_id','agent.agent_id','agent.account_id','game.game_id','pw.payway_company']);
        $day_log_sub_one_builder->unionAll($day_log_sub_two_builder);

        $builder = $this->builder->fromSub($day_log_sub_one_builder, 't');
        $builder->selectRaw('
            media_type,
            tdate_1,
            platform,
            agent_leader,
            agent_group_id,
            agent_id,
            account_id,
            game_id,
            sum( cost_money ) AS cost_money,
            sum( ori_money ) AS ori_money,
            group_concat( DISTINCT contract_game_name ) AS contract_game_name,
            group_concat( DISTINCT clique_game ) AS clique_game,
            group_concat( DISTINCT root_game ) AS root_game,
            group_concat( DISTINCT main_game ) AS main_game,
            group_concat( DISTINCT game ) AS game,
            group_concat(
            DISTINCT CONCAT( game_id, "-", payway_company ) order by CONCAT( game_id, "-", payway_company ) desc) AS payway_company,
            tdate 
        ');
        $builder->groupBy(['tdate_1','platform','agent_leader','agent_group_id','agent_id','account_id','game_id']);

        return $this->getSql($builder);
    }

    /**
     * @return ZDBuilder
     */
    public function paywayCompanyBuilder()
    {
        // 收款主体builder
        $pay_builder = $this->builder;
        $pay_builder->from('tanwan_datahub.v2_ods_pay_order_log as pay');
        $pay_builder->join('tanwan_datahub.v2_dim_pay_way_id as pw', function (JoinClause $join) {
            $join->on('pay.platform', '=', 'pw.platform');
            $join->on('pay.pay_way_id', '=', 'pw.pay_way_id');
        });
        $pay_builder
            ->selectRaw("
                pw.platform,
                game_id,
                sum( pay_money ) AS pay_money,
                YEAR_MONTH ( pay_time ) AS ym,
                CASE
                    WHEN pw.company = '上饶新新' THEN
                    '江西贪玩' 
                    WHEN pw.company = '上海德寒' THEN
                    '江西贪玩'
                    WHEN pw.company = '中旭未来' THEN
                    '江西贪玩' ELSE pw.company 
                END AS payway_company 
            ")
            ->where('pay_time', '>', '2024-12-01')
            ->where('order_status_id', 1)
            ->whereNotIn('pw.company', ['', '贪玩测试'])
            ->whereNotIn('pay.pay_way_id', [63, 133, 69])
            ->groupBy(['pw.platform', 'game_id', 'payway_company', 'ym']);


        $pay_sub_builder = $this->builder;
        $pay_sub_builder->from('tanwan_datahub.v2_ods_pay_order_log');
        $pay_sub_builder->selectRaw('*,case when game_id = 16430 then 13424 else game_id END AS game_id_new');

        // 联运游戏的结算主体
        $ly_gcc_builder = $this->builder;
        $ly_gcc_builder->from('tanwan_datahub_ly.v2_dim_game_channel_company as gcc');
        $ly_gcc_builder->selectRaw("
            *,
            IFNULL(
                lag ( gcc.company_start_time ) over ( PARTITION BY gcc.platform, gcc.game_id ORDER BY gcc.company_start_time DESC ),
            cast( '2100-01-01 00:00:00' AS Datetime )) company_end_time_ 
        ");

        $ly_pay_builder = $this->builder;
        $ly_pay_builder->fromSub($pay_sub_builder, 'pay');
        $ly_pay_builder->joinSub($ly_gcc_builder, 'gcc', function (JoinClause $join) {
            $join
                ->on('pay.raw_game_id', '=', 'gcc.game_id')
                ->on('pay.channel_id', '=', 'gcc.channel_id')
                ->where('pay.pay_way_id', 63)
                ->whereRaw('date(pay.pay_time) between gcc.company_start_time and gcc.company_end_time_');
        });

        $ly_pay_builder
            ->selectRaw("
                gcc.platform,
				game_id_new AS game_id,
				sum( pay_money ) AS pay_money,
				YEAR_MONTH ( pay_time ) AS ym,
                CASE
					WHEN gcc.company = '上饶新新' THEN
					'江西贪玩'
					WHEN gcc.company = '上海德寒' THEN
					'江西贪玩' 
					WHEN gcc.company = '中旭未来' THEN
					'江西贪玩' ELSE gcc.company 
				END AS payway_company 
            ")
            ->where('pay_time', '>', '2024-12-01')
            ->where('order_status_id', 1)
            ->whereNotIn('gcc.company', ['', '贪玩测试'])
            ->groupBy(['gcc.platform', 'game_id_new', 'company', 'ym']);

        // 联运米大师+IOS支付的结算主体
        $ly_mi_gcc_builder = $this->builder;
        $ly_mi_gcc_builder->from('tanwan_datahub.v2_dim_game_company as gcc');
        $ly_mi_gcc_builder->selectRaw("
            *,
            IFNULL(
                lag ( gcc.company_start_time ) over ( PARTITION BY gcc.platform, gcc.game_id ORDER BY gcc.company_start_time DESC ),
            cast( '2100-01-01 00:00:00' AS Datetime )) end_time_
        ");

        $ly_mi_builder = $this->builder;
        $ly_mi_builder->fromSub($pay_sub_builder, 'pay');
        $ly_mi_builder->joinSub($ly_mi_gcc_builder, 'gcc', function (JoinClause $join) {
            $join
                ->on('pay.raw_game_id', '=', 'gcc.game_id')
                ->on('pay.pay_way_id', '=', 'gcc.pay_way_id')
                ->on('pay.platform', '=', 'gcc.platform')
                ->whereIn('pay.pay_way_id', [69, 133])
                ->whereRaw('pay.pay_time between gcc.company_start_time and gcc.end_time_');
        });

        $ly_mi_builder
            ->selectRaw("
                gcc.platform,
				game_id_new AS game_id,
				sum( pay_money ) AS pay_money,
				YEAR_MONTH ( pay_time ) AS ym,
                CASE
					WHEN gcc.company = '上饶新新' THEN
					'江西贪玩'
					WHEN gcc.company = '上海德寒' THEN
					'江西贪玩' 
					WHEN gcc.company = '中旭未来' THEN
					'江西贪玩' ELSE gcc.company 
				END AS payway_company 
            ")
            ->where('pay_time', '>', '2024-12-01')
            ->where('order_status_id', 1)
            ->whereNotIn('gcc.company', ['', '贪玩测试'])
            ->groupBy(['gcc.platform', 'game_id_new', 'company', 'ym']);

        $pay_builder->unionAll($ly_pay_builder);
        $pay_builder->unionAll($ly_mi_builder);

        $builder = $this->builder;
        $builder->fromSub($pay_builder, '');
        $builder
            ->selectRaw("
                platform,
                game_id,
                payway_company,
                ym,
                sum( pay_money ) AS pay_money 
            ")
            ->groupBy(['platform', 'game_id', 'payway_company', 'ym']);

        return $builder;
    }

    /**
     * 注入 day_log 构造器
     * @param Builder $builder
     * @param FinanceListParam $param
     * @param $media_type
     * @return Builder
     */
    public function dayLogBuilderNew(Builder $builder, FinanceListParam $param, $media_type) {
        /* 收款主体 */
        $builder->withExpression('pw', $this->paywayCompanyBuilder());

        /* 消耗union */
        $day_log_sub_one_builder = $this->builder;
        $day_log_sub_two_builder = $this->builder;

        if ($param->platform && $param->account_id) {
            $day_log_sub_one_builder->where(function (Builder $query) use ($param) {
                $query->where(function (Builder $sub_query) use ($param) {
                    $sub_query->whereRaw("agent.platform = '0'");
                    $sub_query->whereIn('agent.account_id', $param->account_id);
                });
                $query->orWhere(function (Builder $sub_query) use ($param) {
                    $sub_query->whereIn('agent.platform', $param->platform);
                    $sub_query->whereIn('agent.account_id', $param->account_id);
                });
            });
            $day_log_sub_two_builder->where(function (Builder $query) use ($param) {
                $query->where(function (Builder $sub_query) use ($param) {
                    $sub_query->whereRaw("agent.platform = '0'");
                    $sub_query->whereIn('agent.account_id', $param->account_id);
                });
                $query->orWhere(function (Builder $sub_query) use ($param) {
                    $sub_query->whereIn('agent.platform', $param->platform);
                    $sub_query->whereIn('agent.account_id', $param->account_id);
                });
            });
        } else {
            if ($param->platform) {
                $day_log_sub_one_builder->where(function (Builder $query) use ($param) {
                    $query->whereRaw("agent.platform = '0'");
                    $query->orWhereIn('agent.platform', $param->platform);
                });
                $day_log_sub_two_builder->where(function (Builder $query) use ($param) {
                    $query->whereRaw("agent.platform = '0'");
                    $query->orWhereIn('agent.platform', $param->platform);
                });
            }
            if ($param->account_id) {
                $day_log_sub_one_builder->whereIn('agent.account_id', $param->account_id);
                $day_log_sub_two_builder->whereIn('agent.account_id', $param->account_id);
            }
        }
        if ($param->date) {
            $day_log_sub_one_builder->whereBetween('tdate', $param->date);
            $day_log_sub_two_builder->whereBetween('t.cost_date', $param->date);
        }

        $day_log_sub_one_builder->from('tanwan_datahub.v2_dwd_day_cost_log as t');
        $day_log_sub_one_builder->Join('tanwan_datahub.v2_dim_game_id as game', function (JoinClause $join) {
            $join->on('t.platform', '=', 'game.platform');
            $join->on('t.game_id', '=', 'game.game_id');
        });
        $day_log_sub_one_builder->Join('tanwan_datahub.v2_dim_agent_id as agent', function (JoinClause $join) {
            $join->on('t.platform', '=', 'agent.platform');
            $join->on('t.agent_id', '=', 'agent.agent_id');
            $join->whereRaw('t.tdate BETWEEN agent.agent_leader_start_time AND agent.agent_leader_end_time');
        });
        $day_log_sub_one_builder->leftJoin('pw', function (JoinClause $join) {
            $join->on('pw.platform', '=', 't.platform')
                ->on('pw.game_id', '=', 't.game_id')
                ->whereRaw("YEAR_MONTH(t.tdate) = pw.ym");
        });

        $plat_id_sql = PlatId::getMiniPlatIDSql();
        $day_log_sub_one_builder->whereRaw("
            ( money != 0 OR ori_money != 0 ) 
			AND (game.plat_id NOT IN $plat_id_sql OR agent.media_type_id NOT IN (1, 3, 4) OR t.tdate >= CURRENT_DATE OR t.money_type != 1 OR t.add_type IN ( 11, 12, 13 ))
		");

        $day_log_sub_one_builder->selectRaw("
                tdate AS tdate_1,
                tdate as tdate,
                t.media_type_id as media_type,
				sum( money ) AS cost_money,
				sum( ori_money ) AS ori_money,
				t.platform,
				agent.agent_leader,
				agent.agent_group_id,
				agent.agent_group_name,
				agent.agent_id,
				agent.agent_name,
				agent.account_id,
				game.game_id,
				game.contract_game_name,
				concat(game.clique_id, '-', game.clique_name) as clique_game,
				concat(game.root_game_id, '-', game.root_game_name) as root_game,
				concat(game.main_game_id, '-', game.main_game_name) as main_game,
				concat(game.game_id, '-', game.game_name) as game,
				pw.payway_company as payway_company,
				pw.pay_money
        ");
        $day_log_sub_one_builder->groupBy(['tdate_1','t.platform','agent.agent_leader','agent.agent_group_id','agent.agent_id','agent.account_id','game.game_id','pw.payway_company']);

        $day_log_sub_two_builder->from('tanwan_datamedia.dwd_media_ad2_common_day_data_log as t');
        $day_log_sub_two_builder->Join('tanwan_datahub.v2_dim_game_id as game', function (JoinClause $join) {
            $join->on('t.platform', '=', 'game.platform');
            $join->on('t.game_id', '=', 'game.game_id');
        });
        $day_log_sub_two_builder->Join('tanwan_datahub.v2_dim_site_id as site', function (JoinClause $join) {
            $join->on('t.platform', '=', 'site.platform');
            $join->on('t.site_id', '=', 'site.site_id');
        });
        $day_log_sub_two_builder->Join('tanwan_datahub.v2_dim_agent_id as agent', function (JoinClause $join) {
            $join->on('t.platform', '=', 'agent.platform');
            $join->on('site.agent_id', '=', 'agent.agent_id');
            $join->whereRaw('t.cost_date BETWEEN agent.agent_leader_start_time 
				AND agent.agent_leader_end_time');
        });
        $day_log_sub_two_builder->leftJoin('pw', function (JoinClause $join) {
            $join->on('pw.platform', '=', 't.platform')
                ->on('pw.game_id', '=', 't.game_id')
                ->whereRaw("YEAR_MONTH(t.cost_date) = pw.ym");
        });

        $day_log_sub_two_builder->selectRaw("
                t.cost_date  AS tdate_1,
                t.cost_date AS tdate,
                t.media_type,
				sum( cost ) AS cost_money,
				sum( ori_cost ) AS ori_money,
				t.platform,
				agent.agent_leader,
				agent.agent_group_id,
				agent.agent_group_name,
				agent.agent_id,
				agent.agent_name,
				agent.account_id,
				game.game_id,
				game.contract_game_name,
				concat(game.clique_id, '-', game.clique_name) as clique_game,
				concat(game.root_game_id, '-', game.root_game_name) as root_game,
				concat(game.main_game_id, '-', game.main_game_name) as main_game,
				concat(game.game_id, '-', game.game_name) as game,
				pw.payway_company as payway_company,
				pw.pay_money     
        ");

        // 处理游戏筛选
        if ($param->game_filter) {
            foreach ($param->game_filter as [$condition, $value]) {
                $day_log_sub_one_builder->whereRaw($condition, $value);
                $day_log_sub_two_builder->whereRaw($condition, $value);
            }
        }

        if ($param->clique_id) {
            $day_log_sub_one_builder->whereIn('game.clique_id', $param->clique_id);
            $day_log_sub_two_builder->whereIn('game.clique_id', $param->clique_id);
        }

        $day_log_sub_two_builder->whereRaw("( cost != 0 OR ori_cost != 0 )  AND (`game`.`plat_id` IN $plat_id_sql AND `agent`.`media_type_id` IN (1, 3, 4))");
        $day_log_sub_two_builder->groupBy(['tdate_1','t.platform','agent.agent_leader','agent.agent_group_id','agent.agent_id','agent.account_id','game.game_id','pw.payway_company']);
        $day_log_sub_one_builder->unionAll($day_log_sub_two_builder);

        $builder->withExpression('cost_union', $day_log_sub_one_builder);

        /* 账号对应的消耗主体及金额排序 */
        // pw_row 按天/按月+平台+账号+游戏+收款主体 排序号 按照这个维度只拿一次收款主体的金额 防止翻倍
        // cost_row 按天+平台+账号+游戏+渠道 排序号 按照这个维度只拿消耗金额 防止翻倍
        if ($param->aggregation_time === '分月') {
            $cost_union_sub_builder = $this->builder->from('cost_union');
            $cost_union_sub_builder
                ->selectRaw("
                *,
                row_number () over ( PARTITION BY YEAR_MONTH ( tdate ), platform, account_id, game_id, payway_company ORDER BY pay_money ) AS pw_row,
                row_number () over ( PARTITION BY tdate, platform, account_id, agent_id, game_id ORDER BY account_id, tdate, game_id ) AS cost_row 
            ");

            $sum_pay_money_builder = $this->builder->fromSub($cost_union_sub_builder, '');
            $sum_pay_money_builder
                ->selectRaw("
                *,
				sum( CASE WHEN pw_row = 1 THEN pay_money END ) over ( PARTITION BY YEAR_MONTH ( tdate ), `platform`, `account_id`, `payway_company` ) sum_pay_money
            ");

            $pay_money_rank_builder = $this->builder->fromSub($sum_pay_money_builder, '');
            $pay_money_rank_builder
                ->selectRaw("
                *,
			    DENSE_RANK () over ( PARTITION BY YEAR_MONTH ( tdate ), `platform`, `account_id` ORDER BY sum_pay_money DESC ) ranks 
            ");

            $acc_pw_builder = $this->builder->fromSub($pay_money_rank_builder, 'pay_money_rank');
            $acc_pw_builder
                ->selectRaw("
                    tdate_1,
                    sum( case when cost_row = 1 then cost_money END ) AS cost_money,
                    sum( case when cost_row = 1 then ori_money END ) AS ad_cost,
                    pay_money,
                    platform,
                    account_id,
                    group_concat( DISTINCT agent_id ORDER BY agent_id ) AS agent_id,
                    group_concat( DISTINCT agent_name ) AS agent_name,
                    group_concat( DISTINCT contract_game_name ) AS contract_game_name,
                    group_concat( DISTINCT clique_game ) AS clique_game,
                    group_concat( DISTINCT root_game ) AS root_game,
                    group_concat( DISTINCT main_game ) AS main_game,
                    group_concat( DISTINCT game ) AS game,
                    group_concat( DISTINCT game_id ORDER BY game_id ) AS game_id,
                    tdate AS date,
                    YEAR_MONTH(tdate) as m_date,
                    GROUP_CONCAT( DISTINCT CASE WHEN ranks = 1 THEN concat( payway_company, '-', sum_pay_money ) END ) AS max_payway_company,
                    GROUP_CONCAT( DISTINCT CASE WHEN ranks > 1 THEN concat( payway_company, '-', sum_pay_money ) END ) AS other_payway_company 
                ")
                ->groupBy(['account_id', 'm_date']);
        } else {
            $cost_union_sub_builder = $this->builder->from('cost_union');
            $cost_union_sub_builder
                ->selectRaw("
                *,
                row_number () over ( PARTITION BY YEAR_MONTH(tdate), platform, account_id, game_id, payway_company ORDER BY pay_money ) AS pw_row,
                row_number () over ( PARTITION BY tdate, platform, account_id, agent_id, game_id ORDER BY account_id, tdate, game_id ) AS cost_row 
            ");

            $sum_pay_money_builder = $this->builder->fromSub($cost_union_sub_builder, '');
            $sum_pay_money_builder
                ->selectRaw("
                *,
				sum( CASE WHEN pw_row = 1 THEN pay_money END ) over ( PARTITION BY YEAR_MONTH(`tdate_1`), `platform`, `account_id`, `payway_company` ) sum_pay_money
            ");

            $pay_money_rank_builder = $this->builder->fromSub($sum_pay_money_builder, '');
            $pay_money_rank_builder
                ->selectRaw("
                *,
			    DENSE_RANK () over ( PARTITION BY YEAR_MONTH(`tdate_1`), `platform`, `account_id` ORDER BY sum_pay_money DESC ) ranks 
            ");

            $builder->withExpression('pay_money_rank', $pay_money_rank_builder);
            $month_pw_builder = $this->builder->from('pay_money_rank');
            $month_pw_builder
                ->selectRaw("
                    account_id, 
                    YEAR_MONTH(tdate) AS m_date,
                    GROUP_CONCAT( DISTINCT CASE WHEN ranks = 1 THEN concat( payway_company, '-', sum_pay_money ) END ) AS max_payway_company,
                    GROUP_CONCAT( DISTINCT CASE WHEN ranks > 1 THEN concat( payway_company, '-', sum_pay_money ) END ) AS other_payway_company
                ")
                ->groupBy(['account_id', 'm_date']);

            $builder->withExpression('month_pw', $month_pw_builder);

            $acc_pw_builder = $this->builder->from('pay_money_rank');
            $acc_pw_builder
                ->selectRaw("
                    tdate_1,
                    sum( case when cost_row = 1 then cost_money END ) AS cost_money,
                    sum( case when cost_row = 1 then ori_money END ) AS ad_cost,
                    pay_money,
                    platform,
                    pay_money_rank.account_id,
                    group_concat( DISTINCT agent_id ORDER BY agent_id ) AS agent_id,
                    group_concat( DISTINCT agent_name ) AS agent_name,
                    group_concat( DISTINCT contract_game_name ) AS contract_game_name,
                    group_concat( DISTINCT clique_game ) AS clique_game,
                    group_concat( DISTINCT root_game ) AS root_game,
                    group_concat( DISTINCT main_game ) AS main_game,
                    group_concat( DISTINCT game ) AS game,
                    group_concat( DISTINCT game_id ORDER BY game_id ) AS game_id,
                    tdate AS date,
                    max_payway_company,
                    other_payway_company 
                ")
                ->leftJoin('month_pw', function (JoinClause $join) {
                    $join->on('pay_money_rank.account_id', '=', 'month_pw.account_id');
                    $join->whereRaw('YEAR_MONTH(pay_money_rank.tdate) = month_pw.m_date');
                })
                ->groupBy(['pay_money_rank.account_id', 'pay_money_rank.tdate']);
        }

        if ($param->platform) {
            $acc_pw_builder->whereIn('platform', $param->platform);
        }

        if ($param->account_id) {
            $acc_pw_builder->whereIn('pay_money_rank.account_id', $param->account_id);
        }

        if ($param->date) {
            $cost_date[0] = $param->date[0] . ' 00:00:00';
            $cost_date[1] = $param->date[1] . ' 23:59:59';
            $acc_pw_builder->whereBetween('tdate', $cost_date);
        }

        if ($media_type) {
            $acc_pw_builder->where('media_type', $media_type);
        }

        $builder->withExpression('day_log', $acc_pw_builder);

        return $builder;
    }
}
