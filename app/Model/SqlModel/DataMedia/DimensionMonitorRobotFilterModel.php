<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2020/04/16
 * Time: 10:24
 */

namespace App\Model\SqlModel\DataMedia;

use App\Constant\ADComposeFilterSqlMap;
use App\Constant\MediaType;
use App\Exception\AppException;
use App\Model\SqlModel\Zeda\ADMaterialPacketModel;
use App\Param\ADServing\ADComposeFilterParam;
use App\Param\ADServing\DimensionMonitorRobotFilterParam;
use Common\EnvConfig;
use Illuminate\Database\Query\Builder;
use Illuminate\Database\Query\JoinClause;

/**
 * Class DimensionMonitorRobotFilterModel
 * @package App\Model\SqlModel\DataMedia
 */
class DimensionMonitorRobotFilterModel extends AbstractDataMediaSqlModel
{
    /**
     * @param DimensionMonitorRobotFilterParam $param
     * @param $agent_permission
     * @param $game_permission
     * @return array
     */
    public function getFilter(DimensionMonitorRobotFilterParam $param, $agent_permission = -1, $game_permission = -1)
    {
        $date_time_range = [
            date('Y-m-d H:i:s', strtotime("-$param->range_hour hours")),
            date('Y-m-d H:i:s')
        ];

        $table = ADComposeFilterSqlMap::TABLE;

        $driver_builder = $this->builder->from($table['ad3_common_log']);
        $ad2_common_log = $this->builder->from($table['ad2_common_log']);
        $hour_data_log = $this->builder->from($table['hour_data_log']);
        $overview_log = $this->builder->from($table['overview_log']);
        $overview_log->where("{$table['overview_log']}.ad2_id", '>', 0);
        $reg_log = $this->builder->from($table['reg_log']);
        $reg_log->where("{$table['reg_log']}.action_id", '=', 2);
        $reg_log->where("{$table['reg_log']}.adgroup_id ", '!=', '');
        $main_builder = $this->builder->from('driver');

        // 组装驱动表
        $driver_builder->selectRaw("{$table['ad3_common_log']}.media_type");
        $driver_builder->selectRaw("{$table['ad3_common_log']}.platform");
        $driver_builder->selectRaw("agent_site.agent_leader as agent_leader");
        $driver_builder->selectRaw("{$table['ad3_common_log']}.account_id as dim_account_id");
        $driver_builder->selectRaw("{$table['ad3_common_log']}.creative_create_time");
        $driver_builder->selectRaw("{$table['ad3_common_log']}.{$param->dimension_exec_target_dim}_id");

        $driver_builder->join($table['ad2_common_log'], function (JoinClause $join) use ($param, $table) {
            $join->on("{$table['ad3_common_log']}.ad2_id", '=', "{$table['ad2_common_log']}.ad2_id");
            $join->on("{$table['ad3_common_log']}.media_type", '=', "{$table['ad2_common_log']}.media_type");
            $join->on("{$table['ad3_common_log']}.platform", '=', "{$table['ad2_common_log']}.platform");

            $join->where("{$table['ad3_common_log']}.media_type", '=', $param->media_type);
            $join->where("{$table['ad3_common_log']}.platform", '=', $param->platform);
            $join->whereBetween("{$table['ad3_common_log']}.creative_create_time", [date('Y-m-d 00:00:00', strtotime("-6 months")), date('Y-m-d H:i:s')]);
        });

        $this->injectAgentPermissionAndJoinAgentSite($driver_builder, $agent_permission, $table['ad2_common_log']);
        $this->injectGamePermissionAndJoinSiteGame($driver_builder, $game_permission, $table['ad2_common_log']);

        foreach ($param->ad3_common_log_filter as [$condition, $value]) {
            $driver_builder->whereRaw($condition, $value);
        }

        $driver_builder->whereBetween("{$table['ad3_common_log']}.creative_create_time", [date('Y-m-d 00:00:00', strtotime("-6 months")), date('Y-m-d H:i:s')]);

        $driver_builder->groupBy("{$table['ad3_common_log']}.{$param->dimension_exec_target_dim}_id");

        $main_builder->withExpression('driver', $driver_builder);

        $main_builder->orderBy('driver.creative_create_time', 'desc');


        if ($param->ad2_common_log_target) {
            $ad2_common_log->selectRaw("{$table['ad3_common_log']}.{$param->dimension_exec_target_dim}_id");

            foreach ($param->ad2_common_log_target as $target) {
                $ad2_common_log->selectRaw($target);
            }

            $ad2_common_log->join($table['ad3_common_log'], function (JoinClause $join) use ($param, $table) {
                $join->on("{$table['ad3_common_log']}.ad2_id", '=', "{$table['ad2_common_log']}.ad2_id");
                $join->on("{$table['ad3_common_log']}.media_type", '=', "{$table['ad2_common_log']}.media_type");
                $join->on("{$table['ad3_common_log']}.platform", '=', "{$table['ad2_common_log']}.platform");

                $join->where("{$table['ad3_common_log']}.media_type", '=', $param->media_type);
                $join->where("{$table['ad3_common_log']}.platform", '=', $param->platform);
                $join->whereBetween("{$table['ad3_common_log']}.creative_create_time", [date('Y-m-d 00:00:00', strtotime("-6 months")), date('Y-m-d H:i:s')]);
            });

            $this->injectAgentPermissionAndJoinAgentSite($ad2_common_log, $agent_permission, $table['ad2_common_log']);
            $this->injectGamePermissionAndJoinSiteGame($ad2_common_log, $game_permission, $table['ad2_common_log']);

            foreach ($param->ad2_common_log_filter as [$condition, $value]) {
                $ad2_common_log->whereRaw($condition, $value);
            }

            $ad2_common_log->groupBy(["{$table['ad3_common_log']}.{$param->dimension_exec_target_dim}_id"]);

            $main_builder->withExpression('ad2_common_log', $ad2_common_log);

            $main_builder->leftJoin('ad2_common_log', function (JoinClause $join) use ($param) {
                $join->on("driver.{$param->dimension_exec_target_dim}_id", '=', "ad2_common_log.{$param->dimension_exec_target_dim}_id");
            });
        }


        // 组装小时表
        if (!empty($param->hour_data_log_target)) {

            $param->hour_data_log_target = array_unique($param->hour_data_log_target);
            $param->main_target = array_unique($param->main_target);

            $hour_data_log->selectRaw("{$table['ad3_common_log']}.{$param->dimension_exec_target_dim}_id");

            foreach ($param->hour_data_log_target as $key => $value) {
                $hour_data_log->selectRaw($value);
            }

            $this->injectAgentPermissionAndJoinAgentSite($hour_data_log, $agent_permission, $table['hour_data_log']);
            $this->injectGamePermissionAndJoinGame($hour_data_log, $game_permission, $table['hour_data_log']);

            $hour_data_log->join($table['ad3_common_log'], function (JoinClause $join) use ($param, $table) {
                $join->on("{$table['hour_data_log']}.ad4_id", '=', "{$table['ad3_common_log']}.ad4_id");
                $join->on("{$table['hour_data_log']}.media_type", '=', "{$table['ad3_common_log']}.media_type");
                $join->on("{$table['hour_data_log']}.platform", '=', "{$table['ad3_common_log']}.platform");

                $join->where(["{$table['ad3_common_log']}.platform" => $param->platform]);
                $join->where(["{$table['ad3_common_log']}.media_type" => $param->media_type]);
                $join->whereBetween("{$table['ad3_common_log']}.creative_create_time", [date('Y-m-d 00:00:00', strtotime("-6 months")), date('Y-m-d H:i:s')]);
            });

            foreach ($param->hour_data_log_filter as [$condition, $value]) {
                $hour_data_log->whereRaw($condition, $value);
            }
            $hour_data_log->whereBetween("{$table['hour_data_log']}.cost_date", $date_time_range);

            $hour_data_log->groupBy(["{$table['ad3_common_log']}.{$param->dimension_exec_target_dim}_id"]);

            $main_builder->withExpression('hour_data_log', $hour_data_log);

            $main_builder->leftJoin('hour_data_log', function (JoinClause $join) use ($param) {
                $join->on("driver.{$param->dimension_exec_target_dim}_id", '=', "hour_data_log.{$param->dimension_exec_target_dim}_id");
            });
        }

        // 组装业务数据表
        if (!empty($param->overview_log_target)) {

            $overview_log->selectRaw("{$table['ad3_common_log']}.{$param->dimension_exec_target_dim}_id");

            foreach ($param->overview_log_target as $key => $value) {
                $overview_log->selectRaw($value);
            }

            $this->injectAgentPermissionAndJoinAgentSite($overview_log, $agent_permission, $table['overview_log']);
            $this->injectGamePermissionAndJoinGame($overview_log, $game_permission, $table['overview_log']);

            $overview_log->join($table['ad3_common_log'], function (JoinClause $join) use ($param, $table) {
                $join->on("{$table['overview_log']}.ad4_id", '=', "{$table['ad3_common_log']}.ad4_id");
                $join->on("{$table['overview_log']}.media_type", '=', "{$table['ad3_common_log']}.media_type");
                $join->on("{$this->v2_dim_agent_site_id_alias}.media_type_id", '=', "{$table['ad3_common_log']}.media_type");
                $join->on("{$table['overview_log']}.platform", '=', "{$table['ad3_common_log']}.platform");
                $join->whereBetween("{$table['ad3_common_log']}.creative_create_time", [date('Y-m-d 00:00:00', strtotime("-6 months")), date('Y-m-d H:i:s')]);
            });

            foreach ($param->overview_log_filter as [$condition, $value]) {
                $overview_log->whereRaw($condition, $value);
            }

            $overview_log->whereBetween("{$table['overview_log']}.log_date", $date_time_range);

            $overview_log->groupBy(["{$table['ad3_common_log']}.{$param->dimension_exec_target_dim}_id"]);

            $main_builder->withExpression('overview_log', $overview_log);

            $main_builder->leftJoin('overview_log', function (JoinClause $join) use ($param) {
                $join->on("driver.{$param->dimension_exec_target_dim}_id", '=', "overview_log.{$param->dimension_exec_target_dim}_id");
            });
        }

        // 组装用户注册表
        if (!empty($param->reg_log_target)) {

            $param->reg_log_target = array_unique($param->reg_log_target);
            $param->main_target = array_unique($param->main_target);

            $reg_log->selectRaw("{$table['ad3_common_log']}.{$param->dimension_exec_target_dim}_id");

            foreach ($param->reg_log_target as $key => $value) {
                $reg_log->selectRaw($value);
            }

            $this->injectAgentPermissionAndJoinAgentSite($reg_log, $agent_permission, $table['reg_log']);
            $this->injectGamePermissionAndJoinGame($reg_log, $game_permission, $table['reg_log']);

            $reg_log->join($table['ad3_common_log'], function (JoinClause $join) use ($param, $table) {
                $join->on("{$table['reg_log']}.ad4_id", '=', "{$table['ad3_common_log']}.ad4_id");
                $join->on("{$table['reg_log']}.platform", '=', "{$table['ad3_common_log']}.platform");
//                    $join->on("{$table['reg_log']}.ad_platform_id", '=', "{$table['ad3_common_log']}.media_type");
                $join->on("{$this->v2_dim_agent_site_id_alias}.media_type_id", '=', "{$table['ad3_common_log']}.media_type");

                $join->where("{$table['ad3_common_log']}.media_type", '=', $param->media_type);
                $join->where("{$table['ad3_common_log']}.platform", '=', $param->platform);
                $join->whereBetween("{$table['ad3_common_log']}.creative_create_time", [date('Y-m-d 00:00:00', strtotime("-6 months")), date('Y-m-d H:i:s')]);
            });

            foreach ($param->reg_log_filter as [$condition, $value]) {
                $reg_log->whereRaw($condition, $value);
            }

            $reg_log->whereBetween("{$table['reg_log']}.game_reg_time ", $date_time_range);

            $reg_log->groupBy(["{$table['ad3_common_log']}.{$param->dimension_exec_target_dim}_id"]);

            $main_builder->withExpression('reg_log', $reg_log);

            $main_builder->leftJoin('reg_log', function (JoinClause $join) use ($param) {
                $join->on("driver.{$param->dimension_exec_target_dim}_id", '=', "reg_log.{$param->dimension_exec_target_dim}_id");
            });
        }

        $main_builder->selectRaw("driver.media_type");
        $main_builder->selectRaw("driver.platform");
        $main_builder->selectRaw("driver.agent_leader");
        $main_builder->selectRaw("driver.creative_create_time");
        $main_builder->selectRaw("driver.{$param->dimension_exec_target_dim}_id");

        foreach ($param->main_target as $key => $value) {
            $main_builder->selectRaw($value);
        }

        foreach ($param->main_calc as [$condition, $value]) {
            !is_null($value) && $main_builder->havingRaw($condition, (array)$value, $param->condition);
        }

        if ($param->limit) {
            $main_builder->limit($param->limit);
        }

        return [
            'list' => $main_builder->get(),
            'sql' => $this->getSql($main_builder)
        ];
    }

    public function getFilterForMinutes(DimensionMonitorRobotFilterParam $param, $agent_permission = -1, $game_permission = -1)
    {
        $date_time_range = [
            date('Y-m-d H:i:s', strtotime("-$param->range_hour minutes")),
            date('Y-m-d H:i:s')
        ];

        $his_hour_data_log = $this->builder;
        $time_window_data_log = $this->builder;

        
    }


}
