<?php

namespace App\Model\SqlModel\DatahubLY;


use App\Utils\Helpers;
use Illuminate\Database\Query\JoinClause;

class V2DWDDayPayLogModel extends AbstractDatahubLYSqlModel
{
    protected $table = 'v2_dws_day_pay_log';

    /**
     * 获取每个游戏的指定时间内的总流水
     *
     * @param integer $start_date 开始时间 时间戳
     * @param integer $end_date   结束时间 时间戳
     *
     * @return \Illuminate\Support\Collection
     */
    public function getGameTotalPayMoney($start_date, $end_date, $platform = '')
    {
        $father_builder = $this->builder;
        $builder = $this->builder;
        $join_builder1 = $this->builder;
        $join_builder2 = $this->builder;

        $builder->selectRaw('t1.platform,t1.game_id,sum( pay_money ) AS game_pay_money,DATE_FORMAT( t1.pay_time, "%Y-%m-%d" ) as pay_date,
        sum(IF((t1.platform IN ( "TW" ) AND t1.pay_way_id = 69 ) OR ( t1.platform IN ( "" ) 
        AND t1.pay_way_id in( 3,14,33,35) ),pay_money,0 ) ) AS apple_pay_money,
		sum(IF((t1.platform IN ( "TW" ) AND t1.pay_way_id = 63 AND channel_id = 56 ) OR 
		( t1.platform IN ( "" ) AND t1.pay_way_id = 99  ),pay_money,0 ) ) AS yyb_pay_money,
		cast(sum(IF((t1.platform IN ( "TW" ) AND ( t1.pay_way_id != 69 AND ( t1.pay_way_id != 63 
		OR channel_id != 56 ) ) ) OR (t1.platform IN ( "" ) AND ( t1.pay_way_id not in( 3,14,33,35) 
		AND ( t1.pay_way_id != 99  ) ) ),pay_money * pay_way_proportion / 10000,0) ) AS INT ) AS pay_way_pay_money');
        $builder->from('v2_ods_pay_order_log AS t1');
        $builder->leftJoin('tanwan_datahub.v2_dim_pay_way_id as t2', function (JoinClause $join) {
            $join->on('t1.platform', '=', 't2.platform');
            $join->on('t1.pay_way_id', '=', 't2.pay_way_id');
        });
        $builder->join('tanwan_datahub.v2_dim_game_id as game', function (JoinClause $join) {
            $join->on('t1.platform', '=', 'game.platform');
            $join->on('t1.game_id', '=', 'game.game_id');
        });
        $builder->where('order_status_id', 1);
        $builder->where('test_order', '=', 0);
        $builder->whereBetween('pay_time', [date("Y-m-d 00:00:00", $start_date), date("Y-m-d 23:59:59", $end_date)]);
        $builder->groupBy(['t1.platform', 't1.game_id','pay_date']);
        if ($platform) {
            $builder->where('t1.platform', $platform);
        }

        $join_builder1->select('*')->from('tanwan_datahub.v2_dim_game_id');

        $join_builder2->selectRaw('t.platform, t.game_id, sum( money ) AS cost_money, t.tdate');
        $join_builder2->from('tanwan_datahub.v2_dwd_day_cost_log as t');
        $join_builder2->whereBetween('tdate', [date("Y-m-d 00:00:00", $start_date), date("Y-m-d 23:59:59", $end_date)]);
        $join_builder2->join('view_v2_dim_site_id as site', function (JoinClause $join) {
            $join->on('t.platform', '=', 'site.platform');
            $join->on('t.site_id', '=', 'site.site_id');
            $join->on('t.game_id', '=', 'site.game_id');
        });
        $join_builder2->join('v2_dim_game_id_for_channel_log as game', function (JoinClause $join) {
            $join->on('t.platform', '=', 'game.platform');
            $join->on('t.game_id', '=', 'game.game_id');
        });
        if ($platform) {
            $join_builder2->where('t.platform', $platform);
        }
        $join_builder2->groupBy(['t.platform', 't.game_id', 't.tdate']);

        $father_builder->joinSub($join_builder2, 'cost', function (JoinClause $join) {
            $join->on('t.platform', '=', 'cost.platform');
            $join->on('t.game_id', '=', 'cost.game_id');
            $join->on('t.pay_date', '=', 'cost.tdate');
        }, null, null, 'FULL OUTER');
        $father_builder->joinSub($join_builder1, 'g', function (JoinClause $join) {
            $join->whereRaw('IFNULL(t.platform, cost.platform) = g.platform');
            $join->whereRaw('IFNULL(t.game_id, cost.game_id) = g.game_id');
        });

        $father_builder->selectRaw('g.platform,g.game_id,
        IFNULL( t.game_pay_money, 0 ) AS game_pay_money,
        IFNULL( t.apple_pay_money, 0 ) AS apple_pay_money,
        IFNULL( t.yyb_pay_money, 0 ) AS yyb_pay_money,
        IFNULL( t.pay_way_pay_money, 0 ) AS pay_way_pay_money,
        IFNULL( t.pay_date, cost.tdate ) AS pay_date,
        g.root_game_id,
        g.root_game_name,
        g.main_game_id,
        g.main_game_name,
        g.game_name,
        g.is_channel,
        g.plat_id,
        g.plat_name,
        g.os,
        IFNULL( cost_money, 0 ) AS cost_money');
        $father_builder->fromSub($builder, 't');

        return $father_builder->get();
    }

}
