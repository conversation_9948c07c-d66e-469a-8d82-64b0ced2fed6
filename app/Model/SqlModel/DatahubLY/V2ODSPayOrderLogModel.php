<?php

namespace App\Model\SqlModel\DatahubLY;


use App\Utils\SqlParser;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Query\Builder;
use Illuminate\Database\Query\JoinClause;

class V2ODSPayOrderLogModel extends AbstractDatahubLYSqlModel
{
    protected $table = 'v2_ods_pay_order_log';

    public function getVerifyData($platform, $start_datetime, $end_datetime)
    {
        $builder = $this->builder;
        $builder = $builder->selectRaw('
            count(1) as count_num, 
            count(distinct uid) as count_distinct_uid, 
            count(distinct device_id) as count_distinct_imei, 
            sum(pay_money) as sum_money'
        )
            ->where('platform', $platform)
            ->whereBetween('pay_time', [$start_datetime, $end_datetime]);
        return $builder->first();
    }

    public function getDataByChannelPayMoney($platform, $game_id, $channel_id)
    {
        $field = 'sum(pay_money) AS total_pay_money,date(min(pay_time)) as start_date,date(max(pay_time)) as end_date';
        $total_pay_money = $this->builder->selectRaw($field)
            ->where('platform', $platform)
            ->where('game_id', $game_id)
            ->where('channel_id', $channel_id)
            ->first();
        if ($total_pay_money) {
            return (array)$total_pay_money;
        } else {
            return ['total_pay_money' => 0];
        }
    }

    /**
     * @param $start_time
     * @param $end_time
     * @param $platform
     * @param $game_id
     * @param $channel_id
     *
     * @return \Illuminate\Support\Collection
     */
    public function getDatePayMoney($start_time, $end_time, $platform, $game_id, $channel_id)
    {
        $t2_builder = $this->genBuilder($platform, 't');
        // t2拼接
        $t2_builder->selectRaw('DATE ( pay_time ) AS date');
        $t2_builder->selectRaw('sum(pay_money) as pay_money');
        $t2_builder->where('t.game_id', $game_id);
        $t2_builder->where('t.channel_id', $channel_id);

        $t2_builder->whereBetween('t.pay_time', [
            date("Y-m-d H:i:s", $start_time),
            date("Y-m-d H:i:s", $end_time)
        ]);

        $t2_builder->where('t.order_status_id', 1);

        $t2_builder->groupBy('date');
        $t2_builder->orderBy('date');
        return $t2_builder->get();
    }


    public function getChannelDayData($start_date, $end_date)
    {
        $builder = $this->builder;
        $builder->selectRaw('
            t1.platform,
            game.plat_id,
            game.plat_name,
            game.root_game_id,
            game.root_game_name,
            site.main_game_name,
            site.main_game_id,
            site.game_name,
            t1.game_id,
            t1.server_id,
            t1.server_name,
            site.channel_name,
            site.channel_id,
            sum( pay_money ) AS game_pay_money,
            DATE_FORMAT( t1.pay_time, "%Y-%m-%d" ) AS pay_date '
        );
        $builder->from('v2_ods_pay_order_log as t1');
        // 连维度表
        $dimension_sub = $this->builder->selectRaw("platform, main_game_id, main_game_name, game_id, game_name, channel_id, channel_name");
        $dimension_sub->from('view_v2_dim_site_id');
        $dimension_sub->groupBy(['platform', 'main_game_id', 'game_id', 'channel_id']);
        $builder->joinSub($dimension_sub, 'site', function (JoinClause $join) {
            $join->on('t1.platform', '=', 'site.platform');
            $join->on('t1.channel_id', '=', 'site.channel_id');
            $join->on('t1.game_id', '=', 'site.game_id');
        });

        $builder->join('tanwan_datahub_ly.v2_dim_game_id_for_channel_log as game', function (JoinClause $join) {
            $join->on('t1.platform', '=', 'game.platform');
            $join->on('t1.game_id', '=', 'game.game_id');
        });
        $builder->where('order_status_id', 1);
        $builder->whereBetween('pay_time', [date("Y-m-d 00:00:00", $start_date), date("Y-m-d 23:59:59", $end_date)]);

        $builder->groupBy('t1.platform', 'game.main_game_id', 't1.game_id', 't1.server_id', 'site.channel_id', 'pay_date');
        return $builder->get();

    }

    /**
     * @param $condition
     * @param $platform
     * @param $game_id
     * @param $channel_id
     *
     * @return array
     */
    public function getPayMoneyByGame($condition, $platform, $game_id, $channel_id)
    {
        $t1_builder = $this->builder;
        $t2_builder = $this->builder->from($this->table . ' as t')->where('platform', $platform);

        // 找出所有game_id
        $game_id_builder = $this->builder->from('tanwan_datahub_ly.view_v2_dim_site_id as site');
        if ($condition['game_range'] && is_array($condition['game_range'])) {
            if (!isset($condition['game_range']['main_game'])) {
                // 兼容老数据
                $game_id_builder->where('platform', $platform)->whereIn('main_game_id', $condition['game_range']);
            } else {
                foreach ($condition['game_range'] as $dimension) {
                    // 过滤空value
                    if (!$dimension['value']) {
                        continue;
                    }
                    [$game_condition, $value] = SqlParser::get($dimension);
                    $game_id_builder->whereRaw($game_condition, $value);
                }
            }

            $game_id_list = $game_id_builder->distinct()->get(['platform', 'game_id'])->groupBy('platform');

        } else {
            // 没有游戏范围说明参数不对，直接返回空数组
            return [];
        }

        // t1拼接
        $t1_builder->from($this->table . ' as t');
        if ($condition['date_range_type'] == 2) {
            $t1_builder->selectRaw('date_format( pay_time, "%Y%m" ) AS month');
            $t1_builder->groupBy('month');
            $t1_builder->orderBy('month');
        }

        // 拼接game_id条件
        $t1_builder->where(function (Builder $f_query) use ($game_id_list) {
            foreach ($game_id_list as $f_platform => $f_item) {
                $f_query->orWhere(function (Builder $sub_query) use ($f_platform, $f_item) {
                    /** @var $f_item Collection */
                    $sub_query->where('platform', $f_platform);
                    $sub_query->whereIn('game_id', $f_item->pluck('game_id'));
                });
            }
        });

        // 排除server
        if (!empty($condition['ly_channel_ids'])) {
            $t1_builder->whereNotIn('server_id', $condition['ly_channel_ids']);
        }

        if ($condition['date_range_value']) {
            $t1_builder->whereBetween('t.pay_time', [
                date("Y-m-d", strtotime($condition['date_range_value'][0])),
                date("Y-m-d", strtotime($condition['date_range_value'][1])) . ' 23:59:59'
            ]);
        }
        $t1_builder->selectRaw('sum(pay_money) as pay_money');
        $t1_list = $t1_builder->get();

        // t2拼接
        if ($condition['date_range_type'] == 2) {
            $t2_builder->selectRaw('date_format( pay_time, "%Y%m" ) AS month');
            $t2_builder->groupBy('month');
            $t2_builder->orderBy('month');
        }
        $t2_builder->where('game_id', $game_id)->where('channel_id', $channel_id);

        if ($condition['date_range_value']) {
            $t2_builder->whereBetween('t.pay_time', [
                date("Y-m-d", strtotime($condition['date_range_value'][0])),
                date("Y-m-d", strtotime($condition['date_range_value'][1])) . ' 23:59:59'
            ]);
        }
        $t2_builder->selectRaw('sum(pay_money) as main_game_pay_money');
        $t2_list = $t2_builder->get();

        return [
            'main_game_pay' => $t1_list,
            'channel_pay' => $t2_list,
            'main_game_sql' => $this->getSql($t1_builder),
            'channel_sql' => $this->getSql($t2_builder),
        ];
    }

    /**
     * 根据主游戏获取server
     *
     * @param        $main_game
     * @param string $keyword
     *
     * @return \Illuminate\Support\Collection
     */
    public function getServerByMainGame($main_game, $keyword = '')
    {
        // 找出所有game_id
        $game_id_builder = $this->builder->from('view_v2_dim_site_id as site');
        [$game_condition, $value] = SqlParser::get($main_game);
        $game_id_builder->whereRaw($game_condition, $value);
        $game_id_list = $game_id_builder->distinct()->get(['platform', 'game_id'])->groupBy('platform');

        $builder = $this->builder;
        // 拼接game_id条件
        $builder->where(function (Builder $query) use ($game_id_list) {
            foreach ($game_id_list as $f_platform => $f_item) {
                $query->orWhere(function (Builder $sub_query) use ($f_platform, $f_item) {
                    /** @var $f_item Collection */
                    $sub_query->where('platform', $f_platform);
                    $sub_query->whereIn('game_id', $f_item->pluck('game_id'));
                });
            }
        });
        if (!empty($keyword)) {
            $builder->where(function (Builder $query) use ($keyword) {
                $query->where('server_id', 'like', '%' . $keyword . '%');
                $query->orWhere('server_name', 'like', '%' . $keyword . '%');
            });
        }
        // server_id为空的暂时不要
        $builder->where('server_id', '>', 0);
        $builder->distinct()->selectRaw('server_id,server_name');
        return $builder->get();
    }

    private function genBuilder($platform, $alias = '')
    {
        $builder = $this->builder;
        if ($platform) {
            if ($alias) {
                $builder->where($alias . '.platform', $platform);
            } else {
                $builder->where('platform', $platform);
            }
        }
        if ($alias) {
            $builder->from($this->table . ' as ' . $alias);
        }
        return $builder;
    }
}
