<?php
/**
 * Created by PhpStorm.
 * User: Melody
 * Date: 2020/6/18
 * Time: 16:57
 */

namespace App\Model\SqlModel\Zeda;


use App\Param\DMS\SiteSettlementListParam;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Collection;

class SiteSettlementModel extends AbstractZedaSqlModel
{
    protected $table = 'site_settlement';

    const UN_SETTLEMENT = 1;
    const SETTLED = 2;

    /**
     * 根据时间范围删除未结算的数据
     *
     *
     * @param $start_date
     * @param $end_date
     *
     * @return int
     */
    public function deleteByDateRange($start_date, $end_date)
    {
        $builder = $this->builder;
        $builder->whereBetween('log_date', [$start_date, $end_date]);
        return $builder->where('type', 1)->delete();
    }

    public function addMultipleIgnore($insert_data)
    {
        return $this->builder->insertOrIgnore($insert_data);
    }

    public function updateSettlementBaseValue($start_date, $end_date)
    {
        $sql = "UPDATE site_settlement as ss JOIN site_date sd ON ss.platform = sd.platform AND ss.site_id = sd.site_id 
            AND ss.log_date >= sd.start_date 
            AND ss.log_date <= sd.end_date
            SET ss.settlement_base_value = sd.settlement_base_value,
                ss.bank_holder = sd.bank_holder,
                ss.bank_card_number = sd.bank_card_number,
                ss.bank_name = sd.bank_name
            where ss.type = 1 and log_date BETWEEN '$start_date'  and '$end_date'";
        $this->connection->update($sql);
    }

    /**
     * 获取结算列表
     *
     * @param SiteSettlementListParam $param
     *
     * @return array
     */
    public function getList(SiteSettlementListParam $param)
    {
        $builder = $this->genBuilder($param);

        $builder->selectRaw("site.*,game.contract_game_name, (IF(settlement_type = 'cpa',log_value * settlement_base_value * (1-tax_rate),
		    log_value * settlement_base_value * ( 1-tax_rate ) * ( 1-channel_fee_rate ))) AS settlement_money,
		    log_value * ( 1-channel_fee_rate ) as exclude_channel_fee_money,
		    alg.agent_leader_group_name,
		    IFNULL(algp.project_team_id, 0) as project_team_id,
		    IFNULL(live_order.live_order_ids, '') AS live_order_ids,
		    p.payway_company as our_company
		    ");

        return [
            'list' => $builder->limit($param->limit)->orderByDesc('log_date')->orderBy('agent_id')->orderBy('site_id')->get(),
            'sql'  => $this->getSql($builder),
        ];
    }

    public function getListTmp()
    {
        return $this->builder->where('type', self::SETTLED)->whereBetween('log_date', ['2025-03-19', '2025-05-14'])->get(['id'])->pluck('id')->toArray();
    }

    public function getSumData(SiteSettlementListParam $param)
    {
        $builder = $this->genBuilder($param);
        $builder->selectRaw("
        sum(site.log_value) as log_value,
	    sum(site.log_true_value) as log_true_value,
	    sum(IF(settlement_type = 'cpa',
			log_value * settlement_base_value * ( 1-tax_rate ),
            log_value * settlement_base_value * ( 1-tax_rate ) * ( 1-channel_fee_rate ))) AS settlement_money,
        sum(site.log_value * ( 1-channel_fee_rate )) as exclude_channel_fee_money"
        );
        return $builder->get()->first();
    }

    private function genBuilder(SiteSettlementListParam $param)
    {
        $builder = $this->builder->from("$this->table as site");


        $builder->where('site.type', $param->type);
        if ($param->start_date && $param->end_date) {
            $builder->whereBetween('log_date', [$param->start_date, $param->end_date]);
        }

        if ($param->settlement_type) {
            $builder->where('settlement_type', $param->settlement_type);
        }

        if ($param->live_order_ids) {
            if (strtolower($param->live_order_ids) == 'null') {
                $builder->whereRaw('live_order.live_order_ids IS NULL');
            } else {
                $builder->where('live_order.live_order_ids', 'like', "%{$param->live_order_ids}%");
            }
        }

        if ($param->bank_holder) {
            $builder->where('site.bank_holder', 'like', "%{$param->bank_holder}%");
        }
        if ($param->our_company) {
            $builder->where('p.payway_company', 'like', "%{$param->our_company}%");
        }

        // 处理维度筛选
        foreach ($param->dimension_filter as [$condition, $value]) {
            $builder->whereRaw($condition, $value);
        }

        $builder->join('v2_dim_game_id as game', function (JoinClause $join) {
            $join->on('game.platform', '=', 'site.platform');
            $join->on('game.game_id', '=', 'site.game_id');
        });
        $builder->leftJoin('site_settlement_live_anchor_orders as live_order', function (JoinClause $join) {
            $join->on('live_order.platform', '=', 'site.platform');
            $join->on('live_order.site_id', '=', 'site.site_id');
            $join->on('live_order.first_live_date', '=', 'site.log_date');
        });
        // 权限
        $this->injectAgentPermissionAndJoinAgent($builder, $param->agent_permission, 'site');
        // 连负责人分组表
        $builder->leftJoin('v2_dim_agent_leader_group as alg', function (JoinClause $join) {
            $join->on('site.agent_leader', '=', 'alg.agent_leader');
        });
        // 再用操作人连一次表
        $builder->leftJoin('v2_dim_agent_leader_group as algp', function (JoinClause $join) {
            $join->on('site.operator', '=', 'algp.agent_leader');
        });
        // 连一下结算主体表
        $builder->leftJoin('dwd_live_pay_way_company as p', function (JoinClause $join) {
            $join->on('site.platform', '=', 'p.platform');
            $join->on('site.game_id', '=', 'p.game_id');
            $join->whereRaw("DATE_FORMAT(site.log_date, '%Y-%m') = DATE_FORMAT(p.tdate, '%Y-%m')");
        });
        return $builder;
    }

    /**
     * 根据id获取列表
     *
     * @param array $ids
     * @param int $type
     * @param bool $company 是否连结算表
     * @return Collection
     */
    public function getListByIds(array $ids, int $type = self::UN_SETTLEMENT, bool $company = false)
    {
        $builder = $this->builder->from($this->table . ' as site');

        $builder->whereIn('id', $ids);
        $builder->selectRaw("site.*, (IF(settlement_type = 'cpa',log_value * settlement_base_value * (1-tax_rate),
		    log_value * settlement_base_value * ( 1-tax_rate ) * ( 1-channel_fee_rate ))) AS settlement_money,
		    site.log_value * ( 1-channel_fee_rate ) as exclude_channel_fee_money,
		    alg.agent_leader_group_name,
		    IFNULL(algp.project_team_id, 0) as project_team_id,
            IFNULL(live_order.live_order_ids, '') AS live_order_ids,
            IFNULL(live_order.trade_nos, '') AS trade_nos
		    ");
        $builder->where('type', $type);
        $builder->leftJoin('site_settlement_live_anchor_orders as live_order', function (JoinClause $join) {
            $join->on('live_order.platform', '=', 'site.platform');
            $join->on('live_order.site_id', '=', 'site.site_id');
            $join->on('live_order.first_live_date', '=', 'site.log_date');
        });
        // 连负责人分组表
        $builder->leftJoin('v2_dim_agent_leader_group as alg', function (JoinClause $join) {
            $join->on('site.agent_leader', '=', 'alg.agent_leader');
        });
        // 再用操作人连一次表
        $builder->leftJoin('v2_dim_agent_leader_group as algp', function (JoinClause $join) {
            $join->on('site.operator', '=', 'algp.agent_leader');
        });
        if ($company) {
            // 连一下结算主体表
            $builder->leftJoin('dwd_live_pay_way_company as p', function (JoinClause $join) {
                $join->on('site.platform', '=', 'p.platform');
                $join->on('site.game_id', '=', 'p.game_id');
                $join->whereRaw("DATE_FORMAT(site.log_date, '%Y-%m') = DATE_FORMAT(p.tdate, '%Y-%m')");
            });
            $builder->selectRaw("p.payway_company as our_company");
        }


        return $builder->get();
    }

    public function getListByIdsWithNetSettlementAmount(array $ids)
    {
        // type 写死
        $type = SiteSettlementModel::SETTLED;

        $builder = $this->builder->from($this->table . ' as site');
        $this->joinAgent($builder, 'site');

        $builder->whereIn('id', $ids);
        $builder->selectRaw("site.*, (IF(settlement_type = 'cpa',log_value * settlement_base_value * (1-tax_rate),
		    log_value * settlement_base_value * ( 1-tax_rate ) * ( 1-channel_fee_rate ))) AS settlement_money");
        $builder->where('type', $type);

        // 连一下结算主体表
        $builder->leftJoin('dwd_live_pay_way_company as p', function (JoinClause $join) {
            $join->on('site.platform', '=', 'p.platform');
            $join->on('site.game_id', '=', 'p.game_id');
            $join->whereRaw("DATE_FORMAT(site.log_date, '%Y-%m') = DATE_FORMAT(p.tdate, '%Y-%m')");
        });
        $builder->selectRaw("p.payway_company as our_company");
        // 获取不含税金额
        $f_builder = $this->builder;
        $f_builder->selectRaw("*,settlement_money / (1 +
                           CASE
                               WHEN tax_rate = 0 THEN 0.06
                               WHEN tax_rate = 0.036 THEN 3
                               WHEN tax_rate = 0.056 THEN 1
                               ELSE 0
                               END) AS net_settlement_amount");
        $f_builder->fromSub($builder, 't1');

        return $f_builder->get();
    }

    public function updateType(array $ids, $type, $operator, $desc = '')
    {
        $builder = $this->builder;

        $builder->whereIn('id', $ids);

        $update_data = [
            'type'     => $type,
            'operator' => $operator
        ];

        if ($desc) {
            $update_data['remark'] = $desc;
        }
        return $builder->update($update_data);
    }
}
