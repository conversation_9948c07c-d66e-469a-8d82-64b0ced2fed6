<?php

namespace App\Model\SqlModel\Zeda;


use App\Param\ADServing\DimensionMonitorRobotParam;
use App\Param\ADServing\DimensionMonitorRobotSearchParam;
use Illuminate\Database\Query\Builder;

class DimensionMonitorRobotModel extends AbstractZedaSqlModel
{
    protected $table = 'dimension_monitor_robot';

    public function getExecList()
    {
        return $this->builder->where('status', '!=', 0)->get();
    }


    public function getList(DimensionMonitorRobotSearchParam $param, $user_list)
    {
        $builder = $this->builder->where('status', '!=', 0);
        if ($user_list) {
            $builder->whereIn('creator', $user_list);
        }

        if ($param->id) {
            $builder->where(['id' => $param->id]);
        }

        if ($param->media_type) {
            $builder->where(['media_type' => $param->media_type]);
        }

        if ($param->media_agent_type) {
            $builder->where(['media_agent_type' => $param->media_agent_type]);
        }

        if ($param->name) {
            $builder->where('name', 'like', "%$param->name%");
        }

        if ($param->creator) {
            $builder->where('creator', 'like', "%$param->creator%");
        }

        $builder->orderByDesc('create_time');

        $total = $builder->count();
        if ($total === 0) {
            return [
                'total' => $total,
                'list' => collect()
            ];
        } else {
            return [
                'total' => $total,
                'list' => $builder->forPage($param->page, $param->rows)->get()
            ];
        }
    }

    /**
     * @param DimensionMonitorRobotParam $data
     * @return int
     */
    public function addDimensionMonitorRobot(DimensionMonitorRobotParam $data)
    {
        return $this->builder->insertGetId($data->toData());
    }

    /**
     * @param $id
     * @param DimensionMonitorRobotParam $data
     * @param array $user_list
     * @return int
     */
    public function updateDimensionMonitorRobot($id, DimensionMonitorRobotParam $data, $user_list = [])
    {
        $builder = $this->builder;
        $data = $data->toData();
        $where = [
            'id' => $id,
        ];
        if ($user_list) {
            $builder->whereIn('creator', $user_list);
        }
        return $builder->where($where)->update($data);
    }

}
