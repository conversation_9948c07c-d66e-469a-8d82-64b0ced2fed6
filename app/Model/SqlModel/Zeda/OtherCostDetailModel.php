<?php

namespace App\Model\SqlModel\Zeda;

use App\Model\SqlModel\Database\ZDBuilder;
use App\Model\SqlModel\Zeda\AbstractZedaSqlModel;
use App\Param\DMS\OtherCostDetailListExportParam;
use App\Param\DMS\OtherCostDetailListParam;
use App\Service\PermissionService;
use Illuminate\Database\Query\Builder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Collection;

class OtherCostDetailModel extends AbstractZedaSqlModel
{
    protected $table = 'other_cost_config_detail';

    // money_type 费用类型
    const LICENSE_COST = 'license_cost';
    const ENDORSEMENT_MONEY = 'endorsement_money';
    const SERVER_COST = 'server_cost';
    const BRAND_PROMOTION_COST = 'brand_promotion_cost';
    const OUTSOURCING_COST = 'outsourcing_cost';
    const TEAM_COST = 'team_cost';
    const PUBLIC_COST = 'public_cost';
    const WORKING_HOURS_COST = 'working_hours_cost';

    const MONEY_TYPE = [
        self::LICENSE_COST => '授权金',
        self::ENDORSEMENT_MONEY => '代言费',
        self::SERVER_COST => '服务器费用',
        self::BRAND_PROMOTION_COST => '宣发费',// 原品宣费
        self::OUTSOURCING_COST => '外包费',
        self::TEAM_COST => '团队费用',
        self::PUBLIC_COST => '公摊费用',
        self::WORKING_HOURS_COST => '直接工时成本',
    ];

    /**
     * @param OtherCostDetailListParam | OtherCostDetailListExportParam $param
     * @return Collection
     */
    public function getOtherCostDetailList($param): Collection
    {
        $service = new PermissionService();
        $permission_data = $service->getLoginUserDMSDataPermission();
        $game_permission = $permission_data['game_permission'];

        $sub_builder = $this->genOtherCostDetailListBuilder($param);
        $this->injectGamePermission($sub_builder, $game_permission);
        $sub_builder->select('other_cost_config_id')->distinct();
        $sub_builder_sql = $this->getSql($sub_builder);

        $builder = $this->builder->from('other_cost_config');
        $builder->select(['*']);
        $builder->whereRaw("id in ({$sub_builder_sql})");
        if ($param->agency) { // 其实这个查询最好通过上面的$sub_builder查询出来，保持和remark字段一样，但是detail没写入agency字段，后期优化改掉
            $builder->where('other_cost_config', 'like', "%{$param->agency}%");
        }
        $builder->orderBy("update_time", 'desc');

//        echo $this->getSql($builder);die;
        return $builder->get();
    }

    /**
     * @param OtherCostDetailListParam | OtherCostDetailListExportParam $param
     * @return ZDBuilder
     */
    public function genOtherCostDetailListBuilder($param): \App\Model\SqlModel\Database\ZDBuilder
    {
        $builder = $this->builder;
        $builder->from($this->table . ' as detail');

        if ($param->platform) {
            $builder->where('detail.platform', $param->platform);
        }

        if ($param->root_game_id) {
            $builder->where('detail.root_game_id', $param->root_game_id);
        }

        if ($param->money_type) {
            $builder->whereIn('detail.money_type', $param->money_type);
        }

        if ($param->cost_month) {
            $builder->whereBetween('detail.cost_month', $param->cost_month);
        }

        if ($param->remark) {
            $builder->where('detail.remark', "like", "%{$param->remark}%");
        }

        if ($param->project_team_ids) {
            $builder->where(function (Builder $query) use ($param) {
                foreach ($param->project_team_ids as $project_team_id) {
                    $query->orWhereRaw("JSON_CONTAINS(project_team_ids, '[{$project_team_id}]') ");
                }
            });
        }

        return $builder;
    }


    /**
     * @param $insert_data
     * @return void
     */
    public function addMultiple($insert_data)
    {
        // mysql长度限制,分批处理
        array_map(function ($item) {
            $this->builder->insertUpdate($item);
        }, array_chunk($insert_data, 2000));
    }

    /**
     * @param $other_cost_config_id
     * @return int
     */
    public function deleteByOtherConfigId($other_cost_config_id): int
    {
        return $this->builder->where('other_cost_config_id', $other_cost_config_id)->delete();
    }
}