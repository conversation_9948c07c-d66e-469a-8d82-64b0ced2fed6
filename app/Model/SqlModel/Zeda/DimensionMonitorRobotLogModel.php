<?php

namespace App\Model\SqlModel\Zeda;

use App\Param\ADServing\DimensionMonitorRobotLogParam;
use App\Param\ADServing\DimensionMonitorRobotLogSearchParam;

class DimensionMonitorRobotLogModel extends AbstractZedaSqlModel
{
    protected $table = 'dimension_monitor_robot_log';

    public function getList(DimensionMonitorRobotLogSearchParam $param)
    {
        $builder = $this->builder;

        if ($param->robot_id) {
            $builder->where('robot_id', $param->robot_id);
        }

        if ($param->is_effective) {
            $builder->where('is_effective', $param->is_effective);
        }

        if ($param->target_value) {
            $builder->whereIn('target_value', $param->target_value);
        }

        if ($param->log_time) {
            $builder->whereBetween('log_time', $param->log_time);
        }

        $builder->orderByDesc('id');

        $total = $builder->count();
        if ($total === 0) {
            return [
                'total' => $total,
                'list' => collect()
            ];
        } else {
            return [
                'total' => $total,
                'list' => $builder->forPage($param->page, $param->rows)->get()
            ];
        }
    }

    /**
     * @param DimensionMonitorRobotLogParam $param
     * @return bool
     */
    public function log(DimensionMonitorRobotLogParam $param): bool
    {
        return $this->builder->insert($param->toData());
    }
}
