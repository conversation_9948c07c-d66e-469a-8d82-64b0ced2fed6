<?php
/**
 * User: zzh
 * Date: 2019/6/10
 * Time: 14:17
 */

namespace App\Model\RedisModel;

use Redis;
use RedisException;

class DimensionMonitorRobotFrequencyModel extends AbstractRedisModel
{

    public function setFrequencyLockForRobot($robot_id, $min)
    {
        $this->redis->setex($this->getFrequencyLockForRobotKey($robot_id), ($min * 60) - 10, 1);
    }

    public function getFrequencyLockForRobot($robot_id)
    {
        return $this->redis->get($this->getFrequencyLockForRobotKey($robot_id));
    }

    private function getFrequencyLockForRobotKey($robot_id): string
    {
        return "dimension_monitor_robot_frequency_lock-robot_id:$robot_id";
    }

}
