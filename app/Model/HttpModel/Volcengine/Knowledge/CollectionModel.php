<?php

namespace App\Model\HttpModel\Volcengine\Knowledge;

class CollectionModel extends AbstractKnowledgeModel
{
    protected $base_path = '/api/knowledge/collection/';


    /**
     * 知识库做在线检索。
     *
     * @param $resource_id
     * @param $query
     * @param  $doc_filter
     * @param $pre_processing
     * @param float $dense_weight
     * @param int $limit
     * @param array $post_processing
     * @return array
     * @see https://www.volcengine.com/docs/84313/1350012   文档链接
     *
     * 例子：
     * doc_filter = {
     * "op": "must",
     * "field": "doc_id",
     * "conds": ["tos_doc_id_123", "tos_doc_id_456"]
     * }
     */
    public function searchKnowledge($resource_id, $query, $doc_filter, $pre_processing, int $limit = 10, float $dense_weight = 0.5, array $post_processing = [])
    {
        $body = [
            'resource_id'    => $resource_id,
            'query'          => $query,
            'limit'          => $limit,
            'pre_processing' => $pre_processing,
            'query_param'    => ['doc_filter' => $doc_filter],
            'dense_weight'   => $dense_weight,
        ];
        if ($post_processing) {
            $body['post_processing'] = $post_processing;
        }

        $body_json = json_encode($body);
        $sub_path = 'search_knowledge';
        $authorization_header = $this->getAuthorizationHeader("POST", $this->base_path . $sub_path, [], $body_json);

        $api = 'https://' . self::HOST . $this->base_path . $sub_path;

        return $this->post($api, $body_json, [
            'header' => $authorization_header,
            'log'    => true,
        ]);
    }
}