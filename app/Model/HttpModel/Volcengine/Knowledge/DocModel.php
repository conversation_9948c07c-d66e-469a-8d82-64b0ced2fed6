<?php

namespace App\Model\HttpModel\Volcengine\Knowledge;

use App\Constant\Environment;
use App\Exception\AppException;
use Common\EnvConfig;

class DocModel extends AbstractKnowledgeModel
{
    protected $base_path = '/api/knowledge/doc/';


    public function info($doc_id, $resource_id = self::SUMMARY_RESOURCE_ID)
    {
        $body = [
            'doc_id'      => $doc_id,
            'resource_id' => $resource_id,
        ];

        $body_json = json_encode($body);
        $sub_path = 'info';
        $authorization_header = $this->getAuthorizationHeader("POST", $this->base_path . $sub_path, [], $body_json);

        $api = 'https://' . self::HOST . $this->base_path . $sub_path;

        return $this->post($api, $body_json, [
            'header' => $authorization_header,
            'log'    => true,
        ]);
    }

    public function add($doc_id, $doc_name, $url, $meta = [], $doc_type = 'markdown', $resource_id = self::SUMMARY_RESOURCE_ID)
    {
        if (EnvConfig::ENV !== Environment::PROD) {
            throw new AppException('非生产环境无法上传知识库');
        }
        $body = [
            'doc_id'      => $doc_id,
            'resource_id' => $resource_id,
            'add_type'    => 'url',
            'doc_name'    => $doc_name,
            'doc_type'    => $doc_type,
            'url'         => $url,
        ];
        if ($meta) {
            $body['meta'] = $meta;
        }

        $body_json = json_encode($body);
        $sub_path = 'add';
        $authorization_header = $this->getAuthorizationHeader("POST", $this->base_path . $sub_path, [], $body_json);

        $api = 'https://' . self::HOST . $this->base_path . $sub_path;

        return $this->post($api, $body_json, [
            'header' => $authorization_header,
            'log'    => true,
        ]);
    }

    public function updateMeta($doc_id, $meta, $resource_id = self::SUMMARY_RESOURCE_ID)
    {
        $body = [
            'doc_id'      => $doc_id,
            'resource_id' => $resource_id,
            'meta'        => $meta,
        ];

        $body_json = json_encode($body);
        $sub_path = 'update_meta';
        $authorization_header = $this->getAuthorizationHeader("POST", $this->base_path . $sub_path, [], $body_json);

        $api = 'https://' . self::HOST . $this->base_path . $sub_path;

        return $this->post($api, $body_json, [
            'header' => $authorization_header,
            'log'    => true,
        ]);
    }

    public function delete($doc_id, $resource_id = self::SUMMARY_RESOURCE_ID)
    {
        $body = [
            'doc_id'      => $doc_id,
            'resource_id' => $resource_id,
        ];

        $body_json = json_encode($body);
        $sub_path = 'delete';
        $authorization_header = $this->getAuthorizationHeader("POST", $this->base_path . $sub_path, [], $body_json);

        $api = 'https://' . self::HOST . $this->base_path . $sub_path;

        return $this->post($api, $body_json, [
            'header' => $authorization_header,
            'log'    => true,
        ]);
    }
}