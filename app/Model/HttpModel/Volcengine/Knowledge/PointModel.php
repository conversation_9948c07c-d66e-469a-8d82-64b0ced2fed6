<?php

namespace App\Model\HttpModel\Volcengine\Knowledge;

class PointModel extends AbstractKnowledgeModel
{
    protected $base_path = '/api/knowledge/point/';


    public function add($doc_id, $content, $chunk_type = 'text')
    {
        $body = [
            'doc_id'      => $doc_id,
            'resource_id' => self::SUMMARY_RESOURCE_ID,
            'chunk_type'  => $chunk_type,
            'content'     => $content,
        ];

        $body_json = json_encode($body);
        $sub_path = 'add';
        $authorization_header = $this->getAuthorizationHeader("POST", $this->base_path . $sub_path, [], $body_json);

        $api = 'https://' . self::HOST . $this->base_path . $sub_path;

        return $this->post($api, $body_json, [
            'header' => $authorization_header,
            'log'    => true,
        ]);
    }

    public function list($doc_id)
    {
        $body = [
            'doc_ids'     => [$doc_id],
            'resource_id' => self::SUMMARY_RESOURCE_ID,
        ];

        $body_json = json_encode($body);
        $sub_path = 'list';
        $authorization_header = $this->getAuthorizationHeader("POST", $this->base_path . $sub_path, [], $body_json);

        $api = 'https://' . self::HOST . $this->base_path . $sub_path;

        return $this->post($api, $body_json, [
            'header' => $authorization_header,
            'log'    => true,
        ]);
    }

    public function update($point_id, $content)
    {
        $body = [
            'point_id'    => $point_id,
            'resource_id' => self::SUMMARY_RESOURCE_ID,
            'content'     => $content,
        ];

        $body_json = json_encode($body);
        $sub_path = 'update';
        $authorization_header = $this->getAuthorizationHeader("POST", $this->base_path . $sub_path, [], $body_json);

        $api = 'https://' . self::HOST . $this->base_path . $sub_path;

        return $this->post($api, $body_json, [
            'header' => $authorization_header,
            'log'    => true,
        ]);
    }

    public function delete($point_id)
    {
        $body = [
            'point_id'    => $point_id,
            'resource_id' => self::SUMMARY_RESOURCE_ID,
        ];

        $body_json = json_encode($body);
        $sub_path = 'delete';
        $authorization_header = $this->getAuthorizationHeader("POST", $this->base_path . $sub_path, [], $body_json);

        $api = 'https://' . self::HOST . $this->base_path . $sub_path;

        return $this->post($api, $body_json, [
            'header' => $authorization_header,
            'log'    => true,
        ]);
    }
}