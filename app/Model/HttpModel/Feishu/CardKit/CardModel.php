<?php

namespace App\Model\HttpModel\Feishu\CardKit;

class CardModel extends AbstractCardKitModel
{
    const CARD_URL = self::URL . 'cards';


    /**
     * 创建卡片实体
     *
     * @param $tenant_access_token
     * @param $data
     * @return array
     */
    public function cards($tenant_access_token, $data)
    {
        $body = json_encode([
            'type' => 'card_json',// 固定值
            'data' => $data,
        ]);

        return $this->post(self::CARD_URL, $body, [
            'header' => [
                "Authorization: Bearer {$tenant_access_token}",
                'Content-Type: application/json'
            ],
            'log'    => true,
        ]);
    }

    /**
     * 流式更新接口
     *
     * @param $tenant_access_token
     * @param $card_id
     * @param $content
     * @param $sequence
     * @param string $element_id
     * @return array
     */
    public function content($tenant_access_token, $card_id, $content, $sequence, string $element_id = 'markdown_1')
    {
        return $this->customerRequest(self::CARD_URL . "/$card_id/elements/$element_id/content", 'PUT', json_encode([
            'content'  => $content,
            'sequence' => $sequence,
        ]), [
            'header' => [
                "Authorization: Bearer {$tenant_access_token}",
                'Content-Type: application/json; charset=utf-8'
            ],
            'log'    => true,
        ]);
    }

    public function cardSettings($tenant_access_token, $card_id, $settings, $sequence)
    {
        $api = self::CARD_URL . "/$card_id/settings";
        return $this->customerRequest($api, 'PATCH', json_encode([
            'settings' => $settings,
            'sequence' => $sequence,
        ]), [
            'header' => [
                "Authorization: Bearer {$tenant_access_token}",
                'Content-Type: application/json; charset=utf-8'
            ],
            'log'    => true,
        ]);
    }
}