<?php

namespace App\Model\HttpModel\Bpm\WorkProjectInst;

class WorkProjectInstModel extends AbstractWorkProjectInstModel
{
    const UPDATE_ANCHOR_COOPERATE_LIVE_ORDER_URL = parent::URL . 'updateAnchorCooperateLiveOrder';
    const UPDATE_ANCHOR_COOPERATE_SETTLEMENT_URL = parent::URL . 'updateAnchorCooperateSettlement';
    const UPDATE_ANCHOR_COOPERATE_SPECIAL_FUND_URL = parent::URL . 'updateAnchorCooperateSpecialFund';
    const SYNC_LIVE_DEMAND_APP_NAME_URL = parent::URL . 'syncLiveDemandAppNameList';
    const SYNC_ROOT_GAME_LIST_URL = parent::URL . 'syncRootGameList';
    const ADD_LIVE_COMPANY_URL = parent::URL . 'addLiveCompany';
    const ADD_LIVE_ANCHOR_URL = parent::URL . 'addLiveAnchor';

    /**
     * 同步星图订单给BPM主播合作管理实例
     * @param $data
     * @return array
     */
    public function updateAnchorCooperateLiveOrder($data)
    {
        return $this->post(self::UPDATE_ANCHOR_COOPERATE_LIVE_ORDER_URL, $data, [
            'log' => true,
        ]);
    }

    /**
     * 同步结算订单给BPM主播合作管理实例
     * @param $data
     * @return array
     */
    public function updateAnchorCooperateSettlement($data)
    {
        return $this->post(self::UPDATE_ANCHOR_COOPERATE_SETTLEMENT_URL, $data, [
            'log' => true,
        ]);
    }

    /**
     * 同步主播费用给BPM主播合作管理实例
     * @param $data
     * @return array
     */
    public function updateAnchorCooperateSpecialFund($data)
    {
        return $this->post(self::UPDATE_ANCHOR_COOPERATE_SPECIAL_FUND_URL, $data, [
            'log' => true,
        ]);
    }

    /**
     * 同步游戏应用名称列表给BPM游戏应用管理实例
     * @param $data
     * @return array
     */
    public function syncLiveDemandAppNameList($data)
    {
        return $this->post(self::SYNC_LIVE_DEMAND_APP_NAME_URL, $data, [
            'log' => true,
            'timeout' => 300
        ]);
    }

    /**
     * 添加BPM机构实例
     * @param $data
     * @return array
     */
    public function addLiveCompany($data)
    {
        return $this->post(self::ADD_LIVE_COMPANY_URL, $data, [
            'log' => true,
        ]);
    }

    /**
     * 添加BPM主播实例
     * @param $data
     * @return array
     */
    public function addLiveAnchor($data)
    {
        return $this->post(self::ADD_LIVE_ANCHOR_URL, $data, [
            'log' => true,
        ]);
    }


    /**
     * 同步根游戏到BPM
     * @param $data
     * @return array
     */
    public function syncRootGameList($data)
    {
        return $this->post(self::SYNC_ROOT_GAME_LIST_URL, $data, [
            'timeout' => 300
        ]);
    }
}
