<?php

namespace App\Model\HttpModel\OpenAI;


/**
 * 所谓DeepSeek，其实也是接入的火山的DeepSeek，
 * 所以这里可以跟火山的模型通用，包括豆包那一堆模型
 */
class OpenAIDeepSeekModel extends AbstractOpenAIModel
{
    // DeepSeek OpenAI 固定配置
    const URL = 'https://ark.cn-beijing.volces.com/api/v3/chat/completions';
    const API_KEY = '87172cf8-2858-4692-a039-14405818caa1';

    // DeepSeek V3
    const V3_MODEL = 'ep-20250208174013-grmnz';

    // DeepSeek R1
    const R1_MODEL = 'ep-20250208141207-9g9dm';

    // Doubao-Seed-1.6-thinking
    const DOUBAO_MODEL = 'ep-20250806143550-wlqfh';


    /**
     * 初始化DeepSeek OpenAI配置
     *
     * @return void
     */
    protected function initializeConfig(): void
    {
        $this->url = self::URL;
        $this->api_key = self::API_KEY;
    }

    /**
     * 获取DeepSeek的请求头
     *
     * @return array
     */
    protected function getHeaders(): array
    {
        return [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $this->api_key,
        ];
    }


}