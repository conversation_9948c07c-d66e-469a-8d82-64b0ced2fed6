<?php

namespace App\Model\HttpModel\OpenAI;


use App\Exception\AppException;
use App\Utils\Helpers;
use Common\EnvConfig;
use Exception;

abstract class AbstractOpenAIModel
{

    /**
     * 请求的api URL
     *
     * @var string
     */
    protected $url;

    /**
     * api-key
     *
     * @var string
     */
    protected $api_key;


    /**
     * 构造函数
     * 子类在构造时会自动配置URL和API密钥
     */
    public function __construct()
    {
        $this->initializeConfig();
    }

    /**
     * 初始化配置 - 抽象方法，由子类实现
     * 子类需要在此方法中设置$url和$api_key
     *
     * @return void
     */
    abstract protected function initializeConfig(): void;


    /**
     * 获取请求头 - 抽象方法，由子类实现
     * 不同的API服务可能有不同的请求头格式
     *
     * @return array
     */
    abstract protected function getHeaders(): array;

    /**
     * 发送聊天请求
     *
     * @param array $content 内容数组
     * @return array
     * @throws AppException
     */
    public function chat(array $content)
    {
        return $this->makeRequest($content);
    }


    /**
     * 通用的HTTP请求方法
     *
     * @param array $data 请求数据
     * @param string $method HTTP方法
     * @return array
     * @throws AppException
     */
    protected function makeRequest(array $data, string $method = 'POST'): array
    {
        $url = $this->url;
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL            => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_CUSTOMREQUEST  => $method,
            CURLOPT_HTTPHEADER     => $this->getHeaders(),
            CURLOPT_POSTFIELDS     => json_encode($data),
            CURLOPT_TIMEOUT        => 10000,
            CURLOPT_SSL_VERIFYPEER => true,
            CURLOPT_CAINFO         => EnvConfig::CURL_CERT_FILE_PATH,
        ]);

        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            Helpers::getLogger("openai")->error("$url 遇到错误：$error", [
                'request_data' => $data
            ]);
            throw new AppException("CURL Error: " . $error);
        }

        if ($http_code !== 200) {
            Helpers::getLogger("openai")->error("$url 返回http error code：$http_code", [
                'request_data' => $data
            ]);
            throw new AppException("$url 发生HTTP Error: " . $http_code . " - " . $response);
        }

        $result = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            Helpers::getLogger("openai")->error("$url return invalid json format", [
                'request_data' => $data,
                'response_str' => $response,
            ]);
            throw new AppException("$url JSON Decode Error: " . json_last_error_msg());
        }

        if (isset($result['error'])) {
            Helpers::getLogger('openai')->error("$url return error code", [
                'request_data'  => $data,
                'response_data' => $result,
            ]);
            throw new AppException("openai 返回错误信息: {$result['error']['message']}");
        }

        return $result;
    }

    /**
     * 流式输出
     * @param array $content 请求体
     *
     */
    public function stream(array $content, $callback)
    {
        $api = $this->url;
        $data = json_encode($content, JSON_UNESCAPED_UNICODE);

        // 获取请求头
        $header = $this->getHeaders();

        $buffer = $reason_buffer = '';
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $api);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, false); // 不将响应保存为字符串，直接处理
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // 注意：在生产环境中应启用 SSL 验证
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false); // 注意：同上
        curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_WRITEFUNCTION, function ($ch, $data) use ($callback, &$buffer, &$reason_buffer) {


            // 拼接返回数据，方便后面存储
            // 按 \n\n 分割数据块
            $chunks = explode("\n\n", $data);
            foreach ($chunks as $chunk) {
                // 检查是否是有效的数据块
                if (strpos($chunk, 'data:') === 0) {
                    // 提取 JSON 数据
                    $jsonStr = substr($chunk, 5); // 去掉 "data:"
                    $result = json_decode($jsonStr, true);

                    // 提取 delta.content
                    $content = $reasoning_content = '';
                    if (!empty($result['choices'][0]['delta']['content'])) {
                        $content = $result['choices'][0]['delta']['content'];
                        $buffer .= $content; // 拼接内容
                    }

                    if (!empty($result['choices'][0]['delta']['reasoning_content'])) {
                        $reasoning_content = $result['choices'][0]['delta']['reasoning_content'];
                        $reason_buffer .= $reasoning_content; // 拼接内容
                    }
                    // 调用回调函数处理数据
                    $callback(['content' => $content, 'reasoning_content' => $reasoning_content]);
                }
            }

            return strlen($data); // 返回接收到的数据长度
        });

        // 执行请求并获取响应
        curl_exec($ch);

        // 检查是否有错误发生
        if (curl_errno($ch)) {
            Helpers::getLogger("DeepSeek")->error("$api", [
                'request_data' => $data,
                'message'      => curl_error($ch)
            ]);
            throw new AppException(curl_error($ch));
        }
        // 关闭 cURL 句柄
        curl_close($ch);

        return ['reason' => $reason_buffer, 'answer' => $buffer];
    }

    /**
     * 获取URL
     *
     * @return string
     */
    public function getUrl(): string
    {
        return $this->url;
    }

    /**
     * 获取API密钥
     *
     * @return string
     */
    public function getApiKey(): string
    {
        return $this->api_key;
    }

    /**
     * 设置URL
     *
     * @param string $url
     * @return void
     */
    public function setUrl(string $url): void
    {
        $this->url = $url;
    }

    /**
     * 设置API密钥
     *
     * @param string $apiKey
     * @return void
     */
    public function setApiKey(string $apiKey): void
    {
        $this->api_key = $apiKey;
    }
}